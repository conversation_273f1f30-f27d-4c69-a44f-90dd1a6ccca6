# IAMM 活動管理系統部署指南

## 系統概覽

IAMM (Interactive Activity Management Module) 是一個完整的活動管理平台，支援：

- 📝 用戶報名系統 (QR Code 生成)
- ✅ 現場簽到系統 (QR Code 掃描)
- 🎯 飛鏢遊戲系統 (Socket.IO 即時通訊)
- 📊 活動主辦方儀表板
- 📈 Grafana 即時監控

## 系統架構

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (Vite)   │    │  後端 (Express) │    │ 資料庫 (MySQL)  │
│                 │◄──►│                 │◄──►│                 │
│ - 報名頁面      │    │ - REST API      │    │ - Prisma ORM    │
│ - 簽到頁面      │    │ - Socket.IO     │    │ - 事件數據      │
│ - 遊戲頁面      │    │ - JWT 認證      │    │ - 用戶數據      │
│ - 儀表板        │    │ - Prometheus    │    │ - 遊戲數據      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │ Prometheus      │◄──►│ Grafana         │
                       │                 │    │                 │
                       │ - 數據收集      │    │ - 數據可視化    │
                       │ - 指標存儲      │    │ - 即時監控      │
                       └─────────────────┘    └─────────────────┘
```

## 前置需求

- Node.js 18+
- MySQL 8.0+
- Docker & Docker Compose (用於監控系統)
- npm 或 yarn

## 安裝步驟

### 1. 克隆專案並安裝依賴

```bash
# 安裝後端依賴
cd backend
npm install

# 安裝前端依賴
cd ../app
npm install
```

### 2. 資料庫設置

```bash
# 1. 創建 MySQL 資料庫
mysql -u root -p
CREATE DATABASE iamm_event;

# 2. 配置環境變數
cp backend/.env.example backend/.env

# 3. 編輯 .env 文件
DATABASE_URL="mysql://username:password@localhost:3306/iamm_event"
JWT_SECRET="your-jwt-secret-key"
```

### 3. 資料庫遷移

```bash
cd backend
npx prisma migrate dev --name init
npx prisma generate
```

### 4. 啟動應用程式

```bash
# 啟動後端 (開發模式)
cd backend
npm run dev

# 啟動前端 (開發模式)
cd app
npm run dev
```

### 5. 啟動監控系統 (可選)

```bash
# 啟動 Prometheus + Grafana
docker-compose -f docker-compose.monitoring.yml up -d

# 訪問 Grafana: http://localhost:3000
# 默認帳號: admin / admin123
```

## 功能使用指南

### 1. 活動報名流程

1. **創建展覽**
   - 登入管理後台
   - 創建新的展覽活動
   - 設置活動時間和狀態

2. **用戶報名**
   - 訪問: `/html/event/registration.html?exhibitionId=1`
   - 填寫報名資訊
   - 獲得 QR Code

3. **現場簽到**
   - 房間設備訪問: `/html/event/checkin.html?roomId=room-001&roomName=飛鏢遊戲區`
   - 掃描用戶 QR Code
   - 系統顯示歡迎訊息

### 2. 飛鏢遊戲流程

1. **進入遊戲房間**
   - 訪問: `/html/game/dart-game.html?roomId=dart-room-001`
   - 輸入報名 QR Code 加入遊戲

2. **遊戲進行**
   - 選擇投擲分數
   - 點擊投擲飛鏢
   - 即時更新分數和排行榜

3. **遊戲結束**
   - 點擊結束遊戲
   - 分數記錄到資料庫
   - 更新排行榜

### 3. 管理儀表板

1. **訪問儀表板**
   - 登入管理帳號
   - 訪問: `/html/dashboard/event-dashboard.html`

2. **監控功能**
   - 即時統計數據
   - 房間狀態監控
   - 最近活動記錄
   - 遊戲排行榜

### 4. Grafana 監控

1. **訪問 Grafana**
   - URL: `http://localhost:3000`
   - 帳號: `admin` / `admin123`

2. **查看儀表板**
   - 導航到 "IAMM 活動管理即時監控"
   - 查看即時數據和圖表

## API 端點

### 報名系統
- `POST /api/v1/registration` - 創建報名
- `GET /api/v1/registration/qr/:qrCode` - 通過 QR Code 獲取報名資訊
- `GET /api/v1/exhibition/:id/registrations` - 獲取展覽報名列表

### 簽到系統
- `POST /api/v1/checkin` - 處理簽到
- `GET /api/v1/room/:roomId/checkins` - 獲取房間簽到記錄

### 遊戲系統
- `POST /api/v1/game-score` - 記錄遊戲分數
- `GET /api/v1/room/:roomId/leaderboard` - 獲取房間排行榜

### 房間管理
- `POST /api/v1/game-room` - 創建遊戲房間
- `GET /api/v1/game-room/:id/stats` - 獲取房間統計

### 監控
- `GET /metrics` - Prometheus metrics

## Socket.IO 事件

### 客戶端發送
- `join-room` - 加入房間
- `start-game` - 開始遊戲
- `dart-throw` - 投擲飛鏢
- `end-game` - 結束遊戲

### 服務端發送
- `join-success` - 加入成功
- `game-started` - 遊戲開始
- `dart-thrown` - 飛鏢投擲結果
- `game-ended` - 遊戲結束

## 故障排除

### 常見問題

1. **資料庫連接失敗**
   ```bash
   # 檢查 MySQL 服務
   sudo systemctl status mysql
   
   # 檢查連接字串
   cat backend/.env | grep DATABASE_URL
   ```

2. **Socket.IO 連接失敗**
   ```bash
   # 檢查防火牆設置
   sudo ufw status
   
   # 檢查端口是否被佔用
   netstat -tulpn | grep 3061
   ```

3. **Grafana 無法訪問**
   ```bash
   # 檢查 Docker 容器狀態
   docker-compose -f docker-compose.monitoring.yml ps
   
   # 查看日誌
   docker-compose -f docker-compose.monitoring.yml logs grafana
   ```

### 日誌查看

```bash
# 應用程式日誌
tail -f backend/logs/app.log

# Grafana 日誌
docker logs iamm-grafana

# Prometheus 日誌
docker logs iamm-prometheus
```

## 生產環境部署

### 1. 環境配置

```bash
# 設置生產環境變數
NODE_ENV=production
DATABASE_URL="mysql://prod_user:prod_pass@prod_host:3306/iamm_prod"
JWT_SECRET="strong-production-secret"
```

### 2. 建置應用程式

```bash
# 建置前端
cd app
npm run build

# 建置後端
cd backend
npm run build
```

### 3. 使用 PM2 部署

```bash
# 安裝 PM2
npm install -g pm2

# 啟動應用程式
pm2 start ecosystem.config.js

# 設置開機自啟
pm2 startup
pm2 save
```

### 4. Nginx 反向代理

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3062;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }

    location /socket.io/ {
        proxy_pass http://localhost:3062;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 安全考量

1. **JWT 密鑰管理**
   - 使用強密鑰
   - 定期輪換
   - 環境變數存儲

2. **資料庫安全**
   - 使用專用用戶
   - 限制權限
   - 定期備份

3. **網路安全**
   - HTTPS 加密
   - 防火牆設置
   - 速率限制

## 維護和監控

1. **定期備份**
   ```bash
   # 資料庫備份
   mysqldump -u user -p iamm_event > backup_$(date +%Y%m%d).sql
   ```

2. **日誌輪轉**
   ```bash
   # 設置 logrotate
   sudo nano /etc/logrotate.d/iamm
   ```

3. **性能監控**
   - 使用 Grafana 監控系統指標
   - 設置告警規則
   - 定期檢查資源使用情況

## 支援和聯繫

如有問題或需要支援，請聯繫開發團隊或查看專案文檔。
