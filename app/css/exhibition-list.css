/* Exhibition List Page Styles */
label {
	color: var(--text-accent);
}

body {
	color: var(--text-primary);
}

.title-name {
	color: var(--text-accent);
	font-size: 1.5rem;
	font-weight: 600;
	margin-bottom: 1.5rem;
}

.top-title {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	gap: 0.5rem;
	margin-bottom: 1rem;
}

.list-title,
.memberlist-title {
	color: var(--text-primary);
	font-size: 1.25rem;
	font-weight: bold;
	margin-bottom: 1rem;
}

.projectName {
	position: relative;
	width: min(90vw, 400px);
	left: 50%;
	text-align: center;
	font-size: 2rem;
	font-weight: 600;
	color: var(--text-accent);
	transform: translate(-50%, 0);
	margin: 2rem 0;
	padding: 1rem;
	background-color: var(--bg-secondary);
	border-radius: 0.5rem;
	border: 1px solid var(--border-color);
}

.exhibition-list-maincontent {
	position: relative;
	padding: 2rem;
	width: min(90vw, 1200px);
	min-height: calc(100vh - 200px);
	left: 50%;
	transform: translate(-50%, 0);
	align-items: center;
	background-color: var(--bg-secondary);
	border-radius: 1rem;
	overflow-y: auto;
	margin-top: 2rem;
	box-shadow: 0 4px 12px var(--shadow);
	border: 1px solid var(--border-color);
}

#table {
	width: 100%;
}

::-webkit-scrollbar {
	display: none;
}

.groupContent {
	display: flex;
	/* 讓子元素並排 */
	gap: 40px;
	/* 兩個區塊之間的間距 */
}

.group-list {
	flex: 1;
	/* 讓兩個區塊平均分配空間 */
	min-width: 300px;
	/* 避免區塊過小 */
	background-color: #404040;
	border-radius: 20px;
	padding-top: 10px;
	padding-left: 50px;
	padding-right: 50px;
	padding-bottom: 10px;
}

.member-list {
	flex: 1;
	/* 讓兩個區塊平均分配空間 */
	min-width: 300px;
	/* 避免區塊過小 */
	background-color: #404040;
	border-radius: 20px;
	padding-top: 10px;
	padding-left: 50px;
	padding-right: 50px;
	padding-bottom: 10px;
}

#create_btn {
	background-color: transparent;
	width: 18px;
	height: 18px;
	color: white;
	border: none;
	/* 移除按鈕框線 */
	box-shadow: none;
	/* 移除按鈕陰影 */
	background-color: transparent;
	/* 如果你想讓按鈕背景也是透明的 */
	outline: none;
	/* 避免點擊時出現輪廓框 */
	background-image: url(../html/assets/add_blue.png);
	background-position: center center;
	background-size: 100%;
	background-repeat: no-repeat;
}

#delete_btn {
	background-color: transparent;
	width: 18px;
	height: 18px;
	color: white;
	border: none;
	/* 移除按鈕框線 */
	box-shadow: none;
	/* 移除按鈕陰影 */
	background-color: transparent;
	/* 如果你想讓按鈕背景也是透明的 */
	outline: none;
	/* 避免點擊時出現輪廓框 */
	background-image: url(../html/assets/bin_blue.png);
	background-position: center center;
	background-size: 100%;
	background-repeat: no-repeat;
}

#leave_btn {
	background-color: transparent;
	font-size: 20px;
	color: white;
	border: none;
	/* 移除按鈕框線 */
	box-shadow: none;
	/* 移除按鈕陰影 */
	background-color: transparent;
	/* 如果你想讓按鈕背景也是透明的 */
	outline: none;
	/* 避免點擊時出現輪廓框 */
}

.table {
	border-collapse: collapse;
	/* 讓邊框合併，避免多餘的線條 */
	border: none;
	/* 移除整個表格的邊框 */
}

.table th,
.table td {
	border: none;
	/* 移除表頭 (th) 和內容 (td) 的邊框 */
}

.btn-list {
	position: fixed;
	bottom: 50px;
	display: flex;
	left: 5vw;
	flex-direction: column;
	/* 讓子元素 (按鈕) 垂直排列 */
	gap: 20px;
	/* 按鈕之間的間距 */
	align-items: flex-start;
	/* 確保按鈕靠左對齊 (可選) */
}

.btn-list button {
	color: #6F8FF6;
	width: 50vw;
	height: 40px;
	font-size: 20px;
	text-align: left;
	border: none;
	/* 移除按鈕框線 */
	box-shadow: none;
	/* 移除按鈕陰影 */
	background-color: transparent;
	/* 如果你想讓按鈕背景也是透明的 */
	outline: none;
	/* 避免點擊時出現輪廓框 */
}

.thistime {
	background-color: #202020 !important;
	border-radius: 10px;
}