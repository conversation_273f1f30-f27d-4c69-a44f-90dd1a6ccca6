/* User Settings Page Styles */
label {
    color: var(--text-accent);
}

form {
    position: relative;
    font-size: 1rem;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    margin: 2rem auto;
    max-width: 600px;
    width: 90%;
    background-color: var(--bg-secondary);
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: 0 4px 12px var(--shadow);
    border: 1px solid var(--border-color);
}

#edit-form {
    top: 2rem;
}

.editbox {
    display: flex;
    gap: 2rem;
    align-items: flex-start;
    margin-bottom: 2rem;
}

.loginimg {
    position: relative;
    width: 150px;
    height: 150px;
    border-radius: 50%;
    border: 3px solid var(--border-color);
    transition: border-color 0.3s ease;
}

.formbox {
    flex: 1;
    min-width: 0;
}

.form-item {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background-color: var(--bg-tertiary);
    border-radius: 0.5rem;
    border: 1px solid var(--border-color);
    transition: background-color 0.2s ease;
}

.form-item:hover {
    background-color: var(--hover-bg);
}

.form-item label {
    width: 100px;
    text-align: left;
    margin-right: 1rem;
    color: var(--text-accent);
    font-weight: 600;
    margin-top: 0;
    margin-bottom: 0;
    flex-shrink: 0;
}

.form-item input {
    flex: 1;
    padding: 0.75rem;
    font-size: 1rem;
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    background-color: var(--input-bg);
    color: var(--text-primary);
    margin: 0;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-item input:focus {
    border-color: var(--text-accent);
    outline: none;
    box-shadow: 0 0 0 3px rgba(111, 143, 246, 0.1);
}

#submit {
    display: block;
    margin: 2rem auto 0;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    background-color: var(--button-primary);
    color: white;
    border: none;
    border-radius: 0.375rem;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s ease;
    min-width: 130px;
}

#submit:hover {
    background-color: var(--button-primary-hover);
}

#error_message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.5rem;
    padding: 0.5rem;
    background-color: rgba(220, 53, 69, 0.1);
    border-radius: 0.375rem;
    border: 1px solid rgba(220, 53, 69, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
    form {
        margin: 1rem;
        padding: 1.5rem;
        max-width: none;
        width: calc(100% - 2rem);
    }

    .editbox {
        flex-direction: column;
        align-items: center;
        gap: 1.5rem;
    }

    .loginimg {
        width: 120px;
        height: 120px;
    }

    .form-item {
        flex-direction: column;
        align-items: flex-start;
        text-align: left;
    }

    .form-item label {
        width: 100%;
        margin-right: 0;
        margin-bottom: 0.5rem;
    }

    .form-item input {
        width: 100%;
    }
}

@media (max-width: 480px) {
    form {
        padding: 1rem;
        border-radius: 0.5rem;
    }

    .form-item {
        padding: 0.75rem;
        margin-bottom: 1rem;
    }

    .loginimg {
        width: 100px;
        height: 100px;
    }

    #submit {
        padding: 0.625rem 1.25rem;
        font-size: 0.875rem;
    }
}