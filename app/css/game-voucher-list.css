label{
	color: white;
}
body{
	color: white;
}
.logo{
	color: #6F8FF6;
}
.title-name{
	color: #6F8FF6;
}
.list-title{
	color: #6F8FF6;
	font-size: 20px;
	font-weight: bold;
	margin-right: 20px;
}
.top-title{
	display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}
.memberlist-title{
	color: white;
	font-size: 20px;
	font-weight: bold;
}
.exhibitionName{
	position: relative;
	width: 20vw;
	left: 25vw;
	text-align: left;
	font-size: 30px;
	transform: translate(-50%, 0);
}
.game-voucher-list-maincontent{
	position: relative;
	padding-top: 10px;
	padding-left: 50px;
	padding-right: 50px;
	padding-bottom: 10px;
	width: 60vw;
	height: calc(100vh - 190px);
	left: 50vw;
	transform: translate(-50%, 0);
    align-items: center;
	background-color: #303030;
	border-radius: 20px;
	overflow-y: scroll;
}

#table{
	width: 100%;
}
::-webkit-scrollbar {
    display: none;
}
.groupContent {
    display: flex;  /* 讓子元素並排 */
    gap: 40px;  /* 兩個區塊之間的間距 */
}

.group-list {
    flex: 1;  /* 讓兩個區塊平均分配空間 */
    min-width: 300px;  /* 避免區塊過小 */
    background-color: #404040;
    border-radius: 20px;
    padding-top: 10px;
	padding-left: 50px;
	padding-right: 50px;
	padding-bottom: 10px;
}
.member-list {
    flex: 1;  /* 讓兩個區塊平均分配空間 */
    min-width: 300px;  /* 避免區塊過小 */
    background-color: #404040;
    border-radius: 20px;
    padding-top: 10px;
	padding-left: 50px;
	padding-right: 50px;
	padding-bottom: 10px;
}
#create_btn{
	background-color: transparent;
	font-size: 20px;
	color: white;
	border: none; /* 移除按鈕框線 */
    box-shadow: none; /* 移除按鈕陰影 */
    background-color: transparent; /* 如果你想讓按鈕背景也是透明的 */
    outline: none; /* 避免點擊時出現輪廓框 */
}
#delete_btn{
	background-color: transparent;
	font-size: 20px;
	color: white;
	border: none; /* 移除按鈕框線 */
    box-shadow: none; /* 移除按鈕陰影 */
    background-color: transparent; /* 如果你想讓按鈕背景也是透明的 */
    outline: none; /* 避免點擊時出現輪廓框 */
}
#leave_btn{
	background-color: transparent;
	font-size: 20px;
	color: white;
	border: none; /* 移除按鈕框線 */
    box-shadow: none; /* 移除按鈕陰影 */
    background-color: transparent; /* 如果你想讓按鈕背景也是透明的 */
    outline: none; /* 避免點擊時出現輪廓框 */
}

.table {
    border-collapse: collapse;  /* 讓邊框合併，避免多餘的線條 */
    border: none;  /* 移除整個表格的邊框 */
}

.table th, 
.table td {
    border: none;  /* 移除表頭 (th) 和內容 (td) 的邊框 */
}
.btn-list {
	position: fixed;
	bottom: 50px;
    display: flex;
    left: 5vw;
    flex-direction: column; /* 讓子元素 (按鈕) 垂直排列 */
    gap: 20px; /* 按鈕之間的間距 */
    align-items: flex-start; /* 確保按鈕靠左對齊 (可選) */
}
.btn-list button{
	color: #6F8FF6;
	width: 50vw;
	height: 40px;
	font-size: 20px;
	text-align: left;
    border: none; /* 移除按鈕框線 */
    box-shadow: none; /* 移除按鈕陰影 */
    background-color: transparent; /* 如果你想讓按鈕背景也是透明的 */
    outline: none; /* 避免點擊時出現輪廓框 */
}
.thistime{
	background-color: #303030!important;
	border-radius: 10px;
}