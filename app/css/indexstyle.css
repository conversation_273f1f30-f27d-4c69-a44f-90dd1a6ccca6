/* Login Page Styles */
label {
    color: var(--text-accent);
}

.loginTitle {
    color: var(--text-primary);
    text-align: center;
    font-family: var(--font-family);
    font-size: 1.5rem;
    font-weight: 600;
    margin: 1.5rem 0;
}

.loginBox {
    width: min(90vw, 400px);
    border-radius: 1rem;
    position: relative;
    transform: translate(-50%, 0);
    left: 50%;
    top: 8vh;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    box-shadow: 0 8px 32px var(--shadow);
    padding: 3rem 2rem;
    backdrop-filter: blur(10px);
}

.loginimg {
    position: relative;
    left: 50%;
    transform: translate(-50%, 0);
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: var(--bg-tertiary);
    padding: 1rem;
    margin-bottom: 1rem;
}

/* Form Styles */
form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

form div {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

input[type="text"],
input[type="password"] {
    padding: 0.875rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    background-color: var(--input-bg);
    color: var(--text-primary);
    font-size: 1rem;
    transition: all 0.2s ease;
    outline: none;
}

input[type="text"]:focus,
input[type="password"]:focus {
    border-color: var(--text-accent);
    box-shadow: 0 0 0 3px var(--focus-ring);
}

input[type="text"]::placeholder,
input[type="password"]::placeholder {
    color: var(--text-secondary);
}

#error_message {
    color: var(--error-color);
    font-size: 0.875rem;
    text-align: center;
    margin: 0.5rem 0;
}

.button-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-top: 2rem;
}

input[type="submit"],
input[type="button"] {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    font-weight: 500;
    background-color: var(--button-primary);
    color: white;
    border: 1px solid var(--button-primary);
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 120px;
    outline: none;
}

input[type="submit"]:hover,
input[type="button"]:hover {
    background-color: var(--button-primary-hover);
    border-color: var(--button-primary-hover);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px var(--shadow);
}

input[type="submit"]:active,
input[type="button"]:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px var(--shadow);
}

input[type="button"] {
    background-color: transparent;
    color: var(--text-accent);
    border-color: var(--text-accent);
}

input[type="button"]:hover {
    background-color: var(--text-accent);
    color: var(--bg-primary);
}

/* Responsive Design */
@media (max-width: 768px) {
    .loginBox {
        width: 95vw;
        padding: 2rem 1.5rem;
        top: 5vh;
    }

    .loginTitle {
        font-size: 1.25rem;
    }

    .button-container {
        flex-direction: column;
        gap: 0.75rem;
    }

    input[type="submit"],
    input[type="button"] {
        width: 100%;
        min-width: auto;
    }
}

@media (max-width: 480px) {
    .loginBox {
        width: 98vw;
        padding: 1.5rem 1rem;
        top: 3vh;
    }

    .loginimg {
        width: 60px;
        height: 60px;
    }

    input[type="text"],
    input[type="password"] {
        padding: 0.75rem;
        font-size: 0.875rem;
    }
}

/* Dark mode specific adjustments */
[data-theme="dark"] .loginimg {
    filter: brightness(0.9);
}

[data-theme="light"] .loginimg {
    filter: brightness(1.1);
}