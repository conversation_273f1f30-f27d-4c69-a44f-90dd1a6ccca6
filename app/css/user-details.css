/* User Details Page Styles */
label {
    color: var(--text-accent);
}

.loginimg {
    position: relative;
    width: 150px;
    height: 150px;
    border-radius: 50%;
    border: 3px solid var(--border-color);
    transition: border-color 0.3s ease;
}

.detailbox {
    display: flex;
    gap: 2rem;
    align-items: flex-start;
}

.databox {
    flex: 1;
    min-width: 0;
}

.details {
    position: relative;
    top: 2rem;
    font-size: 1rem;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    margin: 2rem auto;
    max-width: 600px;
    width: 90%;
    background-color: var(--bg-secondary);
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: 0 4px 12px var(--shadow);
    border: 1px solid var(--border-color);
}

.details-item {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background-color: var(--bg-tertiary);
    border-radius: 0.5rem;
    border: 1px solid var(--border-color);
    transition: background-color 0.2s ease;
}

.details-item:hover {
    background-color: var(--hover-bg);
}

.details-item label:first-child {
    width: 100px;
    text-align: left;
    margin-right: 1rem;
    color: var(--text-accent);
    font-weight: 600;
    margin-top: 0;
    margin-bottom: 0;
    flex-shrink: 0;
}

.details-content {
    flex: 1;
    padding: 0.5rem;
    font-size: 1rem;
    border-radius: 0.375rem;
    color: var(--text-primary);
    margin: 0;
    background-color: transparent;
    word-break: break-word;
}

/* Responsive Design */
@media (max-width: 768px) {
    .details {
        top: 1rem;
        margin: 1rem;
        padding: 1.5rem;
        max-width: none;
        width: calc(100% - 2rem);
    }

    .detailbox {
        flex-direction: column;
        align-items: center;
        gap: 1.5rem;
    }

    .loginimg {
        width: 120px;
        height: 120px;
    }

    .details-item {
        flex-direction: column;
        align-items: flex-start;
        text-align: left;
    }

    .details-item label:first-child {
        width: 100%;
        margin-right: 0;
        margin-bottom: 0.5rem;
    }

    .details-content {
        width: 100%;
    }
}

@media (max-width: 480px) {
    .details {
        padding: 1rem;
        border-radius: 0.5rem;
    }

    .details-item {
        padding: 0.75rem;
        margin-bottom: 1rem;
    }

    .loginimg {
        width: 100px;
        height: 100px;
    }
}