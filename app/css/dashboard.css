/* Dashboard Page Styles */
label {
    color: var(--text-accent);
}

body {
    color: var(--text-primary);
}

.projectName,
.exhibitionName {
    position: relative;
    width: min(90vw, 400px);
    left: 5vw;
    text-align: left;
    font-size: 2rem;
    font-weight: 600;
    color: var(--text-accent);
    margin: 2rem 0;
    padding: 1rem;
    background-color: var(--bg-secondary);
    border-radius: 0.5rem;
    border: 1px solid var(--border-color);
}

.maincontent {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    justify-items: center;
}

.mainbtn {
    width: min(200px, 15vw);
    height: min(200px, 15vw);
    border-radius: 1rem;
    border: 2px solid var(--border-color);
    color: var(--text-accent);
    background-color: var(--bg-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    box-shadow: 0 4px 12px var(--shadow);
    position: relative;
    overflow: hidden;
}

.mainbtn:hover {
    background-color: var(--hover-bg);
    transform: translateY(-4px);
    box-shadow: 0 8px 20px var(--shadow);
    border-color: var(--text-accent);
}

.mainbtn:active {
    transform: translateY(-2px);
}

/* Icon Buttons */
#detailsBtn,
#operationBtn,
#exhibitionBtn,
#monitoringBtn,
#statisticsBtn,
#backBtn,
#resourcesBtn,
#productListBtn,
#userListBtn,
#voucherListBtn,
#settingBtn {
    background-position: center center;
    background-size: 60%;
    background-repeat: no-repeat;
    color: transparent;
}

#detailsBtn {
    background-image: url(../html/assets/project_details.png);
}

#operationBtn {
    background-image: url(../html/assets/project_group.png);
}

#exhibitionBtn {
    background-image: url(../html/assets/project_exhibition.png);
}

#monitoringBtn {
    background-image: url(../html/assets/project_monitoring.png);
}

#statisticsBtn {
    background-image: url(../html/assets/project_statistics.png);
}

#backBtn {
    background-image: url(../html/assets/exhibition_return.png);
}

#resourcesBtn {
    background-image: url(../html/assets/exhibition_resources.png);
}

#productListBtn {
    background-image: url(../html/assets/exhibition_product.png);
}

#userListBtn {
    background-image: url(../html/assets/userlist.png);
}

#voucherListBtn {
    background-image: url(../html/assets/voucher.png);
}

#settingBtn {
    background-image: url(../html/assets/settings.png);
}

/* Responsive Design */
@media (max-width: 768px) {

    .projectName,
    .exhibitionName {
        font-size: 1.5rem;
        left: 2vw;
        width: 96vw;
    }

    .maincontent {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1.5rem;
        padding: 1.5rem;
    }

    .mainbtn {
        width: min(150px, 20vw);
        height: min(150px, 20vw);
        font-size: 0.875rem;
    }
}

@media (max-width: 480px) {

    .projectName,
    .exhibitionName {
        font-size: 1.25rem;
        padding: 0.75rem;
    }

    .maincontent {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
        padding: 1rem;
    }

    .mainbtn {
        width: min(120px, 25vw);
        height: min(120px, 25vw);
        font-size: 0.75rem;
    }
}