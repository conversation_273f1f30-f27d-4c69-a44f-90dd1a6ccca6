/* Project Monitoring Page Styles */
label {
	color: var(--text-accent);
}

body {
	color: var(--text-primary);
}

.title-name {
	color: var(--text-accent);
	font-size: 1.5rem;
	font-weight: 600;
	margin-bottom: 1.5rem;
}

.top-title {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	gap: 0.5rem;
	margin-bottom: 1rem;
}

.list-title,
.memberlist-title {
	color: var(--text-primary);
	font-size: 1.25rem;
	font-weight: bold;
	margin-bottom: 1rem;
}

.projectName {
	position: relative;
	width: min(90vw, 400px);
	left: 50%;
	text-align: center;
	font-size: 2rem;
	font-weight: 600;
	color: var(--text-accent);
	transform: translate(-50%, 0);
	margin: 2rem 0;
	padding: 1rem;
	background-color: var(--bg-secondary);
	border-radius: 0.5rem;
	border: 1px solid var(--border-color);
}

.project-monitoring-maincontent {
	position: relative;
	padding: 2rem;
	width: min(90vw, 1200px);
	min-height: calc(100vh - 200px);
	left: 50%;
	transform: translate(-50%, 0);
	align-items: center;
	background-color: var(--bg-secondary);
	border-radius: 1rem;
	overflow-y: auto;
	margin-top: 2rem;
	box-shadow: 0 4px 12px var(--shadow);
	border: 1px solid var(--border-color);
}


/* Monitoring Controls */
#export_data_btn {
	background-color: var(--button-primary);
	color: white;
	border: none;
	padding: 0.5rem 1rem;
	border-radius: 0.375rem;
	cursor: pointer;
	font-size: 0.875rem;
	font-weight: 500;
	transition: background-color 0.2s ease;
	margin-bottom: 1rem;
}

#export_data_btn:hover {
	background-color: var(--button-primary-hover);
}

#connect-amount {
	color: var(--text-accent);
	font-weight: 600;
	font-size: 1.125rem;
}

/* Table Styles */
#displayers-table {
	width: 100%;
	background-color: var(--bg-secondary);
	border-radius: 0.5rem;
	overflow: hidden;
	box-shadow: 0 2px 8px var(--shadow);
	color: var(--text-primary);
}

#displayers-table th,
#displayers-table td {
	padding: 1rem;
	text-align: left;
	border-bottom: 1px solid var(--border-color);
	color: var(--text-primary);
}

#displayers-table th {
	background-color: var(--bg-tertiary);
	font-weight: 600;
	color: var(--text-accent);
}

::-webkit-scrollbar {
	width: 8px;
}

::-webkit-scrollbar-track {
	background: var(--bg-tertiary);
	border-radius: 4px;
}

::-webkit-scrollbar-thumb {
	background: var(--border-color);
	border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
	background: var(--text-accent);
}

.groupContent {
	display: flex;
	/* 讓子元素並排 */
	gap: 40px;
	/* 兩個區塊之間的間距 */
}

.group-list {
	flex: 1;
	/* 讓兩個區塊平均分配空間 */
	min-width: 300px;
	/* 避免區塊過小 */
	background-color: #404040;
	border-radius: 20px;
	padding-top: 10px;
	padding-left: 50px;
	padding-right: 50px;
	padding-bottom: 10px;
}

.member-list {
	flex: 1;
	/* 讓兩個區塊平均分配空間 */
	min-width: 300px;
	/* 避免區塊過小 */
	background-color: #404040;
	border-radius: 20px;
	padding-top: 10px;
	padding-left: 50px;
	padding-right: 50px;
	padding-bottom: 10px;
}

#create_btn {
	background-color: transparent;
	font-size: 20px;
	color: white;
	border: none;
	/* 移除按鈕框線 */
	box-shadow: none;
	/* 移除按鈕陰影 */
	background-color: transparent;
	/* 如果你想讓按鈕背景也是透明的 */
	outline: none;
	/* 避免點擊時出現輪廓框 */
}

#delete_btn {
	background-color: transparent;
	font-size: 20px;
	color: white;
	border: none;
	/* 移除按鈕框線 */
	box-shadow: none;
	/* 移除按鈕陰影 */
	background-color: transparent;
	/* 如果你想讓按鈕背景也是透明的 */
	outline: none;
	/* 避免點擊時出現輪廓框 */
}

#leave_btn {
	background-color: transparent;
	font-size: 20px;
	color: white;
	border: none;
	/* 移除按鈕框線 */
	box-shadow: none;
	/* 移除按鈕陰影 */
	background-color: transparent;
	/* 如果你想讓按鈕背景也是透明的 */
	outline: none;
	/* 避免點擊時出現輪廓框 */
}

.table {
	border-collapse: collapse;
	/* 讓邊框合併，避免多餘的線條 */
	border: none;
	/* 移除整個表格的邊框 */
}

.table th,
.table td {
	border: none;
	/* 移除表頭 (th) 和內容 (td) 的邊框 */
}

/* Side Navigation */
.btn-list {
	position: fixed;
	bottom: 3rem;
	display: flex;
	left: 5vw;
	flex-direction: column;
	gap: 1.25rem;
	align-items: flex-start;
}

.btn-list button {
	color: var(--text-accent);
	width: 50vw;
	height: 2.5rem;
	font-size: 1.125rem;
	text-align: left;
	border: none;
	box-shadow: none;
	background-color: transparent;
	outline: none;
	cursor: pointer;
	transition: all 0.2s ease;
	padding: 0.5rem;
	border-radius: 0.375rem;
}

.btn-list button:hover {
	background-color: var(--hover-bg);
	color: var(--text-primary);
}

.thistime {
	background-color: var(--bg-secondary) !important;
	border: 1px solid var(--text-accent);
	color: var(--text-accent) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
	.project-monitoring-maincontent {
		width: 95vw;
		padding: 1.5rem;
		margin-top: 1rem;
	}

	.projectName {
		font-size: 1.5rem;
		width: 95vw;
	}

	.btn-list {
		left: 2vw;
	}

	.btn-list button {
		width: 60vw;
		font-size: 1rem;
	}

	#displayers-table th,
	#displayers-table td {
		padding: 0.5rem;
		font-size: 0.875rem;
	}
}

@media (max-width: 480px) {
	.project-monitoring-maincontent {
		width: 98vw;
		padding: 1rem;
	}

	.projectName {
		font-size: 1.25rem;
	}

	#displayers-table th,
	#displayers-table td {
		padding: 0.375rem;
		font-size: 0.75rem;
	}
}