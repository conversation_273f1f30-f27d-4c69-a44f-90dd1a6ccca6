/* Project List Specific Styles */
label {
	color: var(--text-primary);
}

.title {
	color: var(--text-accent);
}

.list-title {
	color: var(--text-accent);
	font-size: 1.25rem;
	font-weight: bold;
}

.top-title {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	gap: 0.5rem;
	margin-bottom: 1rem;
}

.project-list-place {
	position: relative;
	padding: 2rem;
	width: min(90vw, 1200px);
	left: 50vw;
	transform: translate(-50%, 0);
	align-items: center;
	background-color: var(--bg-secondary);
	border-radius: 1rem;
	margin-top: 2rem;
	box-shadow: 0 4px 12px var(--shadow);
	border: 1px solid var(--border-color);
}

#projects-table {
	width: 100%;
	color: var(--text-primary);
	padding-top: 1rem;
	cursor: pointer;
}

/* Table row hover effects and click hints */
#projects-table tbody tr {
	cursor: pointer;
	transition: all 0.2s ease;
	position: relative;
}

#projects-table tbody tr:hover {
	background-color: var(--hover-bg) !important;
	transform: translateY(-1px);
	box-shadow: 0 2px 8px var(--shadow);
}

#projects-table tbody tr:hover::after {
	content: "雙擊進入項目 / Double-click to enter project";
	position: absolute;
	right: 10px;
	top: 50%;
	transform: translateY(-50%);
	background-color: var(--text-accent);
	color: white;
	padding: 0.25rem 0.5rem;
	border-radius: 0.25rem;
	font-size: 0.75rem;
	white-space: nowrap;
	z-index: 10;
	opacity: 0.9;
	pointer-events: none;
}

#projects-table tbody tr:active {
	transform: translateY(0);
	background-color: var(--text-accent) !important;
	color: white !important;
}

#create_project_btn {
	background-color: transparent;
	width: 24px;
	height: 24px;
	color: var(--text-accent);
	border: none;
	box-shadow: none;
	outline: none;
	background-image: url(../html/assets/add_blue.png);
	background-position: center center;
	background-size: 100%;
	background-repeat: no-repeat;
	cursor: pointer;
	transition: all 0.2s ease;
	border-radius: 0.25rem;
	padding: 0.25rem;
}

#create_project_btn:hover {
	background-color: var(--hover-bg);
	transform: scale(1.1);
}

#delete_project_btn {
	background-color: transparent;
	width: 24px;
	height: 24px;
	color: var(--text-accent);
	border: none;
	box-shadow: none;
	outline: none;
	background-image: url(../html/assets/bin_blue.png);
	background-position: center center;
	background-size: 100%;
	background-repeat: no-repeat;
	cursor: pointer;
	transition: all 0.2s ease;
	border-radius: 0.25rem;
	padding: 0.25rem;
}

#delete_project_btn:hover {
	background-color: var(--hover-bg);
	transform: scale(1.1);
}

#join_group_btn {
	background-color: transparent;
	font-size: 1.125rem;
	color: var(--text-accent);
	border: 1px solid var(--text-accent);
	box-shadow: none;
	outline: none;
	padding: 0.5rem 1rem;
	border-radius: 0.375rem;
	cursor: pointer;
	transition: all 0.2s ease;
	font-weight: 500;
}

#join_group_btn:hover {
	background-color: var(--text-accent);
	color: var(--bg-primary);
}

/* Table hint styling */
.table-hint {
	display: flex;
	align-items: center;
	gap: 0.5rem;
	margin: 1rem 0;
	padding: 0.75rem 1rem;
	background-color: var(--bg-tertiary);
	border: 1px solid var(--text-accent);
	border-radius: 0.5rem;
	color: var(--text-primary);
	font-size: 0.875rem;
	animation: fadeInPulse 2s ease-in-out;
}

.hint-icon {
	font-size: 1.2rem;
	animation: bounce 2s infinite;
}

@keyframes fadeInPulse {
	0% {
		opacity: 0;
		transform: translateY(-10px);
	}

	50% {
		opacity: 1;
		transform: translateY(0);
	}

	100% {
		opacity: 1;
	}
}

@keyframes bounce {

	0%,
	20%,
	50%,
	80%,
	100% {
		transform: translateY(0);
	}

	40% {
		transform: translateY(-5px);
	}

	60% {
		transform: translateY(-3px);
	}
}

/* Responsive Design */
@media (max-width: 768px) {
	.header-content {
		padding: 1rem;
		flex-direction: column;
		gap: 1rem;
	}

	.header-nav {
		flex-wrap: wrap;
		justify-content: center;
		gap: 0.5rem;
	}

	.project-list-place {
		width: 95vw;
		padding: 1rem;
		margin-top: 1rem;
	}

	.top-title {
		flex-direction: column;
		align-items: flex-start;
		gap: 1rem;
	}

	#projects-table {
		font-size: 0.875rem;
	}

	th,
	td {
		padding: 0.5rem;
	}

	.table-hint {
		font-size: 0.75rem;
		padding: 0.5rem 0.75rem;
	}

	#projects-table tbody tr:hover::after {
		content: "雙擊進入 / Double-click";
		font-size: 0.625rem;
		padding: 0.2rem 0.4rem;
	}

	.title {
		font-size: 1.5rem;
	}

	.nav-link {
		padding: 0.375rem 0.75rem;
		font-size: 0.875rem;
	}

	.dropdown-menu {
		position: fixed;
		top: auto;
		bottom: 0;
		left: 0;
		right: 0;
		border-radius: 1rem 1rem 0 0;
		min-width: auto;
	}
}

@media (max-width: 480px) {
	.project-list-place {
		width: 98vw;
		padding: 0.75rem;
	}

	.title {
		font-size: 1.25rem;
	}

	.list-title {
		font-size: 1rem;
	}

	#projects-table {
		font-size: 0.75rem;
	}

	th,
	td {
		padding: 0.375rem;
	}

	.header-nav {
		gap: 0.25rem;
	}

	.nav-link {
		padding: 0.25rem 0.5rem;
		font-size: 0.75rem;
	}

	.table-hint {
		font-size: 0.7rem;
		padding: 0.4rem 0.6rem;
		flex-direction: column;
		text-align: center;
		gap: 0.25rem;
	}

	#projects-table tbody tr:hover::after {
		content: "雙擊";
		font-size: 0.6rem;
		padding: 0.15rem 0.3rem;
		right: 5px;
	}
}