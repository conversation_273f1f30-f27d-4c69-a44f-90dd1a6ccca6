/* Create Project Page Styles */
label {
	color: var(--text-accent);
}

.btn-list {
	position: fixed;
	bottom: 3rem;
	display: flex;
	left: 5vw;
	flex-direction: column;
	gap: 1.25rem;
	align-items: flex-start;
}

.btn-list button {
	color: var(--text-accent);
	width: 50vw;
	height: 2.5rem;
	font-size: 1.25rem;
	text-align: left;
	border: none;
	box-shadow: none;
	background-color: transparent;
	outline: none;
	cursor: pointer;
	transition: all 0.2s ease;
	padding: 0.5rem;
	border-radius: 0.375rem;
}

.btn-list button:hover {
	background-color: var(--hover-bg);
	color: var(--text-primary);
}

.maincontent {
	position: relative;
	padding: 2rem;
	width: min(90vw, 800px);
	min-height: calc(100vh - 200px);
	left: 50vw;
	transform: translate(-50%, 0);
	align-items: center;
	background-color: var(--bg-secondary);
	border-radius: 1rem;
	margin-top: 2rem;
	box-shadow: 0 4px 12px var(--shadow);
	border: 1px solid var(--border-color);
}

/* Form Styles */
#project_form {
	display: flex;
	flex-direction: column;
	gap: 1rem;
}

#project_form label {
	font-weight: 600;
	margin-bottom: 0.5rem;
}

#project_form input[type="text"] {
	padding: 0.75rem;
	border: 1px solid var(--border-color);
	border-radius: 0.375rem;
	background-color: var(--input-bg);
	color: var(--text-primary);
	font-size: 1rem;
	transition: border-color 0.2s ease;
}

#project_form input[type="text"]:focus {
	border-color: var(--text-accent);
	outline: none;
	box-shadow: 0 0 0 3px rgba(111, 143, 246, 0.1);
}

#project_form input[type="submit"] {
	background-color: var(--button-primary);
	color: white;
	border: none;
	padding: 0.75rem 1.5rem;
	border-radius: 0.375rem;
	font-size: 1rem;
	font-weight: 500;
	cursor: pointer;
	transition: background-color 0.2s ease;
	margin-top: 1rem;
}

#project_form input[type="submit"]:hover {
	background-color: var(--button-primary-hover);
}

#error_message {
	color: #dc3545;
	font-size: 0.875rem;
	margin-top: 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
	.maincontent {
		width: 95vw;
		padding: 1.5rem;
		margin-top: 1rem;
	}

	.btn-list {
		left: 2vw;
	}

	.btn-list button {
		width: 60vw;
		font-size: 1rem;
	}
}

@media (max-width: 480px) {
	.maincontent {
		width: 98vw;
		padding: 1rem;
	}

	#project_form input[type="text"],
	#project_form input[type="submit"] {
		font-size: 0.875rem;
		padding: 0.625rem;
	}
}