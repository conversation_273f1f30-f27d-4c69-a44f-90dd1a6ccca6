FROM node:24-alpine

# 安裝必要的系統依賴
RUN apk add --no-cache curl

# 設置工作目錄
WORKDIR /app

# 複製 package.json 文件
COPY app/package*.json ./frontend/

# 安裝依賴
RUN cd frontend && npm install

# 複製源代碼
COPY app ./frontend/

# 暴露端口
EXPOSE 5173

# 健康檢查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:5173 || exit 1

# 啟動命令
CMD ["npm", "run", "dev", "--", "--host", "0.0.0.0"]
