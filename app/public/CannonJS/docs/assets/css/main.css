/*
Font sizes for all selectors other than the body are given in percentages,
with 100% equal to 13px. To calculate a font size percentage, multiply the
desired size in pixels by 7.6923076923.

Here's a quick lookup table:

10px - 76.923%
11px - 84.615%
12px - 92.308%
13px - 100%
14px - 107.692%
15px - 115.385%
16px - 123.077%
17px - 130.769%
18px - 138.462%
19px - 146.154%
20px - 153.846%
*/

html {
    background: #fff;
    color: #333;
    overflow-y: scroll;
}

body {
    /*font: 13px/1.4 'Lucida Grande', 'Lucida Sans Unicode', 'DejaVu Sans', 'Bitstream Vera Sans', 'Helvetica', 'Arial', sans-serif;*/
    font: 13px/1.4 'Helvetica', 'Arial', sans-serif;
    margin: 0;
    padding: 0;
}

/* -- Links ----------------------------------------------------------------- */
a {
    color: #356de4;
    text-decoration: none;
}

.hidden {
    display: none;
}

a:hover { text-decoration: underline; }

/* "Jump to Table of Contents" link is shown to assistive tools, but hidden from
   sight until it's focused. */
.jump {
    position: absolute;
    padding: 3px 6px;
    left: -99999px;
    top: 0;
}

.jump:focus { left: 40%; }

/* -- Paragraphs ------------------------------------------------------------ */
p { margin: 1.3em 0; }
dd p, td p { margin-bottom: 0; }
dd p:first-child, td p:first-child { margin-top: 0; }

/* -- Headings -------------------------------------------------------------- */
h1, h2, h3, h4, h5, h6 {
    color: #D98527;/*was #f80*/
    font-family: 'Trebuchet MS', sans-serif;
    font-weight: bold;
    line-height: 1.1;
    margin: 1.1em 0 0.5em;
}

h1 {
    font-size: 184.6%;
    color: #30418C;
    margin: 0.75em 0 0.5em;
}

h2 {
    font-size: 153.846%;
    color: #E48A2B;
}

h3 { font-size: 138.462%; }

h4 {
    border-bottom: 1px solid #DBDFEA;
    color: #E48A2B;
    font-size: 115.385%;
    font-weight: normal;
    padding-bottom: 2px;
}

h5, h6 { font-size: 107.692%; }

/* -- Code and examples ----------------------------------------------------- */
code, kbd, pre, samp {
    font-family: Menlo, Monaco, 'DejaVu Sans Mono', 'Bitstream Vera Sans Mono', 'Courier New', Courier, monospace;
    font-size: 92.308%;
    line-height: 1.35;
}

p code, p kbd, p samp, li code {
    background: #FCFBFA;
    border: 1px solid #EFEEED;
    padding: 0 3px;
}

a code, a kbd, a samp,
pre code, pre kbd, pre samp,
table code, table kbd, table samp,
.intro code, .intro kbd, .intro samp,
.toc code, .toc kbd, .toc samp {
    background: none;
    border: none;
    padding: 0;
}

pre.code, pre.terminal, pre.cmd {
    overflow-x: auto;
    *overflow-x: scroll;
    padding: 0.3em 0.6em;
}

pre.code {
    background: #FCFBFA;
    border: 1px solid #EFEEED;
    border-left-width: 5px;
}

pre.terminal, pre.cmd {
    background: #F0EFFC;
    border: 1px solid #D0CBFB;
    border-left: 5px solid #D0CBFB;
}

/* Don't reduce the font size of <code>/<kbd>/<samp> elements inside <pre>
   blocks. */
pre code, pre kbd, pre samp { font-size: 100%; }

/* Used to denote text that shouldn't be selectable, such as line numbers or
   shell prompts. Guess which browser this doesn't work in. */
.noselect {
    -moz-user-select: -moz-none;
    -khtml-user-select: none;
    -webkit-user-select: none;
    -o-user-select: none;
    user-select: none;
}

/* -- Lists ----------------------------------------------------------------- */
dd { margin: 0.2em 0 0.7em 1em; }
dl { margin: 1em 0; }
dt { font-weight: bold; }

/* -- Tables ---------------------------------------------------------------- */
caption, th { text-align: left; }

table {
    border-collapse: collapse;
    width: 100%;
}

td, th {
    border: 1px solid #fff;
    padding: 5px 12px;
    vertical-align: top;
}

td { background: #E6E9F5; }
td dl { margin: 0; }
td dl dl { margin: 1em 0; }
td pre:first-child { margin-top: 0; }

th {
    background: #D2D7E6;/*#97A0BF*/
    border-bottom: none;
    border-top: none;
    color: #000;/*#FFF1D5*/
    font-family: 'Trebuchet MS', sans-serif;
    font-weight: bold;
    line-height: 1.3;
    white-space: nowrap;
}


/* -- Layout and Content ---------------------------------------------------- */
#doc {
    margin: auto;
    min-width: 1024px;
}

.content { padding: 0 20px 0 25px; }

.sidebar {
    padding: 0 15px 0 10px;
}
#bd {
    padding: 7px 0 130px;
    position: relative;
    width: 99%;
}

/* -- Table of Contents ----------------------------------------------------- */

/* The #toc id refers to the single global table of contents, while the .toc
   class refers to generic TOC lists that could be used throughout the page. */

.toc code, .toc kbd, .toc samp { font-size: 100%; }
.toc li { font-weight: bold; }
.toc li li { font-weight: normal; }

/* -- Intro and Example Boxes ----------------------------------------------- */
/*
.intro, .example { margin-bottom: 2em; }
.example {
    -moz-border-radius: 4px;
    -webkit-border-radius: 4px;
    border-radius: 4px;
    -moz-box-shadow: 0 0 5px #bfbfbf;
    -webkit-box-shadow: 0 0 5px #bfbfbf;
    box-shadow: 0 0 5px #bfbfbf;
    padding: 1em;
}
.intro {
    background: none repeat scroll 0 0 #F0F1F8; border: 1px solid #D4D8EB; padding: 0 1em;
}
*/

/* -- Other Styles ---------------------------------------------------------- */

/* These are probably YUI-specific, and should be moved out of Selleck's default
   theme. */

.button {
    border: 1px solid #dadada;
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px;
    border-radius: 3px;
    color: #444;
    display: inline-block;
    font-family: Helvetica, Arial, sans-serif;
    font-size: 92.308%;
    font-weight: bold;
    padding: 4px 13px 3px;
    -moz-text-shadow: 1px 1px 0 #fff;
    -webkit-text-shadow: 1px 1px 0 #fff;
    text-shadow: 1px 1px 0 #fff;
    white-space: nowrap;

    background: #EFEFEF; /* old browsers */
    background: -moz-linear-gradient(top, #f5f5f5 0%, #efefef 50%, #e5e5e5 51%, #dfdfdf 100%); /* firefox */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#f5f5f5), color-stop(50%,#efefef), color-stop(51%,#e5e5e5), color-stop(100%,#dfdfdf)); /* webkit */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#f5f5f5', endColorstr='#dfdfdf',GradientType=0 ); /* ie */
}

.button:hover {
    border-color: #466899;
    color: #fff;
    text-decoration: none;
    -moz-text-shadow: 1px 1px 0 #222;
    -webkit-text-shadow: 1px 1px 0 #222;
    text-shadow: 1px 1px 0 #222;

    background: #6396D8; /* old browsers */
    background: -moz-linear-gradient(top, #6396D8 0%, #5A83BC 50%, #547AB7 51%, #466899 100%); /* firefox */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#6396D8), color-stop(50%,#5A83BC), color-stop(51%,#547AB7), color-stop(100%,#466899)); /* webkit */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#6396D8', endColorstr='#466899',GradientType=0 ); /* ie */
}

.newwindow { text-align: center; }

.header .version em {
    display: block;
    text-align: right;
}


#classdocs .item {
    border-bottom: 1px solid #466899;
    margin: 1em 0;
    padding: 1.5em;
}

#classdocs .item .params p,
    #classdocs .item .returns p,{
    display: inline;
}

#classdocs .item em code, #classdocs .item em.comment {
    color: green;
}

#classdocs .item em.comment a {
    color: green;
    text-decoration: underline;
}

#classdocs .foundat {
    font-size: 11px;
    font-style: normal;
}

.attrs .emits {
    margin-left: 2em;
    padding: .5em;
    border-left: 1px dashed #ccc;
}

abbr {
    border-bottom: 1px dashed #ccc;
    font-size: 80%;
    cursor: help;
}

.prettyprint li.L0, 
.prettyprint li.L1, 
.prettyprint li.L2, 
.prettyprint li.L3, 
.prettyprint li.L5, 
.prettyprint li.L6, 
.prettyprint li.L7, 
.prettyprint li.L8 {
    list-style: decimal;
}

ul li p {
    margin-top: 0;
}

.method .name {
    font-size: 110%;
}

.apidocs .methods .extends .method,
.apidocs .properties .extends .property,
.apidocs .attrs .extends .attr,
.apidocs .events .extends .event {
    font-weight: bold;
}

.apidocs .methods .extends .inherited,
.apidocs .properties .extends .inherited,
.apidocs .attrs .extends .inherited,
.apidocs .events .extends .inherited {
    font-weight: normal;
}

#hd {
    background: whiteSmoke;
    background: -moz-linear-gradient(top,#DCDBD9 0,#F6F5F3 100%);
    background: -webkit-gradient(linear,left top,left bottom,color-stop(0%,#DCDBD9),color-stop(100%,#F6F5F3));
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#dcdbd9',endColorstr='#F6F5F3',GradientType=0);
    border-bottom: 1px solid #DFDFDF;
    padding: 0 15px 1px 20px;
    margin-bottom: 15px;
}

#hd img {
    margin-right: 10px;
    vertical-align: middle;
}


/* -- API Docs CSS ---------------------------------------------------------- */

/*
This file is organized so that more generic styles are nearer the top, and more
specific styles are nearer the bottom of the file. This allows us to take full
advantage of the cascade to avoid redundant style rules. Please respect this
convention when making changes.
*/

/* -- Generic TabView styles ------------------------------------------------ */

/*
These styles apply to all API doc tabviews. To change styles only for a
specific tabview, see the other sections below.
*/

.yui3-js-enabled .apidocs .tabview {
    visibility: hidden; /* Hide until the TabView finishes rendering. */
    _visibility: visible;
}

.apidocs .tabview.yui3-tabview-content { visibility: visible; }
.apidocs .tabview .yui3-tabview-panel { background: #fff; }

/* -- Generic Content Styles ------------------------------------------------ */

/* Headings */
h2, h3, h4, h5, h6 {
    border: none;
    color: #30418C;
    font-weight: bold;
    text-decoration: none;
}

.link-docs {
    float: right;
    font-size: 15px;
    margin: 4px 4px 6px;
    padding: 6px 30px 5px;
}

.apidocs { zoom: 1; }

/* Generic box styles. */
.apidocs .box {
    border: 1px solid;
    border-radius: 3px;
    margin: 1em 0;
    padding: 0 1em;
}

/* A flag is a compact, capsule-like indicator of some kind. It's used to
   indicate private and protected items, item return types, etc. in an
   attractive and unobtrusive way. */
.apidocs .flag {
    background: #bababa;
    border-radius: 3px;
    color: #fff;
    font-size: 11px;
    margin: 0 0.5em;
    padding: 2px 4px 1px;
}

/* Class/module metadata such as "Uses", "Extends", "Defined in", etc. */
.apidocs .meta {
    background: #f9f9f9;
    border-color: #efefef;
    color: #555;
    font-size: 11px;
    padding: 3px 6px;
}

.apidocs .meta p { margin: 0; }

/* Deprecation warning. */
.apidocs .box.deprecated,
.apidocs .flag.deprecated {
    background: #fdac9f;
    border: 1px solid #fd7775;
}

.apidocs .box.deprecated p { margin: 0.5em 0; }
.apidocs .flag.deprecated { color: #333; }

/* Module/Class intro description. */
.apidocs .intro {
    background: #f0f1f8;
    border-color: #d4d8eb;
}

/* Loading spinners. */
#bd.loading .apidocs,
#api-list.loading .yui3-tabview-panel {
    background: #fff url(../img/spinner.gif) no-repeat center 70px;
    min-height: 150px;
}

#bd.loading .apidocs .content,
#api-list.loading .yui3-tabview-panel .apis {
    display: none;
}

.apidocs .no-visible-items { color: #666; }

/* Generic inline list. */
.apidocs ul.inline {
    display: inline;
    list-style: none;
    margin: 0;
    padding: 0;
}

.apidocs ul.inline li { display: inline; }

/* Comma-separated list. */
.apidocs ul.commas li:after { content: ','; }
.apidocs ul.commas li:last-child:after { content: ''; }

/* Keyboard shortcuts. */
kbd .cmd { font-family: Monaco, Helvetica; }

/* -- Generic Access Level styles ------------------------------------------- */
.apidocs .item.protected,
.apidocs .item.private,
.apidocs .index-item.protected,
.apidocs .index-item.deprecated,
.apidocs .index-item.private {
    display: none;
}

.show-deprecated .item.deprecated,
.show-deprecated .index-item.deprecated,
.show-protected .item.protected,
.show-protected .index-item.protected,
.show-private .item.private,
.show-private .index-item.private {
    display: block;
}

.hide-inherited .item.inherited,
.hide-inherited .index-item.inherited {
    display: none;
}

/* -- Generic Item Index styles --------------------------------------------- */
.apidocs .index { margin: 1.5em 0 3em; }

.apidocs .index h3 {
    border-bottom: 1px solid #efefef;
    color: #333;
    font-size: 13px;
    margin: 2em 0 0.6em;
    padding-bottom: 2px;
}

.apidocs .index .no-visible-items { margin-top: 2em; }

.apidocs .index-list {
    border-color: #efefef;
    font-size: 12px;
    list-style: none;
    margin: 0;
    padding: 0;
    -moz-column-count: 4;
    -moz-column-gap: 10px;
    -moz-column-width: 170px;
    -ms-column-count: 4;
    -ms-column-gap: 10px;
    -ms-column-width: 170px;
    -o-column-count: 4;
    -o-column-gap: 10px;
    -o-column-width: 170px;
    -webkit-column-count: 4;
    -webkit-column-gap: 10px;
    -webkit-column-width: 170px;
    column-count: 4;
    column-gap: 10px;
    column-width: 170px;
}

.apidocs .no-columns .index-list {
    -moz-column-count: 1;
    -ms-column-count: 1;
    -o-column-count: 1;
    -webkit-column-count: 1;
    column-count: 1;
}

.apidocs .index-item { white-space: nowrap; }

.apidocs .index-item .flag {
    background: none;
    border: none;
    color: #afafaf;
    display: inline;
    margin: 0 0 0 0.2em;
    padding: 0;
}

/* -- Generic API item styles ----------------------------------------------- */
.apidocs .args {
    display: inline;
    margin: 0 0.5em;
}

.apidocs .flag.chainable { background: #46ca3b; }
.apidocs .flag.protected { background: #9b86fc; }
.apidocs .flag.private { background: #fd6b1b; }
.apidocs .flag.async { background: #356de4; }
.apidocs .flag.required { background: #e60923; }

.apidocs .item {
    border-bottom: 1px solid #efefef;
    margin: 1.5em 0 2em;
    padding-bottom: 2em;
}

.apidocs .item h4,
.apidocs .item h5,
.apidocs .item h6 {
    color: #333;
    font-family: inherit;
    font-size: 100%;
}

.apidocs .item .description p,
.apidocs .item pre.code {
    margin: 1em 0 0;
}

.apidocs .item .meta {
    background: none;
    border: none;
    padding: 0;
}

.apidocs .item .name {
    display: inline;
    font-size: 14px;
}

.apidocs .item .type,
.apidocs .item .type a,
.apidocs .returns-inline {
    color: #555;
}

.apidocs .item .type,
.apidocs .returns-inline {
    font-size: 11px;
    margin: 0 0 0 0;
}

.apidocs .item .type a { border-bottom: 1px dotted #afafaf; }
.apidocs .item .type a:hover { border: none; }

/* -- Item Parameter List --------------------------------------------------- */
.apidocs .params-list {
    list-style: square;
    margin: 1em 0 0 2em;
    padding: 0;
}

.apidocs .param { margin-bottom: 1em; }

.apidocs .param .type,
.apidocs .param .type a {
    color: #666;
}

.apidocs .param .type {
    margin: 0 0 0 0.5em;
    *margin-left: 0.5em;
}

.apidocs .param-name { font-weight: bold; }

/* -- Item "Emits" block ---------------------------------------------------- */
.apidocs .item .emits {
    background: #f9f9f9;
    border-color: #eaeaea;
}

/* -- Item "Returns" block -------------------------------------------------- */
.apidocs .item .returns .type,
.apidocs .item .returns .type a {
    font-size: 100%;
    margin: 0;
}

/* -- Class Constructor block ----------------------------------------------- */
.apidocs .constructor .item {
    border: none;
    padding-bottom: 0;
}

/* -- File Source View ------------------------------------------------------ */
.apidocs .file pre.code,
#doc .apidocs .file pre.prettyprint {
    background: inherit;
    border: none;
    overflow: visible;
    padding: 0;
}

.apidocs .L0,
.apidocs .L1,
.apidocs .L2,
.apidocs .L3,
.apidocs .L4,
.apidocs .L5,
.apidocs .L6,
.apidocs .L7,
.apidocs .L8,
.apidocs .L9 {
    background: inherit;
}

/* -- Submodule List -------------------------------------------------------- */
.apidocs .module-submodule-description {
    font-size: 12px;
    margin: 0.3em 0 1em;
}

.apidocs .module-submodule-description p:first-child { margin-top: 0; }

/* -- Sidebar TabView ------------------------------------------------------- */
#api-tabview { margin-top: 0.6em; }

#api-tabview-filter,
#api-tabview-panel {
    border: 1px solid #dfdfdf;
}

#api-tabview-filter {
    border-bottom: none;
    border-top: none;
    padding: 0.6em 10px 0 10px;
}

#api-tabview-panel { border-top: none; }
#api-filter { width: 97%; }

/* -- Content TabView ------------------------------------------------------- */
#classdocs .yui3-tabview-panel { border: none; }

/* -- Source File Contents -------------------------------------------------- */
.prettyprint li.L0,
.prettyprint li.L1,
.prettyprint li.L2,
.prettyprint li.L3,
.prettyprint li.L5,
.prettyprint li.L6,
.prettyprint li.L7,
.prettyprint li.L8 {
    list-style: decimal;
}

/* -- API options ----------------------------------------------------------- */
#api-options {
    font-size: 11px;
    margin-top: 2.2em;
    position: absolute;
    right: 1.5em;
}

/*#api-options label { margin-right: 0.6em; }*/

/* -- API list -------------------------------------------------------------- */
#api-list {
    margin-top: 1.5em;
    *zoom: 1;
}

.apis {
    font-size: 12px;
    line-height: 1.4;
    list-style: none;
    margin: 0;
    padding: 0.5em 0 0.5em 0.4em;
}

.apis a {
    border: 1px solid transparent;
    display: block;
    margin: 0 0 0 -4px;
    padding: 1px 4px 0;
    text-decoration: none;
    _border: none;
    _display: inline;
}

.apis a:hover,
.apis a:focus {
    background: #E8EDFC;
    background: -moz-linear-gradient(top, #e8edfc 0%, #becef7 100%);
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#E8EDFC), color-stop(100%,#BECEF7));
    border-color: #AAC0FA;
    border-radius: 3px;
    color: #333;
    outline: none;
}

.api-list-item a:hover,
.api-list-item a:focus {
    font-weight: bold;
    text-shadow: 1px 1px 1px #fff;
}

.apis .message { color: #888; }
.apis .result a { padding: 3px 5px 2px; }

.apis .result .type {
    right: 4px;
    top: 7px;
}

.api-list-item .yui3-highlight {
    font-weight: bold;
}

