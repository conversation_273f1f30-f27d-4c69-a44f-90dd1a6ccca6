YUI.add("yuidoc-meta", function(Y) {
   Y.YUIDoc = { meta: {
    "classes": [
        "AABB",
        "ArrayCollisionMatrix",
        "Body",
        "Box",
        "Broadphase",
        "ConeEquation",
        "ConeTwistConstraint",
        "Constraint",
        "ContactEquation",
        "ContactMaterial",
        "ConvexPolyhedron",
        "Cylinder",
        "Demo",
        "DistanceConstraint",
        "Equation",
        "EventTarget",
        "FrictionEquation",
        "GSSolver",
        "GridBroadphase",
        "Heightfield",
        "HingeConstraint",
        "JacobianElement",
        "LockConstraint",
        "Mat3",
        "Material",
        "NaiveBroadphase",
        "Narrowphase",
        "ObjectCollisionMatrix",
        "Octree",
        "OctreeNode",
        "Particle",
        "Plane",
        "PointToPointConstraint",
        "Pool",
        "Quaternion",
        "Ray",
        "RaycastResult",
        "RaycastVehicle",
        "RigidVehicle",
        "RotationalEquation",
        "RotationalMotorEquation",
        "SAPBroadphase",
        "SPHSystem",
        "Shape",
        "Solver",
        "Sphere",
        "SplitSolver",
        "Spring",
        "Transform",
        "Trimesh",
        "TupleDictionary",
        "Vec3",
        "Vec3Pool",
        "WheelInfo",
        "World"
    ],
    "modules": [],
    "allModules": []
} };
});