{"project": {"name": "cannon", "description": "A lightweight 3D physics engine written in JavaScript.", "version": "0.6.1"}, "files": {"src/collision/AABB.js": {"name": "src/collision/AABB.js", "modules": {}, "classes": {"AABB": 1}, "fors": {}, "namespaces": {}}, "src/collision/ArrayCollisionMatrix.js": {"name": "src/collision/ArrayCollisionMatrix.js", "modules": {}, "classes": {"ArrayCollisionMatrix": 1}, "fors": {}, "namespaces": {}}, "src/collision/Broadphase.js": {"name": "src/collision/Broadphase.js", "modules": {}, "classes": {"Broadphase": 1}, "fors": {}, "namespaces": {}}, "src/collision/GridBroadphase.js": {"name": "src/collision/GridBroadphase.js", "modules": {}, "classes": {"GridBroadphase": 1}, "fors": {}, "namespaces": {}}, "src/collision/NaiveBroadphase.js": {"name": "src/collision/NaiveBroadphase.js", "modules": {}, "classes": {"NaiveBroadphase": 1}, "fors": {}, "namespaces": {}}, "src/collision/ObjectCollisionMatrix.js": {"name": "src/collision/ObjectCollisionMatrix.js", "modules": {}, "classes": {"ObjectCollisionMatrix": 1}, "fors": {}, "namespaces": {}}, "src/collision/Ray.js": {"name": "src/collision/Ray.js", "modules": {}, "classes": {"Ray": 1}, "fors": {}, "namespaces": {}}, "src/collision/RaycastResult.js": {"name": "src/collision/RaycastResult.js", "modules": {}, "classes": {"RaycastResult": 1}, "fors": {}, "namespaces": {}}, "src/collision/SAPBroadphase.js": {"name": "src/collision/SAPBroadphase.js", "modules": {}, "classes": {"SAPBroadphase": 1}, "fors": {}, "namespaces": {}}, "src/constraints/ConeTwistConstraint.js": {"name": "src/constraints/ConeTwistConstraint.js", "modules": {}, "classes": {"ConeTwistConstraint": 1}, "fors": {}, "namespaces": {}}, "src/constraints/Constraint.js": {"name": "src/constraints/Constraint.js", "modules": {}, "classes": {"Constraint": 1}, "fors": {}, "namespaces": {}}, "src/constraints/DistanceConstraint.js": {"name": "src/constraints/DistanceConstraint.js", "modules": {}, "classes": {"DistanceConstraint": 1}, "fors": {}, "namespaces": {}}, "src/constraints/HingeConstraint.js": {"name": "src/constraints/HingeConstraint.js", "modules": {}, "classes": {"HingeConstraint": 1}, "fors": {}, "namespaces": {}}, "src/constraints/LockConstraint.js": {"name": "src/constraints/LockConstraint.js", "modules": {}, "classes": {"LockConstraint": 1}, "fors": {}, "namespaces": {}}, "src/constraints/PointToPointConstraint.js": {"name": "src/constraints/PointToPointConstraint.js", "modules": {}, "classes": {"PointToPointConstraint": 1}, "fors": {}, "namespaces": {}}, "src/demo/Demo.js": {"name": "src/demo/Demo.js", "modules": {}, "classes": {"Demo": 1}, "fors": {}, "namespaces": {}}, "src/equations/ConeEquation.js": {"name": "src/equations/ConeEquation.js", "modules": {}, "classes": {"ConeEquation": 1}, "fors": {}, "namespaces": {}}, "src/equations/ContactEquation.js": {"name": "src/equations/ContactEquation.js", "modules": {}, "classes": {"ContactEquation": 1}, "fors": {}, "namespaces": {}}, "src/equations/Equation.js": {"name": "src/equations/Equation.js", "modules": {}, "classes": {"Equation": 1}, "fors": {}, "namespaces": {}}, "src/equations/FrictionEquation.js": {"name": "src/equations/FrictionEquation.js", "modules": {}, "classes": {"FrictionEquation": 1}, "fors": {}, "namespaces": {}}, "src/equations/RotationalEquation.js": {"name": "src/equations/RotationalEquation.js", "modules": {}, "classes": {"RotationalEquation": 1}, "fors": {}, "namespaces": {}}, "src/equations/RotationalMotorEquation.js": {"name": "src/equations/RotationalMotorEquation.js", "modules": {}, "classes": {"RotationalMotorEquation": 1}, "fors": {}, "namespaces": {}}, "src/material/ContactMaterial.js": {"name": "src/material/ContactMaterial.js", "modules": {}, "classes": {"ContactMaterial": 1}, "fors": {}, "namespaces": {}}, "src/material/Material.js": {"name": "src/material/Material.js", "modules": {}, "classes": {"Material": 1}, "fors": {}, "namespaces": {}}, "src/math/JacobianElement.js": {"name": "src/math/JacobianElement.js", "modules": {}, "classes": {"JacobianElement": 1}, "fors": {}, "namespaces": {}}, "src/math/Mat3.js": {"name": "src/math/Mat3.js", "modules": {}, "classes": {"Mat3": 1}, "fors": {}, "namespaces": {}}, "src/math/Quaternion.js": {"name": "src/math/Quaternion.js", "modules": {}, "classes": {"Quaternion": 1}, "fors": {}, "namespaces": {}}, "src/math/Transform.js": {"name": "src/math/Transform.js", "modules": {}, "classes": {"Transform": 1}, "fors": {}, "namespaces": {}}, "src/math/Vec3.js": {"name": "src/math/Vec3.js", "modules": {}, "classes": {"Vec3": 1}, "fors": {}, "namespaces": {}}, "src/objects/Body.js": {"name": "src/objects/Body.js", "modules": {}, "classes": {"Body": 1}, "fors": {}, "namespaces": {}}, "src/objects/RaycastVehicle.js": {"name": "src/objects/RaycastVehicle.js", "modules": {}, "classes": {"RaycastVehicle": 1}, "fors": {}, "namespaces": {}}, "src/objects/RigidVehicle.js": {"name": "src/objects/RigidVehicle.js", "modules": {}, "classes": {"RigidVehicle": 1}, "fors": {}, "namespaces": {}}, "src/objects/SPHSystem.js": {"name": "src/objects/SPHSystem.js", "modules": {}, "classes": {"SPHSystem": 1}, "fors": {}, "namespaces": {}}, "src/objects/Spring.js": {"name": "src/objects/Spring.js", "modules": {}, "classes": {"Spring": 1}, "fors": {}, "namespaces": {}}, "src/objects/WheelInfo.js": {"name": "src/objects/WheelInfo.js", "modules": {}, "classes": {"WheelInfo": 1}, "fors": {}, "namespaces": {}}, "src/shapes/Box.js": {"name": "src/shapes/Box.js", "modules": {}, "classes": {"Box": 1}, "fors": {}, "namespaces": {}}, "src/shapes/ConvexPolyhedron.js": {"name": "src/shapes/ConvexPolyhedron.js", "modules": {}, "classes": {"ConvexPolyhedron": 1}, "fors": {}, "namespaces": {}}, "src/shapes/Cylinder.js": {"name": "src/shapes/Cylinder.js", "modules": {}, "classes": {"Cylinder": 1}, "fors": {}, "namespaces": {}}, "src/shapes/Heightfield.js": {"name": "src/shapes/Heightfield.js", "modules": {}, "classes": {"Heightfield": 1}, "fors": {}, "namespaces": {}}, "src/shapes/Particle.js": {"name": "src/shapes/Particle.js", "modules": {}, "classes": {"Particle": 1}, "fors": {}, "namespaces": {}}, "src/shapes/Plane.js": {"name": "src/shapes/Plane.js", "modules": {}, "classes": {"Plane": 1}, "fors": {}, "namespaces": {}}, "src/shapes/Shape.js": {"name": "src/shapes/Shape.js", "modules": {}, "classes": {"Shape": 1}, "fors": {}, "namespaces": {}}, "src/shapes/Sphere.js": {"name": "src/shapes/Sphere.js", "modules": {}, "classes": {"Sphere": 1}, "fors": {}, "namespaces": {}}, "src/shapes/Trimesh.js": {"name": "src/shapes/Trimesh.js", "modules": {}, "classes": {"Trimesh": 1}, "fors": {}, "namespaces": {}}, "src/solver/GSSolver.js": {"name": "src/solver/GSSolver.js", "modules": {}, "classes": {"GSSolver": 1}, "fors": {}, "namespaces": {}}, "src/solver/Solver.js": {"name": "src/solver/Solver.js", "modules": {}, "classes": {"Solver": 1}, "fors": {}, "namespaces": {}}, "src/solver/SplitSolver.js": {"name": "src/solver/SplitSolver.js", "modules": {}, "classes": {"SplitSolver": 1}, "fors": {}, "namespaces": {}}, "src/utils/EventTarget.js": {"name": "src/utils/EventTarget.js", "modules": {}, "classes": {"EventTarget": 1}, "fors": {}, "namespaces": {}}, "src/utils/Octree.js": {"name": "src/utils/Octree.js", "modules": {}, "classes": {"OctreeNode": 1, "Octree": 1}, "fors": {}, "namespaces": {}}, "src/utils/Pool.js": {"name": "src/utils/Pool.js", "modules": {}, "classes": {"Pool": 1}, "fors": {}, "namespaces": {}}, "src/utils/TupleDictionary.js": {"name": "src/utils/TupleDictionary.js", "modules": {}, "classes": {"TupleDictionary": 1}, "fors": {}, "namespaces": {}}, "src/utils/Utils.js": {"name": "src/utils/Utils.js", "modules": {}, "classes": {}, "fors": {}, "namespaces": {}}, "src/utils/Vec3Pool.js": {"name": "src/utils/Vec3Pool.js", "modules": {}, "classes": {"Vec3Pool": 1}, "fors": {}, "namespaces": {}}, "src/world/Narrowphase.js": {"name": "src/world/Narrowphase.js", "modules": {}, "classes": {"Narrowphase": 1}, "fors": {}, "namespaces": {}}, "src/world/World.js": {"name": "src/world/World.js", "modules": {}, "classes": {"World": 1}, "fors": {}, "namespaces": {}}}, "modules": {}, "classes": {"AABB": {"name": "AABB", "shortname": "AABB", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/collision/AABB.js", "line": 6, "description": "Axis aligned bounding box class.", "is_constructor": 1, "params": [{"name": "options", "description": "", "type": "Object", "optional": true, "props": [{"name": "upperBound", "description": "", "type": "Vec3", "optional": true}, {"name": "lowerBound", "description": "", "type": "Vec3", "optional": true}]}]}, "ArrayCollisionMatrix": {"name": "ArrayCollisionMatrix", "shortname": "ArrayCollisionMatrix", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/collision/ArrayCollisionMatrix.js", "line": 3, "description": "Collision \"matrix\". It's actually a triangular-shaped array of whether two bodies are touching this step, for reference next step", "is_constructor": 1}, "Broadphase": {"name": "Broadphase", "shortname": "Broadphase", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/collision/Broadphase.js", "line": 9, "description": "Base class for broadphase implementations", "is_constructor": 1, "author": "<PERSON><PERSON><PERSON><PERSON>"}, "GridBroadphase": {"name": "GridBroadphase", "shortname": "GridBroadphase", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/collision/GridBroadphase.js", "line": 7, "description": "Axis aligned uniform grid broadphase.", "is_constructor": 1, "extends": "Broadphase", "todo": ["Needs support for more than just planes and spheres."], "params": [{"name": "aabbMin", "description": "", "type": "Vec3"}, {"name": "aabbMax", "description": "", "type": "Vec3"}, {"name": "nx", "description": "Number of boxes along x", "type": "Number"}, {"name": "ny", "description": "Number of boxes along y", "type": "Number"}, {"name": "nz", "description": "Number of boxes along z", "type": "Number"}]}, "NaiveBroadphase": {"name": "NaiveBroadphase", "shortname": "NaiveBroadphase", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/collision/NaiveBroadphase.js", "line": 6, "description": "Naive broadphase implementation, used in lack of better ones.", "is_constructor": 1, "extends": "Broadphase"}, "ObjectCollisionMatrix": {"name": "ObjectCollisionMatrix", "shortname": "ObjectCollisionMatrix", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/collision/ObjectCollisionMatrix.js", "line": 3, "description": "Records what objects are colliding with each other", "is_constructor": 1}, "Ray": {"name": "<PERSON>", "shortname": "<PERSON>", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/collision/Ray.js", "line": 12, "description": "A line in 3D space that intersects bodies and return points.", "is_constructor": 1, "params": [{"name": "from", "description": "", "type": "Vec3"}, {"name": "to", "description": "", "type": "Vec3"}]}, "RaycastResult": {"name": "RaycastResult", "shortname": "RaycastResult", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/collision/RaycastResult.js", "line": 5, "description": "Storage for Ray casting data.", "is_constructor": 1}, "SAPBroadphase": {"name": "SAPBroadphase", "shortname": "SAPBroadphase", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/collision/SAPBroadphase.js", "line": 6, "description": "Sweep and prune broadphase along one axis.", "is_constructor": 1, "params": [{"name": "world", "description": "", "type": "World", "optional": true}], "extends": "Broadphase"}, "ConeTwistConstraint": {"name": "ConeTwistConstraint", "shortname": "ConeTwistConstraint", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/constraints/ConeTwistConstraint.js", "line": 10, "is_constructor": 1, "author": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "bodyA", "description": "", "type": "Body"}, {"name": "bodyB", "description": "", "type": "Body"}, {"name": "options", "description": "", "type": "Object", "optional": true, "props": [{"name": "pivotA", "description": "", "type": "Vec3", "optional": true}, {"name": "pivotB", "description": "", "type": "Vec3", "optional": true}, {"name": "axisA", "description": "", "type": "Vec3", "optional": true}, {"name": "axisB", "description": "", "type": "Vec3", "optional": true}, {"name": "max<PERSON><PERSON>ce", "description": "", "type": "Number", "optional": true, "optdefault": "1e6"}]}], "extends": "PointToPointConstraint"}, "Constraint": {"name": "Constraint", "shortname": "Constraint", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/constraints/Constraint.js", "line": 5, "description": "Constraint base class", "author": "<PERSON><PERSON><PERSON><PERSON>", "is_constructor": 1, "params": [{"name": "bodyA", "description": "", "type": "Body"}, {"name": "bodyB", "description": "", "type": "Body"}, {"name": "options", "description": "", "type": "Object", "optional": true, "props": [{"name": "collideConnected", "description": "", "type": "Boolean", "optional": true, "optdefault": "true"}, {"name": "wakeUpBodies", "description": "", "type": "Boolean", "optional": true, "optdefault": "true"}]}]}, "DistanceConstraint": {"name": "DistanceConstraint", "shortname": "DistanceConstraint", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/constraints/DistanceConstraint.js", "line": 6, "description": "Constrains two bodies to be at a constant distance from each others center of mass.", "is_constructor": 1, "author": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "bodyA", "description": "", "type": "Body"}, {"name": "bodyB", "description": "", "type": "Body"}, {"name": "distance", "description": "The distance to keep. If undefined, it will be set to the current distance between bodyA and bodyB", "type": "Number", "optional": true}, {"name": "max<PERSON><PERSON>ce", "description": "", "type": "Number", "optional": true, "optdefault": "1e6"}], "extends": "Constraint"}, "HingeConstraint": {"name": "HingeConstraint", "shortname": "HingeConstraint", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/constraints/HingeConstraint.js", "line": 10, "description": "Hinge constraint. Think of it as a door hinge. It tries to keep the door in the correct place and with the correct orientation.", "is_constructor": 1, "author": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "bodyA", "description": "", "type": "Body"}, {"name": "bodyB", "description": "", "type": "Body"}, {"name": "options", "description": "", "type": "Object", "optional": true, "props": [{"name": "pivotA", "description": "A point defined locally in bodyA. This defines the offset of axisA.", "type": "Vec3", "optional": true}, {"name": "axisA", "description": "An axis that bodyA can rotate around, defined locally in bodyA.", "type": "Vec3", "optional": true}, {"name": "pivotB", "description": "", "type": "Vec3", "optional": true}, {"name": "axisB", "description": "", "type": "Vec3", "optional": true}, {"name": "max<PERSON><PERSON>ce", "description": "", "type": "Number", "optional": true, "optdefault": "1e6"}]}], "extends": "PointToPointConstraint"}, "LockConstraint": {"name": "LockConstraint", "shortname": "LockConstraint", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/constraints/LockConstraint.js", "line": 10, "description": "Lock constraint. Will remove all degrees of freedom between the bodies.", "is_constructor": 1, "author": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "bodyA", "description": "", "type": "Body"}, {"name": "bodyB", "description": "", "type": "Body"}, {"name": "options", "description": "", "type": "Object", "optional": true, "props": [{"name": "max<PERSON><PERSON>ce", "description": "", "type": "Number", "optional": true, "optdefault": "1e6"}]}], "extends": "PointToPointConstraint"}, "PointToPointConstraint": {"name": "PointToPointConstraint", "shortname": "PointToPointConstraint", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/constraints/PointToPointConstraint.js", "line": 7, "description": "Connects two bodies at given offset points.", "extends": "Constraint", "is_constructor": 1, "params": [{"name": "bodyA", "description": "", "type": "Body"}, {"name": "pivotA", "description": "The point relative to the center of mass of bodyA which bodyA is constrained to.", "type": "Vec3"}, {"name": "bodyB", "description": "Body that will be constrained in a similar way to the same point as bodyA. We will therefore get a link between bodyA and bodyB. If not specified, bodyA will be constrained to a static point.", "type": "Body"}, {"name": "pivotB", "description": "See pivotA.", "type": "Vec3"}, {"name": "max<PERSON><PERSON>ce", "description": "The maximum force that should be applied to constrain the bodies.", "type": "Number"}], "example": ["\n    var bodyA = new Body({ mass: 1 });\n    var bodyB = new Body({ mass: 1 });\n    bodyA.position.set(-1, 0, 0);\n    bodyB.position.set(1, 0, 0);\n    bodyA.addShape(shapeA);\n    bodyB.addShape(shapeB);\n    world.addBody(bodyA);\n    world.addBody(bodyB);\n    var localPivotA = new Vec3(1, 0, 0);\n    var localPivotB = new Vec3(-1, 0, 0);\n    var constraint = new PointToPointConstraint(bodyA, localPivotA, bodyB, localPivotB);\n    world.addConstraint(constraint);"]}, "Demo": {"name": "Demo", "shortname": "Demo", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/demo/Demo.js", "line": 5, "description": "Demo framework class. If you want to learn how to connect Cannon.js with Three.js, please look at the examples/ instead.", "is_constructor": 1, "params": [{"name": "options", "description": "", "type": "Object"}]}, "ConeEquation": {"name": "ConeEquation", "shortname": "ConeEquation", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/equations/ConeEquation.js", "line": 7, "description": "Cone equation. Works to keep the given body world vectors aligned, or tilted within a given angle from each other.", "is_constructor": 1, "author": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "bodyA", "description": "", "type": "Body"}, {"name": "bodyB", "description": "", "type": "Body"}, {"name": "options.axisA", "description": "Local axis in A", "type": "Vec3", "optional": true}, {"name": "options.axisB", "description": "Local axis in B", "type": "Vec3", "optional": true}, {"name": "options.angle", "description": "The \"cone angle\" to keep", "type": "Vec3", "optional": true}, {"name": "options.maxForce", "description": "", "type": "Number", "optional": true, "optdefault": "1e6"}], "extends": "Equation"}, "ContactEquation": {"name": "ContactEquation", "shortname": "ContactEquation", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/equations/ContactEquation.js", "line": 7, "description": "Contact/non-penetration constraint equation", "is_constructor": 1, "author": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "bodyA", "description": "", "type": "Body"}, {"name": "bodyB", "description": "", "type": "Body"}], "extends": "Equation"}, "Equation": {"name": "Equation", "shortname": "Equation", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/equations/Equation.js", "line": 6, "description": "Equation base class", "is_constructor": 1, "author": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "bi", "description": "", "type": "Body"}, {"name": "bj", "description": "", "type": "Body"}, {"name": "minForce", "description": "Minimum (read: negative max) force to be applied by the constraint.", "type": "Number"}, {"name": "max<PERSON><PERSON>ce", "description": "Maximum (read: positive max) force to be applied by the constraint.", "type": "Number"}]}, "FrictionEquation": {"name": "FrictionEquation", "shortname": "FrictionEquation", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/equations/FrictionEquation.js", "line": 7, "description": "Constrains the slipping in a contact along a tangent", "is_constructor": 1, "author": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "bodyA", "description": "", "type": "Body"}, {"name": "bodyB", "description": "", "type": "Body"}, {"name": "slipForce", "description": "should be +-F_friction = +-mu * F_normal = +-mu * m * g", "type": "Number"}], "extends": "Equation"}, "RotationalEquation": {"name": "RotationalEquation", "shortname": "RotationalEquation", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/equations/RotationalEquation.js", "line": 7, "description": "Rotational constraint. Works to keep the local vectors orthogonal to each other in world space.", "is_constructor": 1, "author": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "bodyA", "description": "", "type": "Body"}, {"name": "bodyB", "description": "", "type": "Body"}, {"name": "options.axisA", "description": "", "type": "Vec3", "optional": true}, {"name": "options.axisB", "description": "", "type": "Vec3", "optional": true}, {"name": "options.maxForce", "description": "", "type": "Number", "optional": true}], "extends": "Equation"}, "RotationalMotorEquation": {"name": "RotationalMotorEquation", "shortname": "RotationalMotorEquation", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/equations/RotationalMotorEquation.js", "line": 7, "description": "Rotational motor constraint. Tries to keep the relative angular velocity of the bodies to a given value.", "is_constructor": 1, "author": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "bodyA", "description": "", "type": "Body"}, {"name": "bodyB", "description": "", "type": "Body"}, {"name": "max<PERSON><PERSON>ce", "description": "", "type": "Number"}], "extends": "Equation"}, "ContactMaterial": {"name": "ContactMaterial", "shortname": "ContactMaterial", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/material/ContactMaterial.js", "line": 5, "description": "Defines what happens when two materials meet.", "is_constructor": 1, "params": [{"name": "m1", "description": "", "type": "Material"}, {"name": "m2", "description": "", "type": "Material"}, {"name": "options", "description": "", "type": "Object", "optional": true, "props": [{"name": "friction", "description": "", "type": "Number", "optional": true, "optdefault": "0.3"}, {"name": "restitution", "description": "", "type": "Number", "optional": true, "optdefault": "0.3"}, {"name": "contactEquationStiffness", "description": "", "type": "Number", "optional": true, "optdefault": "1e7"}, {"name": "contactEquationRelaxation", "description": "", "type": "Number", "optional": true, "optdefault": "3"}, {"name": "frictionEquationStiffness", "description": "", "type": "Number", "optional": true, "optdefault": "1e7"}, {"name": "frictionEquationRelaxation", "description": "", "type": "Number", "optional": true, "optdefault": "3"}]}]}, "Material": {"name": "Material", "shortname": "Material", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/material/Material.js", "line": 3, "description": "Defines a physics material.", "is_constructor": 1, "params": [{"name": "options", "description": "", "type": "Object", "optional": true}], "author": "<PERSON><PERSON><PERSON><PERSON>"}, "JacobianElement": {"name": "<PERSON><PERSON><PERSON>", "shortname": "<PERSON><PERSON><PERSON>", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/math/JacobianElement.js", "line": 5, "description": "An element containing 6 entries, 3 spatial and 3 rotational degrees of freedom.", "is_constructor": 1}, "Mat3": {"name": "Mat3", "shortname": "Mat3", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/math/Mat3.js", "line": 5, "description": "A 3x3 matrix.", "is_constructor": 1, "params": [{"name": "array", "description": "elements Array of nine elements. Optional."}], "author": "schteppe / http://github.com/schteppe"}, "Quaternion": {"name": "Quaternion", "shortname": "Quaternion", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/math/Quaternion.js", "line": 5, "description": "A Quaternion describes a rotation in 3D space. The Quaternion is mathematically defined as Q = x*i + y*j + z*k + w, where (i,j,k) are imaginary basis vectors. (x,y,z) can be seen as a vector related to the axis of rotation, while the real multiplier, w, is related to the amount of rotation.", "is_constructor": 1, "params": [{"name": "x", "description": "Multiplier of the imaginary basis vector i.", "type": "Number"}, {"name": "y", "description": "Multiplier of the imaginary basis vector j.", "type": "Number"}, {"name": "z", "description": "Multiplier of the imaginary basis vector k.", "type": "Number"}, {"name": "w", "description": "Multiplier of the real part.", "type": "Number"}], "see": ["http://en.wikipedia.org/wiki/Quaternion"]}, "Transform": {"name": "Transform", "shortname": "Transform", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/math/Transform.js", "line": 6, "is_constructor": 1}, "Vec3": {"name": "Vec3", "shortname": "Vec3", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/math/Vec3.js", "line": 5, "description": "3-dimensional vector", "is_constructor": 1, "params": [{"name": "x", "description": "", "type": "Number"}, {"name": "y", "description": "", "type": "Number"}, {"name": "z", "description": "", "type": "Number"}], "author": "<PERSON><PERSON><PERSON><PERSON>", "example": ["\n    var v = new Vec3(1, 2, 3);\n    console.log('x=' + v.x); // x=1"]}, "Body": {"name": "Body", "shortname": "Body", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/objects/Body.js", "line": 12, "description": "Base class for all body types.", "is_constructor": 1, "extends": "EventTarget", "params": [{"name": "options", "description": "", "type": "Object", "optional": true, "props": [{"name": "position", "description": "", "type": "Vec3", "optional": true}, {"name": "velocity", "description": "", "type": "Vec3", "optional": true}, {"name": "angularVelocity", "description": "", "type": "Vec3", "optional": true}, {"name": "quaternion", "description": "", "type": "Quaternion", "optional": true}, {"name": "mass", "description": "", "type": "Number", "optional": true}, {"name": "material", "description": "", "type": "Material", "optional": true}, {"name": "type", "description": "", "type": "Number", "optional": true}, {"name": "linearDamping", "description": "", "type": "Number", "optional": true, "optdefault": "0.01"}, {"name": "angularDamping", "description": "", "type": "Number", "optional": true, "optdefault": "0.01"}, {"name": "allowSleep", "description": "", "type": "Boolean", "optional": true, "optdefault": "true"}, {"name": "sleepSpeedLimit", "description": "", "type": "Number", "optional": true, "optdefault": "0.1"}, {"name": "sleepTimeLimit", "description": "", "type": "Number", "optional": true, "optdefault": "1"}, {"name": "collisionFilterGroup", "description": "", "type": "Number", "optional": true, "optdefault": "1"}, {"name": "collisionFilterMask", "description": "", "type": "Number", "optional": true, "optdefault": "1"}, {"name": "fixedRotation", "description": "", "type": "Boolean", "optional": true, "optdefault": "false"}, {"name": "shape", "description": "", "type": "Body", "optional": true}]}], "example": ["\n    var body = new Body({\n        mass: 1\n    });\n    var shape = new Sphere(1);\n    body.addShape(shape);\n    world.add(body);"]}, "RaycastVehicle": {"name": "RaycastVehicle", "shortname": "RaycastVehicle", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/objects/RaycastVehicle.js", "line": 10, "description": "Vehicle helper class that casts rays from the wheel positions towards the ground and applies forces.", "is_constructor": 1, "params": [{"name": "options", "description": "", "type": "Object", "optional": true, "props": [{"name": "chassisBody", "description": "The car chassis body.", "type": "Body", "optional": true}, {"name": "indexRightAxis", "description": "Axis to use for right. x=0, y=1, z=2", "type": "Integer", "optional": true}, {"name": "indexLeftAxis", "description": "", "type": "Integer", "optional": true}, {"name": "indexUpAxis", "description": "", "type": "Integer", "optional": true}]}]}, "RigidVehicle": {"name": "RigidVehicle", "shortname": "RigidVehicle", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/objects/RigidVehicle.js", "line": 9, "description": "Simple vehicle helper class with spherical rigid body wheels.", "is_constructor": 1, "params": [{"name": "options.chassisBody", "description": "", "type": "Body", "optional": true}]}, "SPHSystem": {"name": "SPHSystem", "shortname": "SPHSystem", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/objects/SPHSystem.js", "line": 10, "description": "Smoothed-particle hydrodynamics system", "is_constructor": 1}, "Spring": {"name": "Spring", "shortname": "Spring", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/objects/Spring.js", "line": 5, "description": "A spring, connecting two bodies.", "is_constructor": 1, "params": [{"name": "bodyA", "description": "", "type": "Body"}, {"name": "bodyB", "description": "", "type": "Body"}, {"name": "options", "description": "", "type": "Object", "optional": true, "props": [{"name": "restLength", "description": "A number > 0. <PERSON><PERSON><PERSON>: 1", "type": "Number", "optional": true}, {"name": "stiffness", "description": "A number >= 0. De<PERSON><PERSON>: 100", "type": "Number", "optional": true}, {"name": "damping", "description": "A number >= 0. <PERSON><PERSON><PERSON>: 1", "type": "Number", "optional": true}, {"name": "worldAnchorA", "description": "Where to hook the spring to body A, in world coordinates.", "type": "Vec3", "optional": true}, {"name": "worldAnchorB", "description": "", "type": "Vec3", "optional": true}, {"name": "localAnchorA", "description": "Where to hook the spring to body A, in local body coordinates.", "type": "Vec3", "optional": true}, {"name": "localAnchorB", "description": "", "type": "Vec3", "optional": true}]}]}, "WheelInfo": {"name": "WheelInfo", "shortname": "WheelInfo", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/objects/WheelInfo.js", "line": 8, "is_constructor": 1, "params": [{"name": "options", "description": "", "type": "Object", "optional": true, "props": [{"name": "chassisConnectionPointLocal", "description": "", "type": "Vec3", "optional": true}, {"name": "chassisConnectionPointWorld", "description": "", "type": "Vec3", "optional": true}, {"name": "directionLocal", "description": "", "type": "Vec3", "optional": true}, {"name": "directionWorld", "description": "", "type": "Vec3", "optional": true}, {"name": "axleLocal", "description": "", "type": "Vec3", "optional": true}, {"name": "axleWorld", "description": "", "type": "Vec3", "optional": true}, {"name": "suspensionRestLength", "description": "", "type": "Number", "optional": true, "optdefault": "1"}, {"name": "suspensionMaxLength", "description": "", "type": "Number", "optional": true, "optdefault": "2"}, {"name": "radius", "description": "", "type": "Number", "optional": true, "optdefault": "1"}, {"name": "suspensionStiffness", "description": "", "type": "Number", "optional": true, "optdefault": "100"}, {"name": "dampingCompression", "description": "", "type": "Number", "optional": true, "optdefault": "10"}, {"name": "dampingRelaxation", "description": "", "type": "Number", "optional": true, "optdefault": "10"}, {"name": "frictionSlip", "description": "", "type": "Number", "optional": true, "optdefault": "10000"}, {"name": "steering", "description": "", "type": "Number", "optional": true, "optdefault": "0"}, {"name": "rotation", "description": "", "type": "Number", "optional": true, "optdefault": "0"}, {"name": "deltaRotation", "description": "", "type": "Number", "optional": true, "optdefault": "0"}, {"name": "rollInfluence", "description": "", "type": "Number", "optional": true, "optdefault": "0.01"}, {"name": "maxSuspensionForce", "description": "", "type": "Number", "optional": true}, {"name": "isFrontWheel", "description": "", "type": "Boolean", "optional": true, "optdefault": "true"}, {"name": "clippedInvContactDotSuspension", "description": "", "type": "Number", "optional": true, "optdefault": "1"}, {"name": "suspensionRelativeVelocity", "description": "", "type": "Number", "optional": true, "optdefault": "0"}, {"name": "suspensionF<PERSON>ce", "description": "", "type": "Number", "optional": true, "optdefault": "0"}, {"name": "skidInfo", "description": "", "type": "Number", "optional": true, "optdefault": "0"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "Number", "optional": true, "optdefault": "0"}, {"name": "maxSuspensionTravel", "description": "", "type": "Number", "optional": true, "optdefault": "1"}, {"name": "useCustomSlidingRotationalSpeed", "description": "", "type": "Boolean", "optional": true, "optdefault": "false"}, {"name": "customSlidingRotationalSpeed", "description": "", "type": "Number", "optional": true, "optdefault": "-0.1"}]}]}, "Box": {"name": "Box", "shortname": "Box", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/shapes/Box.js", "line": 7, "description": "A 3d box shape.", "is_constructor": 1, "params": [{"name": "halfExtents", "description": "", "type": "Vec3"}], "author": "<PERSON><PERSON><PERSON><PERSON>", "extends": "<PERSON><PERSON><PERSON>"}, "ConvexPolyhedron": {"name": "ConvexPolyhedron", "shortname": "ConvexPolyhedron", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/shapes/ConvexPolyhedron.js", "line": 8, "description": "A set of polygons describing a convex shape.", "is_constructor": 1, "extends": "<PERSON><PERSON><PERSON>", "params": [{"name": "points", "description": "An array of Vec3's", "type": "Array"}, {"name": "faces", "description": "Array of integer arrays, describing which vertices that is included in each face.", "type": "Array"}], "author": "schteppe / https://github.com/schteppe", "see": ["http://www.altdevblogaday.com/2011/05/13/contact-generation-between-3d-convex-meshes/", "http://bullet.googlecode.com/svn/trunk/src/BulletCollision/NarrowPhaseCollision/btPolyhedralContactClipping.cpp"], "todo": ["Move the clipping functions to ContactGenerator?", "Automatically merge coplanar polygons in constructor."]}, "Cylinder": {"name": "<PERSON><PERSON><PERSON>", "shortname": "<PERSON><PERSON><PERSON>", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/shapes/Cylinder.js", "line": 8, "is_constructor": 1, "extends": "ConvexPolyhedron", "author": "schteppe / https://github.com/schteppe", "params": [{"name": "radiusTop", "description": "", "type": "Number"}, {"name": "radiusBottom", "description": "", "type": "Number"}, {"name": "height", "description": "", "type": "Number"}, {"name": "numSegments", "description": "The number of segments to build the cylinder out of", "type": "Number"}]}, "Heightfield": {"name": "Heightfield", "shortname": "Heightfield", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/shapes/Heightfield.js", "line": 8, "description": "Heightfield shape class. Height data is given as an array. These data points are spread out evenly with a given distance.", "extends": "<PERSON><PERSON><PERSON>", "is_constructor": 1, "params": [{"name": "data", "description": "An array of Y values that will be used to construct the terrain.", "type": "Array"}, {"name": "options", "description": "", "type": "Object", "props": [{"name": "minValue", "description": "Minimum value of the data points in the data array. Will be computed automatically if not given.", "type": "Number", "optional": true}, {"name": "maxValue", "description": "Maximum value.", "type": "Number", "optional": true}, {"name": "elementSize", "description": "World spacing between the data points in X direction.", "type": "Number", "optional": true, "optdefault": "0.1"}]}], "todo": ["Should be possible to use along all axes", "not just y"], "example": ["\n    // Generate some height data (y-values).\n    var data = [];\n    for(var i = 0; i < 1000; i++){\n        var y = 0.5 * Math.cos(0.2 * i);\n        data.push(y);\n    }\n\n    // Create the heightfield shape\n    var heightfieldShape = new Heightfield(data, {\n        elementSize: 1 // Distance between the data points in X and Y directions\n    });\n    var heightfieldBody = new Body();\n    heightfieldBody.addShape(heightfieldShape);\n    world.addBody(heightfieldBody);"]}, "Particle": {"name": "Particle", "shortname": "Particle", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/shapes/Particle.js", "line": 6, "description": "Particle shape.", "is_constructor": 1, "author": "<PERSON><PERSON><PERSON><PERSON>", "extends": "<PERSON><PERSON><PERSON>"}, "Plane": {"name": "Plane", "shortname": "Plane", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/shapes/Plane.js", "line": 6, "description": "A plane, facing in the Z direction. The plane has its surface at z=0 and everything below z=0 is assumed to be solid plane. To make the plane face in some other direction than z, you must put it inside a RigidBody and rotate that body. See the demos.", "is_constructor": 1, "extends": "<PERSON><PERSON><PERSON>", "author": "<PERSON><PERSON><PERSON><PERSON>"}, "Shape": {"name": "<PERSON><PERSON><PERSON>", "shortname": "<PERSON><PERSON><PERSON>", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/shapes/Shape.js", "line": 8, "description": "Base class for shapes", "is_constructor": 1, "author": "<PERSON><PERSON><PERSON><PERSON>", "todo": ["Should have a mechanism for caching bounding sphere radius instead of calculating it each time"]}, "Sphere": {"name": "Sphere", "shortname": "Sphere", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/shapes/Sphere.js", "line": 6, "description": "Spherical shape", "is_constructor": 1, "extends": "<PERSON><PERSON><PERSON>", "params": [{"name": "radius", "description": "The radius of the sphere, a non-negative number.", "type": "Number"}], "author": "schteppe / http://github.com/schteppe"}, "Trimesh": {"name": "Trimesh", "shortname": "Trimesh", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/shapes/Trimesh.js", "line": 10, "is_constructor": 1, "params": [{"name": "vertices", "description": "", "type": "Array"}, {"name": "indices", "description": "", "type": "Array"}], "extends": "<PERSON><PERSON><PERSON>", "example": ["\n    // How to make a mesh with a single triangle\n    var vertices = [\n        0, 0, 0, // vertex 0\n        1, 0, 0, // vertex 1\n        0, 1, 0  // vertex 2\n    ];\n    var indices = [\n        0, 1, 2  // triangle 0\n    ];\n    var trimeshShape = new Trimesh(vertices, indices);"]}, "GSSolver": {"name": "GSSolver", "shortname": "GSSolver", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/solver/GSSolver.js", "line": 7, "description": "Constraint equation <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> solver.", "is_constructor": 1, "todo": ["The spook parameters should be specified for each constraint", "not globally."], "author": "schteppe / https://github.com/schteppe", "see": ["https://www8.cs.umu.se/kurser/5DV058/VT09/lectures/spooknotes.pdf"], "extends": "Solver"}, "Solver": {"name": "Solver", "shortname": "Solver", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/solver/Solver.js", "line": 3, "description": "Constraint equation solver base class.", "is_constructor": 1, "author": "schteppe / https://github.com/schteppe"}, "SplitSolver": {"name": "SplitSolver", "shortname": "SplitSolver", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/solver/SplitSolver.js", "line": 8, "description": "Splits the equations into islands and solves them independently. Can improve performance.", "is_constructor": 1, "extends": "Solver", "params": [{"name": "subsolver", "description": "", "type": "Solver"}]}, "EventTarget": {"name": "EventTarget", "shortname": "EventTarget", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/utils/EventTarget.js", "line": 1, "description": "Base class for objects that dispatches events.", "is_constructor": 1}, "OctreeNode": {"name": "OctreeNode", "shortname": "OctreeNode", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/utils/Octree.js", "line": 6, "params": [{"name": "options", "description": "", "type": "Object", "optional": true, "props": [{"name": "root", "description": "", "type": "Octree", "optional": true}, {"name": "aabb", "description": "", "type": "AABB", "optional": true}]}]}, "Octree": {"name": "Octree", "shortname": "Octree", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/utils/Octree.js", "line": 40, "params": [{"name": "aabb", "description": "The total AABB of the tree", "type": "AABB"}, {"name": "options", "description": "", "type": "Object", "optional": true, "props": [{"name": "max<PERSON><PERSON><PERSON>", "description": "", "type": "Number", "optional": true, "optdefault": "8"}]}], "extends": "OctreeNode"}, "Pool": {"name": "Pool", "shortname": "Pool", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/utils/Pool.js", "line": 3, "description": "For pooling objects that can be reused.", "is_constructor": 1}, "TupleDictionary": {"name": "TupleDictionary", "shortname": "TupleDictionary", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/utils/TupleDictionary.js", "line": 3, "is_constructor": 1}, "Vec3Pool": {"name": "Vec3Pool", "shortname": "Vec3Pool", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/utils/Vec3Pool.js", "line": 6, "is_constructor": 1, "extends": "Pool"}, "Narrowphase": {"name": "Narrowphase", "shortname": "Narrowphase", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/world/Narrowphase.js", "line": 15, "description": "Helper class for the World. Generates ContactEquations.", "is_constructor": 1, "todo": ["Sphere-ConvexPolyhedron contacts", "Contact reduction", "should move methods to prototype"]}, "World": {"name": "World", "shortname": "World", "classitems": [], "plugins": [], "extensions": [], "plugin_for": [], "extension_for": [], "file": "src/world/World.js", "line": 24, "description": "The physics world", "is_constructor": 1, "extends": "EventTarget"}}, "classitems": [{"file": "src/collision/AABB.js", "line": 17, "description": "The lower bound of the bounding box.", "itemtype": "property", "name": "lowerBound", "type": "{Vec3}", "class": "AABB"}, {"file": "src/collision/AABB.js", "line": 27, "description": "The upper bound of the bounding box.", "itemtype": "property", "name": "upperBound", "type": "{Vec3}", "class": "AABB"}, {"file": "src/collision/AABB.js", "line": 40, "description": "Set the AABB bounds from a set of points.", "itemtype": "method", "name": "setFromPoints", "params": [{"name": "points", "description": "An array of Vec3's.", "type": "Array"}, {"name": "position", "description": "", "type": "Vec3"}, {"name": "quaternion", "description": "", "type": "Quaternion"}, {"name": "skinSize", "description": "", "type": "Number"}], "return": {"description": "The self object", "type": "AABB"}, "class": "AABB"}, {"file": "src/collision/AABB.js", "line": 95, "description": "Copy bounds from an AABB to this AABB", "itemtype": "method", "name": "copy", "params": [{"name": "aabb", "description": "Source to copy from", "type": "AABB"}], "return": {"description": "The this object, for chainability", "type": "AABB"}, "class": "AABB"}, {"file": "src/collision/AABB.js", "line": 107, "description": "Clone an AABB", "itemtype": "method", "name": "clone", "class": "AABB"}, {"file": "src/collision/AABB.js", "line": 115, "description": "Extend this AABB so that it covers the given AABB too.", "itemtype": "method", "name": "extend", "params": [{"name": "aabb", "description": "", "type": "AABB"}], "class": "AABB"}, {"file": "src/collision/AABB.js", "line": 158, "description": "Returns true if the given AABB overlaps this AABB.", "itemtype": "method", "name": "overlaps", "params": [{"name": "aabb", "description": "", "type": "AABB"}], "return": {"description": "", "type": "Boolean"}, "class": "AABB"}, {"file": "src/collision/AABB.js", "line": 180, "description": "Returns true if the given AABB is fully contained in this AABB.", "itemtype": "method", "name": "contains", "params": [{"name": "aabb", "description": "", "type": "AABB"}], "return": {"description": "", "type": "Boolean"}, "class": "AABB"}, {"file": "src/collision/AABB.js", "line": 204, "itemtype": "method", "name": "getCorners", "params": [{"name": "a", "description": "", "type": "Vec3"}, {"name": "b", "description": "", "type": "Vec3"}, {"name": "c", "description": "", "type": "Vec3"}, {"name": "d", "description": "", "type": "Vec3"}, {"name": "e", "description": "", "type": "Vec3"}, {"name": "f", "description": "", "type": "Vec3"}, {"name": "g", "description": "", "type": "Vec3"}, {"name": "h", "description": "", "type": "Vec3"}], "class": "AABB"}, {"file": "src/collision/AABB.js", "line": 240, "description": "Get the representation of an AABB in another frame.", "itemtype": "method", "name": "toLocalFrame", "params": [{"name": "frame", "description": "", "type": "Transform"}, {"name": "target", "description": "", "type": "AABB"}], "return": {"description": "The \"target\" AABB object.", "type": "AABB"}, "class": "AABB"}, {"file": "src/collision/AABB.js", "line": 271, "description": "Get the representation of an AABB in the global frame.", "itemtype": "method", "name": "toWorldFrame", "params": [{"name": "frame", "description": "", "type": "Transform"}, {"name": "target", "description": "", "type": "AABB"}], "return": {"description": "The \"target\" AABB object.", "type": "AABB"}, "class": "AABB"}, {"file": "src/collision/ArrayCollisionMatrix.js", "line": 10, "description": "The matrix storage", "itemtype": "property", "name": "matrix", "type": "{Array}", "class": "ArrayCollisionMatrix"}, {"file": "src/collision/ArrayCollisionMatrix.js", "line": 18, "description": "Get an element", "itemtype": "method", "name": "get", "params": [{"name": "i", "description": "", "type": "Number"}, {"name": "j", "description": "", "type": "Number"}], "return": {"description": "", "type": "Number"}, "class": "ArrayCollisionMatrix"}, {"file": "src/collision/ArrayCollisionMatrix.js", "line": 36, "description": "Set an element", "itemtype": "method", "name": "set", "params": [{"name": "i", "description": "", "type": "Number"}, {"name": "j", "description": "", "type": "Number"}, {"name": "value", "description": "", "type": "Number"}], "class": "ArrayCollisionMatrix"}, {"file": "src/collision/ArrayCollisionMatrix.js", "line": 54, "description": "Sets all elements to zero", "itemtype": "method", "name": "reset", "class": "ArrayCollisionMatrix"}, {"file": "src/collision/ArrayCollisionMatrix.js", "line": 64, "description": "Sets the max number of objects", "itemtype": "method", "name": "setNumObjects", "params": [{"name": "n", "description": "", "type": "Number"}], "class": "ArrayCollisionMatrix"}, {"file": "src/collision/Broadphase.js", "line": 16, "description": "The world to search for collisions in.", "itemtype": "property", "name": "world", "type": "{World}", "class": "Broadphase"}, {"file": "src/collision/Broadphase.js", "line": 23, "description": "If set to true, the broadphase uses bounding boxes for intersection test, else it uses bounding spheres.", "itemtype": "property", "name": "useBoundingBoxes", "type": "{<PERSON><PERSON><PERSON>}", "class": "Broadphase"}, {"file": "src/collision/Broadphase.js", "line": 30, "description": "Set to true if the objects in the world moved.", "itemtype": "property", "name": "dirty", "type": "Boolean", "class": "Broadphase"}, {"file": "src/collision/Broadphase.js", "line": 37, "description": "Get the collision pairs from the world", "itemtype": "method", "name": "collisionPairs", "params": [{"name": "world", "description": "The world to search in", "type": "World"}, {"name": "p1", "description": "Empty array to be filled with body objects", "type": "Array"}, {"name": "p2", "description": "Empty array to be filled with body objects", "type": "Array"}], "class": "Broadphase"}, {"file": "src/collision/Broadphase.js", "line": 48, "description": "Check if a body pair needs to be intersection tested at all.", "itemtype": "method", "name": "needBroadphaseCollision", "params": [{"name": "bodyA", "description": "", "type": "Body"}, {"name": "bodyB", "description": "", "type": "Body"}], "return": {"description": "", "type": "Bool"}, "class": "Broadphase"}, {"file": "src/collision/Broadphase.js", "line": 73, "description": "Check if the bounding volumes of two bodies intersect.", "itemtype": "method", "name": "intersectionTest", "params": [{"name": "bodyA", "description": "", "type": "Body"}, {"name": "bodyB", "description": "", "type": "Body"}, {"name": "pairs1", "description": "", "type": "Array"}, {"name": "pairs2", "description": "", "type": "Array"}], "class": "Broadphase"}, {"file": "src/collision/Broadphase.js", "line": 89, "description": "Check if the bounding spheres of two bodies are intersecting.", "itemtype": "method", "name": "doBoundingSphereBroadphase", "params": [{"name": "bodyA", "description": "", "type": "Body"}, {"name": "bodyB", "description": "", "type": "Body"}, {"name": "pairs1", "description": "bodyA is appended to this array if intersection", "type": "Array"}, {"name": "pairs2", "description": "bodyB is appended to this array if intersection", "type": "Array"}], "class": "Broadphase"}, {"file": "src/collision/Broadphase.js", "line": 112, "description": "Check if the bounding boxes of two bodies are intersecting.", "itemtype": "method", "name": "doBoundingBoxBroadphase", "params": [{"name": "bodyA", "description": "", "type": "Body"}, {"name": "bodyB", "description": "", "type": "Body"}, {"name": "pairs1", "description": "", "type": "Array"}, {"name": "pairs2", "description": "", "type": "Array"}], "class": "Broadphase"}, {"file": "src/collision/Broadphase.js", "line": 135, "description": "Removes duplicate pairs from the pair arrays.", "itemtype": "method", "name": "makePairsUnique", "params": [{"name": "pairs1", "description": "", "type": "Array"}, {"name": "pairs2", "description": "", "type": "Array"}], "class": "Broadphase"}, {"file": "src/collision/Broadphase.js", "line": 175, "description": "To be implemented by subcasses", "itemtype": "method", "name": "setWorld", "params": [{"name": "world", "description": "", "type": "World"}], "class": "Broadphase"}, {"file": "src/collision/Broadphase.js", "line": 183, "description": "Check if the bounding spheres of two bodies overlap.", "itemtype": "method", "name": "boundingSphereCheck", "params": [{"name": "bodyA", "description": "", "type": "Body"}, {"name": "bodyB", "description": "", "type": "Body"}], "return": {"description": "", "type": "Boolean"}, "class": "Broadphase"}, {"file": "src/collision/Broadphase.js", "line": 197, "description": "Returns all the bodies within the AABB.", "itemtype": "method", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "world", "description": "", "type": "World"}, {"name": "aabb", "description": "", "type": "AABB"}, {"name": "result", "description": "An array to store resulting bodies in.", "type": "Array"}], "return": {"description": "", "type": "Array"}, "class": "Broadphase"}, {"file": "src/collision/GridBroadphase.js", "line": 42, "description": "Get all the collision pairs in the physics world", "itemtype": "method", "name": "collisionPairs", "params": [{"name": "world", "description": "", "type": "World"}, {"name": "pairs1", "description": "", "type": "Array"}, {"name": "pairs2", "description": "", "type": "Array"}], "class": "GridBroadphase"}, {"file": "src/collision/NaiveBroadphase.js", "line": 19, "description": "Get all the collision pairs in the physics world", "itemtype": "method", "name": "collisionPairs", "params": [{"name": "world", "description": "", "type": "World"}, {"name": "pairs1", "description": "", "type": "Array"}, {"name": "pairs2", "description": "", "type": "Array"}], "class": "NaiveBroadphase"}, {"file": "src/collision/NaiveBroadphase.js", "line": 49, "description": "Returns all the bodies within an AABB.", "itemtype": "method", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "world", "description": "", "type": "World"}, {"name": "aabb", "description": "", "type": "AABB"}, {"name": "result", "description": "An array to store resulting bodies in.", "type": "Array"}], "return": {"description": "", "type": "Array"}, "class": "NaiveBroadphase"}, {"file": "src/collision/ObjectCollisionMatrix.js", "line": 10, "description": "The matrix storage", "itemtype": "property", "name": "matrix", "type": "{Object}", "class": "ObjectCollisionMatrix"}, {"file": "src/collision/ObjectCollisionMatrix.js", "line": 18, "itemtype": "method", "name": "get", "params": [{"name": "i", "description": "", "type": "Number"}, {"name": "j", "description": "", "type": "Number"}], "return": {"description": "", "type": "Number"}, "class": "ObjectCollisionMatrix"}, {"file": "src/collision/ObjectCollisionMatrix.js", "line": 35, "itemtype": "method", "name": "set", "params": [{"name": "i", "description": "", "type": "Number"}, {"name": "j", "description": "", "type": "Number"}, {"name": "value", "description": "", "type": "Number"}], "class": "ObjectCollisionMatrix"}, {"file": "src/collision/ObjectCollisionMatrix.js", "line": 57, "description": "Empty the matrix", "itemtype": "method", "name": "reset", "class": "ObjectCollisionMatrix"}, {"file": "src/collision/ObjectCollisionMatrix.js", "line": 65, "description": "Set max number of objects", "itemtype": "method", "name": "setNumObjects", "params": [{"name": "n", "description": "", "type": "Number"}], "class": "ObjectCollisionMatrix"}, {"file": "src/collision/Ray.js", "line": 20, "itemtype": "property", "name": "from", "type": "Vec3", "class": "<PERSON>"}, {"file": "src/collision/Ray.js", "line": 25, "itemtype": "property", "name": "to", "type": "Vec3", "class": "<PERSON>"}, {"file": "src/collision/Ray.js", "line": 30, "access": "private", "tagname": "", "itemtype": "property", "name": "_direction", "type": "Vec3", "class": "<PERSON>"}, {"file": "src/collision/Ray.js", "line": 36, "description": "The precision of the ray. Used when checking parallelity etc.", "itemtype": "property", "name": "precision", "type": "Number", "class": "<PERSON>"}, {"file": "src/collision/Ray.js", "line": 42, "description": "Set to true if you want the Ray to take .collisionResponse flags into account on bodies and shapes.", "itemtype": "property", "name": "checkCollisionResponse", "type": "Boolean", "class": "<PERSON>"}, {"file": "src/collision/Ray.js", "line": 48, "description": "If set to true, the ray skips any hits with normal.dot(rayDirection) < 0.", "itemtype": "property", "name": "skipBackfaces", "type": "Boolean", "class": "<PERSON>"}, {"file": "src/collision/Ray.js", "line": 54, "itemtype": "property", "name": "collisionFilterMask", "type": "Number", "default": "-1", "class": "<PERSON>"}, {"file": "src/collision/Ray.js", "line": 60, "itemtype": "property", "name": "collisionFilterGroup", "type": "Number", "default": "-1", "class": "<PERSON>"}, {"file": "src/collision/Ray.js", "line": 66, "description": "The intersection mode. Should be Ray.ANY, Ray.ALL or Ray.CLOSEST.", "itemtype": "property", "name": "mode", "type": "Number", "class": "<PERSON>"}, {"file": "src/collision/Ray.js", "line": 72, "description": "Current result object.", "itemtype": "property", "name": "result", "type": "RaycastResult", "class": "<PERSON>"}, {"file": "src/collision/Ray.js", "line": 78, "description": "Will be set to true during intersectWorld() if the ray hit anything.", "itemtype": "property", "name": "hasHit", "type": "Boolean", "class": "<PERSON>"}, {"file": "src/collision/Ray.js", "line": 84, "description": "Current, user-provided result callback. Will be used if mode is Ray.ALL.", "itemtype": "property", "name": "callback", "type": "Function", "class": "<PERSON>"}, {"file": "src/collision/Ray.js", "line": 99, "description": "Do itersection against all bodies in the given World.", "itemtype": "method", "name": "intersectWorld", "params": [{"name": "world", "description": "", "type": "World"}, {"name": "options", "description": "", "type": "Object"}], "return": {"description": "True if the ray hit anything, otherwise false.", "type": "Boolean"}, "class": "<PERSON>"}, {"file": "src/collision/Ray.js", "line": 157, "description": "Shoot a ray at a body, get back information about the hit.", "itemtype": "method", "name": "intersectBody", "access": "private", "tagname": "", "params": [{"name": "body", "description": "", "type": "Body"}, {"name": "result", "description": "Deprecated - set the result property of the Ray instead.", "type": "RaycastResult", "optional": true}], "class": "<PERSON>"}, {"file": "src/collision/Ray.js", "line": 208, "itemtype": "method", "name": "intersectBodies", "params": [{"name": "bodies", "description": "An array of Body objects.", "type": "Array"}, {"name": "result", "description": "Deprecated", "type": "RaycastResult", "optional": true}], "class": "<PERSON>"}, {"file": "src/collision/Ray.js", "line": 224, "description": "Updates the _direction vector.", "access": "private", "tagname": "", "itemtype": "method", "name": "_updateDirection", "class": "<PERSON>"}, {"file": "src/collision/Ray.js", "line": 234, "itemtype": "method", "name": "intersectShape", "access": "private", "tagname": "", "params": [{"name": "shape", "description": "", "type": "<PERSON><PERSON><PERSON>"}, {"name": "quat", "description": "", "type": "Quaternion"}, {"name": "position", "description": "", "type": "Vec3"}, {"name": "body", "description": "", "type": "Body"}], "class": "<PERSON>"}, {"file": "src/collision/Ray.js", "line": 269, "itemtype": "method", "name": "intersectBox", "access": "private", "tagname": "", "params": [{"name": "shape", "description": "", "type": "<PERSON><PERSON><PERSON>"}, {"name": "quat", "description": "", "type": "Quaternion"}, {"name": "position", "description": "", "type": "Vec3"}, {"name": "body", "description": "", "type": "Body"}], "class": "<PERSON>"}, {"file": "src/collision/Ray.js", "line": 282, "itemtype": "method", "name": "intersectPlane", "access": "private", "tagname": "", "params": [{"name": "shape", "description": "", "type": "<PERSON><PERSON><PERSON>"}, {"name": "quat", "description": "", "type": "Quaternion"}, {"name": "position", "description": "", "type": "Vec3"}, {"name": "body", "description": "", "type": "Body"}], "class": "<PERSON>"}, {"file": "src/collision/Ray.js", "line": 334, "description": "Get the world AABB of the ray.", "itemtype": "method", "name": "getAABB", "params": [{"name": "aabb", "description": "", "type": "AABB"}], "class": "<PERSON>"}, {"file": "src/collision/Ray.js", "line": 354, "itemtype": "method", "name": "intersectHeightfield", "access": "private", "tagname": "", "params": [{"name": "shape", "description": "", "type": "<PERSON><PERSON><PERSON>"}, {"name": "quat", "description": "", "type": "Quaternion"}, {"name": "position", "description": "", "type": "Vec3"}, {"name": "body", "description": "", "type": "Body"}], "class": "<PERSON>"}, {"file": "src/collision/Ray.js", "line": 439, "itemtype": "method", "name": "intersectSphere", "access": "private", "tagname": "", "params": [{"name": "shape", "description": "", "type": "<PERSON><PERSON><PERSON>"}, {"name": "quat", "description": "", "type": "Quaternion"}, {"name": "position", "description": "", "type": "Vec3"}, {"name": "body", "description": "", "type": "Body"}], "class": "<PERSON>"}, {"file": "src/collision/Ray.js", "line": 505, "itemtype": "method", "name": "intersectConvex", "access": "private", "tagname": "", "params": [{"name": "shape", "description": "", "type": "<PERSON><PERSON><PERSON>"}, {"name": "quat", "description": "", "type": "Quaternion"}, {"name": "position", "description": "", "type": "Vec3"}, {"name": "body", "description": "", "type": "Body"}, {"name": "options", "description": "", "type": "Object", "optional": true, "props": [{"name": "faceList", "description": "", "type": "Array", "optional": true}]}], "class": "<PERSON>"}, {"file": "src/collision/Ray.js", "line": 623, "itemtype": "method", "name": "intersectTrimesh", "access": "private", "tagname": "", "params": [{"name": "shape", "description": "", "type": "<PERSON><PERSON><PERSON>"}, {"name": "quat", "description": "", "type": "Quaternion"}, {"name": "position", "description": "", "type": "Vec3"}, {"name": "body", "description": "", "type": "Body"}, {"name": "options", "description": "", "type": "Object", "optional": true}], "todo": ["Optimize by transforming the world to local space first.", "Use Octree lookup"], "class": "<PERSON>"}, {"file": "src/collision/Ray.js", "line": 738, "itemtype": "method", "name": "reportIntersection", "access": "private", "tagname": "", "params": [{"name": "normal", "description": "", "type": "Vec3"}, {"name": "hitPointWorld", "description": "", "type": "Vec3"}, {"name": "shape", "description": "", "type": "<PERSON><PERSON><PERSON>"}, {"name": "body", "description": "", "type": "Body"}], "return": {"description": "True if the intersections should continue", "type": "Boolean"}, "class": "<PERSON>"}, {"file": "src/collision/RaycastResult.js", "line": 12, "itemtype": "property", "name": "rayFromWorld", "type": "Vec3", "class": "RaycastResult"}, {"file": "src/collision/RaycastResult.js", "line": 17, "itemtype": "property", "name": "rayToWorld", "type": "Vec3", "class": "RaycastResult"}, {"file": "src/collision/RaycastResult.js", "line": 22, "itemtype": "property", "name": "hitNormalWorld", "type": "Vec3", "class": "RaycastResult"}, {"file": "src/collision/RaycastResult.js", "line": 27, "itemtype": "property", "name": "hitPointWorld", "type": "Vec3", "class": "RaycastResult"}, {"file": "src/collision/RaycastResult.js", "line": 32, "itemtype": "property", "name": "hasHit", "type": "Boolean", "class": "RaycastResult"}, {"file": "src/collision/RaycastResult.js", "line": 37, "description": "The hit shape, or null.", "itemtype": "property", "name": "shape", "type": "<PERSON><PERSON><PERSON>", "class": "RaycastResult"}, {"file": "src/collision/RaycastResult.js", "line": 43, "description": "The hit body, or null.", "itemtype": "property", "name": "body", "type": "Body", "class": "RaycastResult"}, {"file": "src/collision/RaycastResult.js", "line": 49, "description": "The index of the hit triangle, if the hit shape was a trimesh.", "itemtype": "property", "name": "hitFaceIndex", "type": "Number", "default": "-1", "class": "RaycastResult"}, {"file": "src/collision/RaycastResult.js", "line": 56, "description": "Distance to the hit. Will be set to -1 if there was no hit.", "itemtype": "property", "name": "distance", "type": "Number", "default": "-1", "class": "RaycastResult"}, {"file": "src/collision/RaycastResult.js", "line": 63, "description": "If the ray should stop traversing the bodies.", "access": "private", "tagname": "", "itemtype": "property", "name": "_shouldStop", "type": "Boolean", "default": "false", "class": "RaycastResult"}, {"file": "src/collision/RaycastResult.js", "line": 72, "description": "Reset all result data.", "itemtype": "method", "name": "reset", "class": "RaycastResult"}, {"file": "src/collision/RaycastResult.js", "line": 89, "itemtype": "method", "name": "abort", "class": "RaycastResult"}, {"file": "src/collision/RaycastResult.js", "line": 96, "itemtype": "method", "name": "set", "params": [{"name": "rayFromWorld", "description": "", "type": "Vec3"}, {"name": "rayToWorld", "description": "", "type": "Vec3"}, {"name": "hitNormalWorld", "description": "", "type": "Vec3"}, {"name": "hitPointWorld", "description": "", "type": "Vec3"}, {"name": "shape", "description": "", "type": "<PERSON><PERSON><PERSON>"}, {"name": "body", "description": "", "type": "Body"}, {"name": "distance", "description": "", "type": "Number"}], "class": "RaycastResult"}, {"file": "src/collision/SAPBroadphase.js", "line": 17, "description": "List of bodies currently in the broadphase.", "itemtype": "property", "name": "axisList", "type": "{Array}", "class": "SAPBroadphase"}, {"file": "src/collision/SAPBroadphase.js", "line": 24, "description": "The world to search in.", "itemtype": "property", "name": "world", "type": "{World}", "class": "SAPBroadphase"}, {"file": "src/collision/SAPBroadphase.js", "line": 31, "description": "Axis to sort the bodies along. Set to 0 for x axis, and 1 for y axis. For best performance, choose an axis that the bodies are spread out more on.", "itemtype": "property", "name": "axisIndex", "type": "{Number}", "class": "SAPBroadphase"}, {"file": "src/collision/SAPBroadphase.js", "line": 57, "description": "Change the world", "itemtype": "method", "name": "setWorld", "params": [{"name": "world", "description": "", "type": "World"}], "class": "SAPBroadphase"}, {"file": "src/collision/SAPBroadphase.js", "line": 83, "static": 1, "itemtype": "method", "name": "insertionSortX", "params": [{"name": "a", "description": "", "type": "Array"}], "return": {"description": "", "type": "Array"}, "class": "SAPBroadphase"}, {"file": "src/collision/SAPBroadphase.js", "line": 103, "static": 1, "itemtype": "method", "name": "insertionSortY", "params": [{"name": "a", "description": "", "type": "Array"}], "return": {"description": "", "type": "Array"}, "class": "SAPBroadphase"}, {"file": "src/collision/SAPBroadphase.js", "line": 123, "static": 1, "itemtype": "method", "name": "insertionSortZ", "params": [{"name": "a", "description": "", "type": "Array"}], "return": {"description": "", "type": "Array"}, "class": "SAPBroadphase"}, {"file": "src/collision/SAPBroadphase.js", "line": 143, "description": "Collect all collision pairs", "itemtype": "method", "name": "collisionPairs", "params": [{"name": "world", "description": "", "type": "World"}, {"name": "p1", "description": "", "type": "Array"}, {"name": "p2", "description": "", "type": "Array"}], "class": "SAPBroadphase"}, {"file": "src/collision/SAPBroadphase.js", "line": 204, "description": "Check if the bounds of two bodies overlap, along the given SAP axis.", "static": 1, "itemtype": "method", "name": "checkBounds", "params": [{"name": "bi", "description": "", "type": "Body"}, {"name": "bj", "description": "", "type": "Body"}, {"name": "axisIndex", "description": "", "type": "Number"}], "return": {"description": "", "type": "Boolean"}, "class": "SAPBroadphase"}, {"file": "src/collision/SAPBroadphase.js", "line": 238, "description": "Computes the variance of the body positions and estimates the best\naxis to use. Will automatically set property .axisIndex.", "itemtype": "method", "name": "autoDetectAxis", "class": "SAPBroadphase"}, {"file": "src/collision/SAPBroadphase.js", "line": 287, "description": "Returns all the bodies within an AABB.", "itemtype": "method", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "world", "description": "", "type": "World"}, {"name": "aabb", "description": "", "type": "AABB"}, {"name": "result", "description": "An array to store resulting bodies in.", "type": "Array"}], "return": {"description": "", "type": "Array"}, "class": "SAPBroadphase"}, {"file": "src/constraints/ConeTwistConstraint.js", "line": 40, "itemtype": "property", "name": "coneEquation", "type": "ConeEquation", "class": "ConeTwistConstraint"}, {"file": "src/constraints/ConeTwistConstraint.js", "line": 45, "itemtype": "property", "name": "twistEquation", "type": "RotationalEquation", "class": "ConeTwistConstraint"}, {"file": "src/constraints/Constraint.js", "line": 22, "description": "Equations to be solved in this constraint", "itemtype": "property", "name": "equations", "type": "{Array}", "class": "Constraint"}, {"file": "src/constraints/Constraint.js", "line": 29, "itemtype": "property", "name": "bodyA", "type": "Body", "class": "Constraint"}, {"file": "src/constraints/Constraint.js", "line": 34, "itemtype": "property", "name": "bodyB", "type": "Body", "class": "Constraint"}, {"file": "src/constraints/Constraint.js", "line": 39, "itemtype": "property", "name": "id", "type": "Number", "class": "Constraint"}, {"file": "src/constraints/Constraint.js", "line": 44, "description": "Set to true if you want the bodies to collide when they are connected.", "itemtype": "property", "name": "collideConnected", "type": "{boolean}", "class": "Constraint"}, {"file": "src/constraints/Constraint.js", "line": 61, "description": "Update all the equations with data.", "itemtype": "method", "name": "update", "class": "Constraint"}, {"file": "src/constraints/Constraint.js", "line": 69, "description": "Enables all equations in the constraint.", "itemtype": "method", "name": "enable", "class": "Constraint"}, {"file": "src/constraints/Constraint.js", "line": 80, "description": "Disables all equations in the constraint.", "itemtype": "method", "name": "disable", "class": "Constraint"}, {"file": "src/constraints/DistanceConstraint.js", "line": 28, "itemtype": "property", "name": "distance", "type": "Number", "class": "DistanceConstraint"}, {"file": "src/constraints/DistanceConstraint.js", "line": 33, "itemtype": "property", "name": "distanceEquation", "type": "ContactEquation", "class": "DistanceConstraint"}, {"file": "src/constraints/HingeConstraint.js", "line": 33, "description": "Rotation axis, defined locally in bodyA.", "itemtype": "property", "name": "axisA", "type": "Vec3", "class": "HingeConstraint"}, {"file": "src/constraints/HingeConstraint.js", "line": 40, "description": "Rotation axis, defined locally in bodyB.", "itemtype": "property", "name": "axisB", "type": "Vec3", "class": "HingeConstraint"}, {"file": "src/constraints/HingeConstraint.js", "line": 47, "itemtype": "property", "name": "rotationalEquation1", "type": "RotationalEquation", "class": "HingeConstraint"}, {"file": "src/constraints/HingeConstraint.js", "line": 52, "itemtype": "property", "name": "rotationalEquation2", "type": "RotationalEquation", "class": "HingeConstraint"}, {"file": "src/constraints/HingeConstraint.js", "line": 57, "itemtype": "property", "name": "motorEquation", "type": "RotationalMotorEquation", "class": "HingeConstraint"}, {"file": "src/constraints/HingeConstraint.js", "line": 73, "itemtype": "method", "name": "enableMotor", "class": "HingeConstraint"}, {"file": "src/constraints/HingeConstraint.js", "line": 80, "itemtype": "method", "name": "disableMotor", "class": "HingeConstraint"}, {"file": "src/constraints/HingeConstraint.js", "line": 87, "itemtype": "method", "name": "setMotorSpeed", "params": [{"name": "speed", "description": "", "type": "Number"}], "class": "HingeConstraint"}, {"file": "src/constraints/HingeConstraint.js", "line": 95, "itemtype": "method", "name": "setMotorMaxForce", "params": [{"name": "max<PERSON><PERSON>ce", "description": "", "type": "Number"}], "class": "HingeConstraint"}, {"file": "src/constraints/LockConstraint.js", "line": 35, "itemtype": "property", "name": "rotationalEquation1", "type": "RotationalEquation", "class": "LockConstraint"}, {"file": "src/constraints/LockConstraint.js", "line": 40, "itemtype": "property", "name": "rotationalEquation2", "type": "RotationalEquation", "class": "LockConstraint"}, {"file": "src/constraints/LockConstraint.js", "line": 45, "itemtype": "property", "name": "rotationalEquation3", "type": "RotationalEquation", "class": "LockConstraint"}, {"file": "src/constraints/PointToPointConstraint.js", "line": 37, "description": "Pivot, defined locally in bodyA.", "itemtype": "property", "name": "pivotA", "type": "Vec3", "class": "PointToPointConstraint"}, {"file": "src/constraints/PointToPointConstraint.js", "line": 43, "description": "Pivot, defined locally in bodyB.", "itemtype": "property", "name": "pivotB", "type": "Vec3", "class": "PointToPointConstraint"}, {"file": "src/constraints/PointToPointConstraint.js", "line": 49, "itemtype": "property", "name": "equationX", "type": "ContactEquation", "class": "PointToPointConstraint"}, {"file": "src/constraints/PointToPointConstraint.js", "line": 54, "itemtype": "property", "name": "equationY", "type": "ContactEquation", "class": "PointToPointConstraint"}, {"file": "src/constraints/PointToPointConstraint.js", "line": 59, "itemtype": "property", "name": "equationZ", "type": "ContactEquation", "class": "PointToPointConstraint"}, {"file": "src/demo/Demo.js", "line": 209, "description": "Add a scene to the demo app", "itemtype": "method", "name": "addScene", "params": [{"name": "title", "description": "Title of the scene", "type": "String"}, {"name": "initfunc", "description": "A function that takes one argument, app, and initializes a physics scene. The function runs app.setWorld(body), app.addVisual(body), app.removeVisual(body) etc.", "type": "Function"}], "class": "Demo"}, {"file": "src/demo/Demo.js", "line": 230, "description": "Restarts the current scene", "itemtype": "method", "name": "restartCurrentScene", "class": "Demo"}, {"file": "src/equations/ConeEquation.js", "line": 29, "description": "The cone angle to keep", "itemtype": "property", "name": "angle", "type": "Number", "class": "ConeEquation"}, {"file": "src/equations/ContactEquation.js", "line": 20, "itemtype": "property", "name": "restitution", "type": "{Number}", "class": "ContactEquation"}, {"file": "src/equations/ContactEquation.js", "line": 26, "description": "World-oriented vector that goes from the center of bi to the contact point.", "itemtype": "property", "name": "ri", "type": "Vec3", "class": "ContactEquation"}, {"file": "src/equations/ContactEquation.js", "line": 32, "description": "World-oriented vector that starts in body j position and goes to the contact point.", "itemtype": "property", "name": "rj", "type": "Vec3", "class": "ContactEquation"}, {"file": "src/equations/ContactEquation.js", "line": 38, "description": "Contact normal, pointing out of body i.", "itemtype": "property", "name": "ni", "type": "Vec3", "class": "ContactEquation"}, {"file": "src/equations/ContactEquation.js", "line": 113, "description": "Get the current relative velocity in the contact point.", "itemtype": "method", "name": "getImpactVelocityAlongNormal", "return": {"description": "", "type": "Number"}, "class": "ContactEquation"}, {"file": "src/equations/Equation.js", "line": 19, "itemtype": "property", "name": "minForce", "type": "Number", "class": "Equation"}, {"file": "src/equations/Equation.js", "line": 24, "itemtype": "property", "name": "max<PERSON><PERSON>ce", "type": "Number", "class": "Equation"}, {"file": "src/equations/Equation.js", "line": 29, "itemtype": "property", "name": "bi", "type": "{Body}", "class": "Equation"}, {"file": "src/equations/Equation.js", "line": 35, "itemtype": "property", "name": "bj", "type": "{Body}", "class": "Equation"}, {"file": "src/equations/Equation.js", "line": 41, "description": "SPOOK parameter", "itemtype": "property", "name": "a", "type": "Number", "class": "Equation"}, {"file": "src/equations/Equation.js", "line": 47, "description": "SPOOK parameter", "itemtype": "property", "name": "b", "type": "Number", "class": "Equation"}, {"file": "src/equations/Equation.js", "line": 53, "description": "SPOOK parameter", "itemtype": "property", "name": "eps", "type": "Number", "class": "Equation"}, {"file": "src/equations/Equation.js", "line": 59, "itemtype": "property", "name": "jacobianElementA", "type": "<PERSON><PERSON><PERSON>", "class": "Equation"}, {"file": "src/equations/Equation.js", "line": 64, "itemtype": "property", "name": "jacobianElementB", "type": "<PERSON><PERSON><PERSON>", "class": "Equation"}, {"file": "src/equations/Equation.js", "line": 69, "itemtype": "property", "name": "enabled", "type": "Boolean", "default": "true", "class": "Equation"}, {"file": "src/equations/Equation.js", "line": 82, "description": "Recalculates a,b,eps.", "itemtype": "method", "name": "setSpookParams", "class": "Equation"}, {"file": "src/equations/Equation.js", "line": 95, "description": "Computes the RHS of the SPOOK equation", "itemtype": "method", "name": "computeB", "return": {"description": "", "type": "Number"}, "class": "Equation"}, {"file": "src/equations/Equation.js", "line": 107, "description": "Computes G*q, where q are the generalized body coordinates", "itemtype": "method", "name": "computeGq", "return": {"description": "", "type": "Number"}, "class": "Equation"}, {"file": "src/equations/Equation.js", "line": 124, "description": "Computes G*W, where W are the body velocities", "itemtype": "method", "name": "computeGW", "return": {"description": "", "type": "Number"}, "class": "Equation"}, {"file": "src/equations/Equation.js", "line": 142, "description": "Computes G*Wlambda, where W are the body velocities", "itemtype": "method", "name": "computeGWlambda", "return": {"description": "", "type": "Number"}, "class": "Equation"}, {"file": "src/equations/Equation.js", "line": 159, "description": "Computes G*inv(M)*f, where M is the mass matrix with diagonal blocks for each body, and f are the forces on the bodies.", "itemtype": "method", "name": "computeGiMf", "return": {"description": "", "type": "Number"}, "class": "Equation"}, {"file": "src/equations/Equation.js", "line": 191, "description": "Computes G*inv(M)*G'", "itemtype": "method", "name": "computeGiMGt", "return": {"description": "", "type": "Number"}, "class": "Equation"}, {"file": "src/equations/Equation.js", "line": 228, "description": "Add constraint velocity to the bodies.", "itemtype": "method", "name": "addToWlambda", "params": [{"name": "deltalambda", "description": "", "type": "Number"}], "class": "Equation"}, {"file": "src/equations/Equation.js", "line": 262, "description": "Compute the denominator part of the SPOOK equation: C = G*inv(M)*G' + eps", "itemtype": "method", "name": "computeInvC", "params": [{"name": "eps", "description": "", "type": "Number"}], "return": {"description": "", "type": "Number"}, "class": "Equation"}, {"file": "src/equations/RotationalMotorEquation.js", "line": 21, "description": "World oriented rotational axis", "itemtype": "property", "name": "axisA", "type": "Vec3", "class": "RotationalMotorEquation"}, {"file": "src/equations/RotationalMotorEquation.js", "line": 27, "description": "World oriented rotational axis", "itemtype": "property", "name": "axisB", "type": "Vec3", "class": "RotationalMotorEquation"}, {"file": "src/equations/RotationalMotorEquation.js", "line": 33, "description": "Motor velocity", "itemtype": "property", "name": "targetVelocity", "type": "Number", "class": "RotationalMotorEquation"}, {"file": "src/material/ContactMaterial.js", "line": 29, "description": "Identifier of this material", "itemtype": "property", "name": "id", "type": "Number", "class": "ContactMaterial"}, {"file": "src/material/ContactMaterial.js", "line": 35, "description": "Participating materials", "itemtype": "property", "name": "materials", "type": "Array", "todo": ["Should be .materialA and .materialB instead"], "class": "ContactMaterial"}, {"file": "src/material/ContactMaterial.js", "line": 42, "description": "Friction coefficient", "itemtype": "property", "name": "friction", "type": "Number", "class": "ContactMaterial"}, {"file": "src/material/ContactMaterial.js", "line": 48, "description": "Restitution coefficient", "itemtype": "property", "name": "restitution", "type": "Number", "class": "ContactMaterial"}, {"file": "src/material/ContactMaterial.js", "line": 54, "description": "Stiffness of the produced contact equations", "itemtype": "property", "name": "contactEquationStiffness", "type": "Number", "class": "ContactMaterial"}, {"file": "src/material/ContactMaterial.js", "line": 60, "description": "Relaxation time of the produced contact equations", "itemtype": "property", "name": "contactEquationRelaxation", "type": "Number", "class": "ContactMaterial"}, {"file": "src/material/ContactMaterial.js", "line": 66, "description": "Stiffness of the produced friction equations", "itemtype": "property", "name": "frictionEquationStiffness", "type": "Number", "class": "ContactMaterial"}, {"file": "src/material/ContactMaterial.js", "line": 72, "description": "Relaxation time of the produced friction equations", "itemtype": "property", "name": "frictionEquationRelaxation", "type": "Number", "class": "ContactMaterial"}, {"file": "src/material/Material.js", "line": 22, "itemtype": "property", "name": "name", "type": "{String}", "class": "Material"}, {"file": "src/material/Material.js", "line": 28, "description": "material id.", "itemtype": "property", "name": "id", "type": "{number}", "class": "Material"}, {"file": "src/material/Material.js", "line": 35, "description": "Friction for this material. If non-negative, it will be used instead of the friction given by ContactMaterials. If there's no matching ContactMaterial, the value from .defaultContactMaterial in the World will be used.", "itemtype": "property", "name": "friction", "type": "Number", "class": "Material"}, {"file": "src/material/Material.js", "line": 41, "description": "Restitution for this material. If non-negative, it will be used instead of the restitution given by ContactMaterials. If there's no matching ContactMaterial, the value from .defaultContactMaterial in the World will be used.", "itemtype": "property", "name": "restitution", "type": "Number", "class": "Material"}, {"file": "src/math/JacobianElement.js", "line": 12, "itemtype": "property", "name": "spatial", "type": "Vec3", "class": "<PERSON><PERSON><PERSON>"}, {"file": "src/math/JacobianElement.js", "line": 17, "itemtype": "property", "name": "rotational", "type": "Vec3", "class": "<PERSON><PERSON><PERSON>"}, {"file": "src/math/JacobianElement.js", "line": 23, "description": "Multiply with other JacobianElement", "itemtype": "method", "name": "multiplyElement", "params": [{"name": "element", "description": "", "type": "<PERSON><PERSON><PERSON>"}], "return": {"description": "", "type": "Number"}, "class": "<PERSON><PERSON><PERSON>"}, {"file": "src/math/JacobianElement.js", "line": 33, "description": "Multiply with two vectors", "itemtype": "method", "name": "multiplyVectors", "params": [{"name": "spatial", "description": "", "type": "Vec3"}, {"name": "rotational", "description": "", "type": "Vec3"}], "return": {"description": "", "type": "Number"}, "class": "<PERSON><PERSON><PERSON>"}, {"file": "src/math/Mat3.js", "line": 13, "description": "A vector of length 9, containing all matrix elements", "itemtype": "property", "name": "elements", "type": "Array", "class": "Mat3"}, {"file": "src/math/Mat3.js", "line": 24, "description": "Sets the matrix to identity", "itemtype": "method", "name": "identity", "todo": ["Should perhaps be renamed to setIdentity() to be more clear.", "Create another function that immediately creates an identity matrix eg. eye()"], "class": "Mat3"}, {"file": "src/math/Mat3.js", "line": 45, "description": "Set all elements to zero", "itemtype": "method", "name": "setZero", "class": "Mat3"}, {"file": "src/math/Mat3.js", "line": 62, "description": "Sets the matrix diagonal elements from a Vec3", "itemtype": "method", "name": "setTrace", "params": [{"name": "vec3", "description": "", "type": "Vec3"}], "class": "Mat3"}, {"file": "src/math/Mat3.js", "line": 74, "description": "Gets the matrix diagonal elements", "itemtype": "method", "name": "getTrace", "return": {"description": "", "type": "Vec3"}, "class": "Mat3"}, {"file": "src/math/Mat3.js", "line": 87, "description": "Matrix-Vector multiplication", "itemtype": "method", "name": "vmult", "params": [{"name": "v", "description": "The vector to multiply with", "type": "Vec3"}, {"name": "target", "description": "Optional, target to save the result in.", "type": "Vec3"}], "class": "Mat3"}, {"file": "src/math/Mat3.js", "line": 107, "description": "Matrix-scalar multiplication", "itemtype": "method", "name": "smult", "params": [{"name": "s", "description": "", "type": "Number"}], "class": "Mat3"}, {"file": "src/math/Mat3.js", "line": 118, "description": "Matrix multiplication", "itemtype": "method", "name": "mmult", "params": [{"name": "m", "description": "Matrix to multiply with from left side.", "type": "Mat3"}], "return": {"description": "The result.", "type": "Mat3"}, "class": "Mat3"}, {"file": "src/math/Mat3.js", "line": 138, "description": "Scale each column of the matrix", "itemtype": "method", "name": "scale", "params": [{"name": "v", "description": "", "type": "Vec3"}], "return": {"description": "The result.", "type": "Mat3"}, "class": "Mat3"}, {"file": "src/math/Mat3.js", "line": 156, "description": "Solve Ax=b", "itemtype": "method", "name": "solve", "params": [{"name": "b", "description": "The right hand side", "type": "Vec3"}, {"name": "target", "description": "Optional. Target vector to save in.", "type": "Vec3"}], "return": {"description": "The solution x", "type": "Vec3"}, "todo": ["should reuse arrays"], "class": "Mat3"}, {"file": "src/math/Mat3.js", "line": 227, "description": "Get an element in the matrix by index. Index starts at 0, not 1!!!", "itemtype": "method", "name": "e", "params": [{"name": "row", "description": "", "type": "Number"}, {"name": "column", "description": "", "type": "Number"}, {"name": "value", "description": "Optional. If provided, the matrix element will be set to this value.", "type": "Number"}], "return": {"description": "", "type": "Number"}, "class": "Mat3"}, {"file": "src/math/Mat3.js", "line": 244, "description": "Copy another matrix into this matrix object.", "itemtype": "method", "name": "copy", "params": [{"name": "source", "description": "", "type": "Mat3"}], "return": {"description": "this", "type": "Mat3"}, "class": "Mat3"}, {"file": "src/math/Mat3.js", "line": 257, "description": "Returns a string representation of the matrix.", "itemtype": "method", "name": "toString", "return": {"description": "string"}, "class": "Mat3"}, {"file": "src/math/Mat3.js", "line": 271, "description": "reverse the matrix", "itemtype": "method", "name": "reverse", "params": [{"name": "target", "description": "Optional. Target matrix to save in.", "type": "Mat3"}], "return": {"description": "The solution x", "type": "Mat3"}, "class": "Mat3"}, {"file": "src/math/Mat3.js", "line": 375, "description": "Set the matrix from a quaterion", "itemtype": "method", "name": "setRotationFromQuaternion", "params": [{"name": "q", "description": "", "type": "Quaternion"}], "class": "Mat3"}, {"file": "src/math/Mat3.js", "line": 403, "description": "Transpose the matrix", "itemtype": "method", "name": "transpose", "params": [{"name": "target", "description": "Where to store the result.", "type": "Mat3"}], "return": {"description": "The target Mat3, or a new Mat3 if target was omitted.", "type": "Mat3"}, "class": "Mat3"}, {"file": "src/math/Quaternion.js", "line": 16, "itemtype": "property", "name": "x", "type": "Number", "class": "Quaternion"}, {"file": "src/math/Quaternion.js", "line": 21, "itemtype": "property", "name": "y", "type": "Number", "class": "Quaternion"}, {"file": "src/math/Quaternion.js", "line": 26, "itemtype": "property", "name": "z", "type": "Number", "class": "Quaternion"}, {"file": "src/math/Quaternion.js", "line": 31, "description": "The multiplier of the real quaternion basis vector.", "itemtype": "property", "name": "w", "type": "Number", "class": "Quaternion"}, {"file": "src/math/Quaternion.js", "line": 38, "description": "Set the value of the quaternion.", "itemtype": "method", "name": "set", "params": [{"name": "x", "description": "", "type": "Number"}, {"name": "y", "description": "", "type": "Number"}, {"name": "z", "description": "", "type": "Number"}, {"name": "w", "description": "", "type": "Number"}], "class": "Quaternion"}, {"file": "src/math/Quaternion.js", "line": 53, "description": "Convert to a readable format", "itemtype": "method", "name": "toString", "return": {"description": "string"}, "class": "Quaternion"}, {"file": "src/math/Quaternion.js", "line": 62, "description": "Convert to an Array", "itemtype": "method", "name": "toArray", "return": {"description": "Array"}, "class": "Quaternion"}, {"file": "src/math/Quaternion.js", "line": 71, "description": "Set the quaternion components given an axis and an angle.", "itemtype": "method", "name": "setFromAxisAngle", "params": [{"name": "axis", "description": "", "type": "Vec3"}, {"name": "angle", "description": "in radians", "type": "Number"}], "class": "Quaternion"}, {"file": "src/math/Quaternion.js", "line": 85, "description": "Converts the quaternion to axis/angle representation.", "itemtype": "method", "name": "toAxisAngle", "params": [{"name": "targetAxis", "description": "Optional. A vector object to reuse for storing the axis.", "type": "Vec3"}], "return": {"description": "Array An array, first elemnt is the axis and the second is the angle in radians."}, "class": "Quaternion"}, {"file": "src/math/Quaternion.js", "line": 112, "description": "Set the quaternion value given two vectors. The resulting rotation will be the needed rotation to rotate u to v.", "itemtype": "method", "name": "setFromVectors", "params": [{"name": "u", "description": "", "type": "Vec3"}, {"name": "v", "description": "", "type": "Vec3"}], "class": "Quaternion"}, {"file": "src/math/Quaternion.js", "line": 135, "description": "Quaternion multiplication", "itemtype": "method", "name": "mult", "params": [{"name": "q", "description": "", "type": "Quaternion"}, {"name": "target", "description": "Optional.", "type": "Quaternion"}], "return": {"description": "", "type": "Quaternion"}, "class": "Quaternion"}, {"file": "src/math/Quaternion.js", "line": 164, "description": "Get the inverse quaternion rotation.", "itemtype": "method", "name": "inverse", "params": [{"name": "target", "description": "", "type": "Quaternion"}], "return": {"description": "", "type": "Quaternion"}, "class": "Quaternion"}, {"file": "src/math/Quaternion.js", "line": 184, "description": "Get the quaternion conjugate", "itemtype": "method", "name": "conjugate", "params": [{"name": "target", "description": "", "type": "Quaternion"}], "return": {"description": "", "type": "Quaternion"}, "class": "Quaternion"}, {"file": "src/math/Quaternion.js", "line": 201, "description": "Normalize the quaternion. Note that this changes the values of the quaternion.", "itemtype": "method", "name": "normalize", "class": "Quaternion"}, {"file": "src/math/Quaternion.js", "line": 221, "description": "Approximation of quaternion normalization. Works best when quat is already almost-normalized.", "itemtype": "method", "name": "normalizeFast", "see": ["http://jsperf.com/fast-quaternion-normalization"], "author": "unphased, https://github.com/unphased", "class": "Quaternion"}, {"file": "src/math/Quaternion.js", "line": 242, "description": "Multiply the quaternion by a vector", "itemtype": "method", "name": "vmult", "params": [{"name": "v", "description": "", "type": "Vec3"}, {"name": "target", "description": "Optional", "type": "Vec3"}], "return": {"description": "", "type": "Vec3"}, "class": "Quaternion"}, {"file": "src/math/Quaternion.js", "line": 274, "description": "Copies value of source to this quaternion.", "itemtype": "method", "name": "copy", "params": [{"name": "source", "description": "", "type": "Quaternion"}], "return": {"description": "this", "type": "Quaternion"}, "class": "Quaternion"}, {"file": "src/math/Quaternion.js", "line": 288, "description": "Convert the quaternion to euler angle representation. Order: YZX, as this page describes: http://www.euclideanspace.com/maths/standards/index.htm", "itemtype": "method", "name": "toEuler", "params": [{"name": "target", "description": "", "type": "Vec3"}, {"name": "string", "description": "order Three-character string e.g. \"YZX\", which also is default."}], "class": "Quaternion"}, {"file": "src/math/Quaternion.js", "line": 331, "description": "See http://www.mathworks.com/matlabcentral/fileexchange/20696-function-to-convert-between-dcm-euler-angles-quaternions-and-euler-vectors/content/SpinCalc.m", "itemtype": "method", "name": "setFromEuler", "params": [{"name": "x", "description": "", "type": "Number"}, {"name": "y", "description": "", "type": "Number"}, {"name": "z", "description": "", "type": "Number"}, {"name": "order", "description": "The order to apply angles: 'XYZ' or 'YXZ' or any other combination", "type": "String"}], "class": "Quaternion"}, {"file": "src/math/Transform.js", "line": 13, "itemtype": "property", "name": "position", "type": "Vec3", "class": "Transform"}, {"file": "src/math/Transform.js", "line": 21, "itemtype": "property", "name": "quaternion", "type": "Quaternion", "class": "Transform"}, {"file": "src/math/Transform.js", "line": 32, "static": 1, "itemtype": "method", "name": "pointToLocaFrame", "params": [{"name": "position", "description": "", "type": "Vec3"}, {"name": "quaternion", "description": "", "type": "Quaternion"}, {"name": "worldPoint", "description": "", "type": "Vec3"}, {"name": "result", "description": "", "type": "Vec3"}], "class": "Transform"}, {"file": "src/math/Transform.js", "line": 48, "description": "Get a global point in local transform coordinates.", "itemtype": "method", "name": "pointToLocal", "params": [{"name": "point", "description": "", "type": "Vec3"}, {"name": "result", "description": "", "type": "Vec3"}], "return": {"description": "The \"result\" vector object", "type": "Vec3"}, "class": "Transform"}, {"file": "src/math/Transform.js", "line": 59, "static": 1, "itemtype": "method", "name": "pointToWorldFrame", "params": [{"name": "position", "description": "", "type": "Vec3"}, {"name": "quaternion", "description": "", "type": "Vec3"}, {"name": "localPoint", "description": "", "type": "Vec3"}, {"name": "result", "description": "", "type": "Vec3"}], "class": "Transform"}, {"file": "src/math/Transform.js", "line": 74, "description": "Get a local point in global transform coordinates.", "itemtype": "method", "name": "pointToWorld", "params": [{"name": "point", "description": "", "type": "Vec3"}, {"name": "result", "description": "", "type": "Vec3"}], "return": {"description": "The \"result\" vector object", "type": "Vec3"}, "class": "Transform"}, {"file": "src/math/Vec3.js", "line": 18, "itemtype": "property", "name": "x", "type": "{Number}", "class": "Vec3"}, {"file": "src/math/Vec3.js", "line": 24, "itemtype": "property", "name": "y", "type": "{Number}", "class": "Vec3"}, {"file": "src/math/Vec3.js", "line": 30, "itemtype": "property", "name": "z", "type": "{Number}", "class": "Vec3"}, {"file": "src/math/Vec3.js", "line": 37, "static": 1, "itemtype": "property", "name": "ZERO", "type": "Vec3", "class": "Vec3"}, {"file": "src/math/Vec3.js", "line": 43, "static": 1, "itemtype": "property", "name": "UNIT_X", "type": "Vec3", "class": "Vec3"}, {"file": "src/math/Vec3.js", "line": 49, "static": 1, "itemtype": "property", "name": "UNIT_Y", "type": "Vec3", "class": "Vec3"}, {"file": "src/math/Vec3.js", "line": 55, "static": 1, "itemtype": "property", "name": "UNIT_Z", "type": "Vec3", "class": "Vec3"}, {"file": "src/math/Vec3.js", "line": 61, "description": "Vector cross product", "itemtype": "method", "name": "cross", "params": [{"name": "v", "description": "", "type": "Vec3"}, {"name": "target", "description": "Optional. Target to save in.", "type": "Vec3"}], "return": {"description": "", "type": "Vec3"}, "class": "Vec3"}, {"file": "src/math/Vec3.js", "line": 79, "description": "Set the vectors' 3 elements", "itemtype": "method", "name": "set", "params": [{"name": "x", "description": "", "type": "Number"}, {"name": "y", "description": "", "type": "Number"}, {"name": "z", "description": "", "type": "Number"}], "return": {"description": "Vec3"}, "class": "Vec3"}, {"file": "src/math/Vec3.js", "line": 94, "description": "Set all components of the vector to zero.", "itemtype": "method", "name": "setZero", "class": "Vec3"}, {"file": "src/math/Vec3.js", "line": 102, "description": "Vector addition", "itemtype": "method", "name": "vadd", "params": [{"name": "v", "description": "", "type": "Vec3"}, {"name": "target", "description": "Optional.", "type": "Vec3"}], "return": {"description": "", "type": "Vec3"}, "class": "Vec3"}, {"file": "src/math/Vec3.js", "line": 121, "description": "Vector subtraction", "itemtype": "method", "name": "vsub", "params": [{"name": "v", "description": "", "type": "Vec3"}, {"name": "target", "description": "Optional. Target to save in.", "type": "Vec3"}], "return": {"description": "", "type": "Vec3"}, "class": "Vec3"}, {"file": "src/math/Vec3.js", "line": 140, "description": "Get the cross product matrix a_cross from a vector, such that a x b = a_cross * b = c", "itemtype": "method", "name": "crossmat", "see": ["http://www8.cs.umu.se/kurser/TDBD24/VT06/lectures/Lecture6.pdf"], "return": {"description": "", "type": "Mat3"}, "class": "Vec3"}, {"file": "src/math/Vec3.js", "line": 152, "description": "Normalize the vector. Note that this changes the values in the vector.", "itemtype": "method", "name": "normalize", "return": {"description": "Returns the norm of the vector", "type": "Number"}, "class": "Vec3"}, {"file": "src/math/Vec3.js", "line": 174, "description": "Get the version of this vector that is of length 1.", "itemtype": "method", "name": "unit", "params": [{"name": "target", "description": "Optional target to save in", "type": "Vec3"}], "return": {"description": "Returns the unit vector", "type": "Vec3"}, "class": "Vec3"}, {"file": "src/math/Vec3.js", "line": 197, "description": "Get the length of the vector", "itemtype": "method", "name": "norm", "return": {"description": "", "type": "Number"}, "deprecated": true, "deprecationMessage": "Use .length() instead", "class": "Vec3"}, {"file": "src/math/Vec3.js", "line": 208, "description": "Get the length of the vector", "itemtype": "method", "name": "length", "return": {"description": "", "type": "Number"}, "class": "Vec3"}, {"file": "src/math/Vec3.js", "line": 215, "description": "Get the squared length of the vector", "itemtype": "method", "name": "norm2", "return": {"description": "", "type": "Number"}, "deprecated": true, "deprecationMessage": "Use .lengthSquared() instead.", "class": "Vec3"}, {"file": "src/math/Vec3.js", "line": 225, "description": "Get the squared length of the vector.", "itemtype": "method", "name": "lengthSquared", "return": {"description": "", "type": "Number"}, "class": "Vec3"}, {"file": "src/math/Vec3.js", "line": 232, "description": "Get distance from this point to another point", "itemtype": "method", "name": "distanceTo", "params": [{"name": "p", "description": "", "type": "Vec3"}], "return": {"description": "", "type": "Number"}, "class": "Vec3"}, {"file": "src/math/Vec3.js", "line": 246, "description": "Get squared distance from this point to another point", "itemtype": "method", "name": "distanceSquared", "params": [{"name": "p", "description": "", "type": "Vec3"}], "return": {"description": "", "type": "Number"}, "class": "Vec3"}, {"file": "src/math/Vec3.js", "line": 258, "description": "Multiply all the components of the vector with a scalar.", "deprecated": true, "deprecationMessage": "Use .scale() instead", "itemtype": "method", "name": "mult", "params": [{"name": "scalar", "description": "", "type": "Number"}, {"name": "target", "description": "The vector to save the result in.", "type": "Vec3"}], "return": {"description": "", "type": "Vec3"}, "class": "Vec3"}, {"file": "src/math/Vec3.js", "line": 278, "description": "Multiply the vector with a scalar.", "itemtype": "method", "name": "scale", "params": [{"name": "scalar", "description": "", "type": "Number"}, {"name": "target", "description": "", "type": "Vec3"}], "return": {"description": "", "type": "Vec3"}, "class": "Vec3"}, {"file": "src/math/Vec3.js", "line": 287, "description": "Calculate dot product", "itemtype": "method", "name": "dot", "params": [{"name": "v", "description": "", "type": "Vec3"}], "return": {"description": "", "type": "Number"}, "class": "Vec3"}, {"file": "src/math/Vec3.js", "line": 297, "itemtype": "method", "name": "isZero", "return": {"description": "bool"}, "class": "Vec3"}, {"file": "src/math/Vec3.js", "line": 305, "description": "Make the vector point in the opposite direction.", "itemtype": "method", "name": "negate", "params": [{"name": "target", "description": "Optional target to save in", "type": "Vec3"}], "return": {"description": "", "type": "Vec3"}, "class": "Vec3"}, {"file": "src/math/Vec3.js", "line": 319, "description": "Compute two artificial tangents to the vector", "itemtype": "method", "name": "tangents", "params": [{"name": "t1", "description": "Vector object to save the first tangent in", "type": "Vec3"}, {"name": "t2", "description": "Vector object to save the second tangent in", "type": "Vec3"}], "class": "Vec3"}, {"file": "src/math/Vec3.js", "line": 349, "description": "Converts to a more readable format", "itemtype": "method", "name": "toString", "return": {"description": "string"}, "class": "Vec3"}, {"file": "src/math/Vec3.js", "line": 358, "description": "Converts to an array", "itemtype": "method", "name": "toArray", "return": {"description": "Array"}, "class": "Vec3"}, {"file": "src/math/Vec3.js", "line": 367, "description": "Copies value of source to this vector.", "itemtype": "method", "name": "copy", "params": [{"name": "source", "description": "", "type": "Vec3"}], "return": {"description": "this", "type": "Vec3"}, "class": "Vec3"}, {"file": "src/math/Vec3.js", "line": 381, "description": "Do a linear interpolation between two vectors", "itemtype": "method", "name": "lerp", "params": [{"name": "v", "description": "", "type": "Vec3"}, {"name": "t", "description": "A number between 0 and 1. 0 will make this function return u, and 1 will make it return v. Numbers in between will generate a vector in between them.", "type": "Number"}, {"name": "target", "description": "", "type": "Vec3"}], "class": "Vec3"}, {"file": "src/math/Vec3.js", "line": 395, "description": "Check if a vector equals is almost equal to another one.", "itemtype": "method", "name": "almostEquals", "params": [{"name": "v", "description": "", "type": "Vec3"}, {"name": "precision", "description": "", "type": "Number"}], "return": {"description": "bool"}, "class": "Vec3"}, {"file": "src/math/Vec3.js", "line": 414, "description": "Check if a vector is almost zero", "itemtype": "method", "name": "almostZero", "params": [{"name": "precision", "description": "", "type": "Number"}], "class": "Vec3"}, {"file": "src/math/Vec3.js", "line": 433, "description": "Check if the vector is anti-parallel to another vector.", "itemtype": "method", "name": "isAntiparallelTo", "params": [{"name": "v", "description": "", "type": "Vec3"}, {"name": "precision", "description": "Set to zero for exact comparisons", "type": "Number"}], "return": {"description": "", "type": "Boolean"}, "class": "Vec3"}, {"file": "src/math/Vec3.js", "line": 445, "description": "Clone the vector", "itemtype": "method", "name": "clone", "return": {"description": "", "type": "Vec3"}, "class": "Vec3"}, {"file": "src/objects/Body.js", "line": 49, "description": "Reference to the world the body is living in", "itemtype": "property", "name": "world", "type": "{World}", "class": "Body"}, {"file": "src/objects/Body.js", "line": 56, "description": "Callback function that is used BEFORE stepping the system. Use it to apply forces, for example. Inside the function, \"this\" will refer to this Body object.", "itemtype": "property", "name": "preStep", "type": "{Function}", "deprecated": true, "deprecationMessage": "Use World events instead", "class": "Body"}, {"file": "src/objects/Body.js", "line": 64, "description": "Callback function that is used AFTER stepping the system. Inside the function, \"this\" will refer to this Body object.", "itemtype": "property", "name": "postStep", "type": "{Function}", "deprecated": true, "deprecationMessage": "Use World events instead", "class": "Body"}, {"file": "src/objects/Body.js", "line": 74, "itemtype": "property", "name": "collisionFilterGroup", "type": "Number", "class": "Body"}, {"file": "src/objects/Body.js", "line": 79, "itemtype": "property", "name": "collisionFilterMask", "type": "Number", "class": "Body"}, {"file": "src/objects/Body.js", "line": 84, "description": "Whether to produce contact forces when in contact with other bodies. Note that contacts will be generated, but they will be disabled.", "itemtype": "property", "name": "collisionResponse", "type": "Number", "class": "Body"}, {"file": "src/objects/Body.js", "line": 90, "itemtype": "property", "name": "position", "type": "{Vec3}", "class": "Body"}, {"file": "src/objects/Body.js", "line": 100, "itemtype": "property", "name": "previousPosition", "type": "Vec3", "class": "Body"}, {"file": "src/objects/Body.js", "line": 105, "description": "Initial position of the body", "itemtype": "property", "name": "initPosition", "type": "{Vec3}", "class": "Body"}, {"file": "src/objects/Body.js", "line": 112, "itemtype": "property", "name": "velocity", "type": "{Vec3}", "class": "Body"}, {"file": "src/objects/Body.js", "line": 122, "itemtype": "property", "name": "initVelocity", "type": "{Vec3}", "class": "Body"}, {"file": "src/objects/Body.js", "line": 128, "description": "Linear force on the body", "itemtype": "property", "name": "force", "type": "{Vec3}", "class": "Body"}, {"file": "src/objects/Body.js", "line": 137, "itemtype": "property", "name": "mass", "type": "{Number}", "default": "0", "class": "Body"}, {"file": "src/objects/Body.js", "line": 144, "itemtype": "property", "name": "invMass", "type": "{Number}", "class": "Body"}, {"file": "src/objects/Body.js", "line": 150, "itemtype": "property", "name": "material", "type": "{Material}", "class": "Body"}, {"file": "src/objects/Body.js", "line": 156, "itemtype": "property", "name": "linearDamping", "type": "{Number}", "class": "Body"}, {"file": "src/objects/Body.js", "line": 162, "description": "One of: Body.DYNAMIC, Body.STATIC and Body.KINEMATIC.", "itemtype": "property", "name": "type", "type": "{Number}", "class": "Body"}, {"file": "src/objects/Body.js", "line": 172, "description": "If true, the body will automatically fall to sleep.", "itemtype": "property", "name": "allowSleep", "type": "{<PERSON><PERSON><PERSON>}", "default": "true", "class": "Body"}, {"file": "src/objects/Body.js", "line": 180, "description": "Current sleep state.", "itemtype": "property", "name": "sleepState", "type": "{Number}", "class": "Body"}, {"file": "src/objects/Body.js", "line": 187, "description": "If the speed (the norm of the velocity) is smaller than this value, the body is considered sleepy.", "itemtype": "property", "name": "sleepSpeedLimit", "type": "{Number}", "default": "0.1", "class": "Body"}, {"file": "src/objects/Body.js", "line": 195, "description": "If the body has been sleepy for this sleepTimeLimit seconds, it is considered sleeping.", "itemtype": "property", "name": "sleepTimeLimit", "type": "{Number}", "default": "1", "class": "Body"}, {"file": "src/objects/Body.js", "line": 208, "description": "Rotational force on the body, around center of mass", "itemtype": "property", "name": "torque", "type": "Vec3", "class": "Body"}, {"file": "src/objects/Body.js", "line": 214, "description": "Orientation of the body", "itemtype": "property", "name": "quaternion", "type": "{Quaternion}", "class": "Body"}, {"file": "src/objects/Body.js", "line": 225, "itemtype": "property", "name": "initQuaternion", "type": "{Quaternion}", "class": "Body"}, {"file": "src/objects/Body.js", "line": 231, "itemtype": "property", "name": "angularVelocity", "type": "{Vec3}", "class": "Body"}, {"file": "src/objects/Body.js", "line": 241, "itemtype": "property", "name": "initAngularVelocity", "type": "{Vec3}", "class": "Body"}, {"file": "src/objects/Body.js", "line": 250, "itemtype": "property", "name": "shapes", "type": "{array}", "class": "Body"}, {"file": "src/objects/Body.js", "line": 256, "itemtype": "property", "name": "shapeOffsets", "type": "{array}", "class": "Body"}, {"file": "src/objects/Body.js", "line": 262, "itemtype": "property", "name": "shapeOrientations", "type": "{array}", "class": "Body"}, {"file": "src/objects/Body.js", "line": 268, "itemtype": "property", "name": "inertia", "type": "{Vec3}", "class": "Body"}, {"file": "src/objects/Body.js", "line": 274, "itemtype": "property", "name": "invInertia", "type": "Vec3", "class": "Body"}, {"file": "src/objects/Body.js", "line": 279, "itemtype": "property", "name": "invInertiaWorld", "type": "Mat3", "class": "Body"}, {"file": "src/objects/Body.js", "line": 286, "itemtype": "property", "name": "invInertiaSolve", "type": "Vec3", "class": "Body"}, {"file": "src/objects/Body.js", "line": 291, "itemtype": "property", "name": "invInertiaWorldSolve", "type": "Mat3", "class": "Body"}, {"file": "src/objects/Body.js", "line": 296, "description": "Set to true if you don't want the body to rotate. Make sure to run .updateMassProperties() after changing this.", "itemtype": "property", "name": "fixedRotation", "type": "Boolean", "default": "false", "class": "Body"}, {"file": "src/objects/Body.js", "line": 303, "itemtype": "property", "name": "angularDamping", "type": "Number", "class": "Body"}, {"file": "src/objects/Body.js", "line": 308, "itemtype": "property", "name": "aabb", "type": "{AABB}", "class": "Body"}, {"file": "src/objects/Body.js", "line": 314, "description": "Indicates if the AABB needs to be updated before use.", "itemtype": "property", "name": "aabbNeedsUpdate", "type": "{<PERSON><PERSON><PERSON>}", "class": "Body"}, {"file": "src/objects/Body.js", "line": 332, "description": "A dynamic body is fully simulated. Can be moved manually by the user, but normally they move according to forces. A dynamic body can collide with all body types. A dynamic body always has finite, non-zero mass.", "static": 1, "itemtype": "property", "name": "DYNAMIC", "type": "{Number}", "class": "Body"}, {"file": "src/objects/Body.js", "line": 340, "description": "A static body does not move during simulation and behaves as if it has infinite mass. Static bodies can be moved manually by setting the position of the body. The velocity of a static body is always zero. Static bodies do not collide with other static or kinematic bodies.", "static": 1, "itemtype": "property", "name": "STATIC", "type": "{Number}", "class": "Body"}, {"file": "src/objects/Body.js", "line": 348, "description": "A kinematic body moves under simulation according to its velocity. They do not respond to forces. They can be moved manually, but normally a kinematic body is moved by setting its velocity. A kinematic body behaves as if it has infinite mass. Kinematic bodies do not collide with other static or kinematic bodies.", "static": 1, "itemtype": "property", "name": "KINEMATIC", "type": "{Number}", "class": "Body"}, {"file": "src/objects/Body.js", "line": 358, "static": 1, "itemtype": "property", "name": "AWAKE", "type": "{number}", "class": "Body"}, {"file": "src/objects/Body.js", "line": 365, "static": 1, "itemtype": "property", "name": "SLEEPY", "type": "{number}", "class": "Body"}, {"file": "src/objects/Body.js", "line": 372, "static": 1, "itemtype": "property", "name": "SLEEPING", "type": "{number}", "class": "Body"}, {"file": "src/objects/Body.js", "line": 381, "description": "Wake the body up.", "itemtype": "method", "name": "wakeUp", "class": "Body"}, {"file": "src/objects/Body.js", "line": 393, "description": "Force body sleep", "itemtype": "method", "name": "sleep", "class": "Body"}, {"file": "src/objects/Body.js", "line": 411, "description": "Called every timestep to update internal sleep timer and change sleep state if needed.", "itemtype": "method", "name": "sleepTick", "params": [{"name": "time", "description": "The world time in seconds", "type": "Number"}], "class": "Body"}, {"file": "src/objects/Body.js", "line": 434, "description": "If the body is sleeping, it should be immovable / have infinite mass during solve. We solve it by having a separate \"solve mass\".", "itemtype": "method", "name": "updateSolveMassProperties", "class": "Body"}, {"file": "src/objects/Body.js", "line": 450, "description": "Convert a world point to local body frame.", "itemtype": "method", "name": "pointToLocalFrame", "params": [{"name": "worldPoint", "description": "", "type": "Vec3"}, {"name": "result", "description": "", "type": "Vec3"}], "return": {"description": "", "type": "Vec3"}, "class": "Body"}, {"file": "src/objects/Body.js", "line": 464, "description": "Convert a world vector to local body frame.", "itemtype": "method", "name": "vectorToLocalFrame", "params": [{"name": "worldPoint", "description": "", "type": "Vec3"}, {"name": "result", "description": "", "type": "Vec3"}], "return": {"description": "", "type": "Vec3"}, "class": "Body"}, {"file": "src/objects/Body.js", "line": 477, "description": "Convert a local body point to world frame.", "itemtype": "method", "name": "pointToWorldFrame", "params": [{"name": "localPoint", "description": "", "type": "Vec3"}, {"name": "result", "description": "", "type": "Vec3"}], "return": {"description": "", "type": "Vec3"}, "class": "Body"}, {"file": "src/objects/Body.js", "line": 491, "description": "Convert a local body point to world frame.", "itemtype": "method", "name": "vectorToWorldFrame", "params": [{"name": "localVector", "description": "", "type": "Vec3"}, {"name": "result", "description": "", "type": "Vec3"}], "return": {"description": "", "type": "Vec3"}, "class": "Body"}, {"file": "src/objects/Body.js", "line": 507, "description": "Add a shape to the body with a local offset and orientation.", "itemtype": "method", "name": "addShape", "params": [{"name": "shape", "description": "", "type": "<PERSON><PERSON><PERSON>"}, {"name": "offset", "description": "", "type": "Vec3"}, {"name": "quaternion", "description": "", "type": "Quaternion"}], "return": {"description": "The body object, for chainability.", "type": "Body"}, "class": "Body"}, {"file": "src/objects/Body.js", "line": 537, "description": "Update the bounding radius of the body. Should be done if any of the shapes are changed.", "itemtype": "method", "name": "updateBoundingRadius", "class": "Body"}, {"file": "src/objects/Body.js", "line": 562, "description": "Updates the .aabb", "itemtype": "method", "name": "computeAABB", "todo": ["rename to updateAABB()"], "class": "Body"}, {"file": "src/objects/Body.js", "line": 608, "description": "Update .inertiaWorld and .invInertiaWorld", "itemtype": "method", "name": "updateInertiaWorld", "class": "Body"}, {"file": "src/objects/Body.js", "line": 637, "description": "Apply force to a world point. This could for example be a point on the Body surface. Applying force this way will add to Body.force and Body.torque.", "itemtype": "method", "name": "applyForce", "params": [{"name": "force", "description": "The amount of force to add.", "type": "Vec3"}, {"name": "worldPoint", "description": "A world point to apply the force on.", "type": "Vec3"}], "class": "Body"}, {"file": "src/objects/Body.js", "line": 665, "description": "Apply force to a local point in the body.", "itemtype": "method", "name": "applyLocalForce", "params": [{"name": "force", "description": "The force vector to apply, defined locally in the body frame.", "type": "Vec3"}, {"name": "localPoint", "description": "A local point in the body to apply the force on.", "type": "Vec3"}], "class": "Body"}, {"file": "src/objects/Body.js", "line": 688, "description": "Apply impulse to a world point. This could for example be a point on the Body surface. An impulse is a force added to a body during a short period of time (impulse = force * time). Impulses will be added to Body.velocity and Body.angularVelocity.", "itemtype": "method", "name": "applyImpulse", "params": [{"name": "impulse", "description": "The amount of impulse to add.", "type": "Vec3"}, {"name": "worldPoint", "description": "A world point to apply the force on.", "type": "Vec3"}], "class": "Body"}, {"file": "src/objects/Body.js", "line": 729, "description": "Apply locally-defined impulse to a local point in the body.", "itemtype": "method", "name": "applyLocalImpulse", "params": [{"name": "force", "description": "The force vector to apply, defined locally in the body frame.", "type": "Vec3"}, {"name": "localPoint", "description": "A local point in the body to apply the force on.", "type": "Vec3"}], "class": "Body"}, {"file": "src/objects/Body.js", "line": 754, "description": "Should be called whenever you change the body shape or mass.", "itemtype": "method", "name": "updateMassProperties", "class": "Body"}, {"file": "src/objects/Body.js", "line": 782, "description": "Get world velocity of a point in the body.", "itemtype": "method", "name": "getVelocityAtWorldPoint", "params": [{"name": "worldPoint", "description": "", "type": "Vec3"}, {"name": "result", "description": "", "type": "Vec3"}], "return": {"description": "The result vector.", "type": "Vec3"}, "class": "Body"}, {"file": "src/objects/RaycastVehicle.js", "line": 22, "itemtype": "property", "name": "chassisBody", "type": "Body", "class": "RaycastVehicle"}, {"file": "src/objects/RaycastVehicle.js", "line": 27, "description": "An array of WheelInfo objects.", "itemtype": "property", "name": "wheelInfos", "type": "Array", "class": "RaycastVehicle"}, {"file": "src/objects/RaycastVehicle.js", "line": 33, "description": "Will be set to true if the car is sliding.", "itemtype": "property", "name": "sliding", "type": "Boolean", "class": "RaycastVehicle"}, {"file": "src/objects/RaycastVehicle.js", "line": 39, "itemtype": "property", "name": "world", "type": "World", "class": "RaycastVehicle"}, {"file": "src/objects/RaycastVehicle.js", "line": 44, "description": "Index of the right axis, 0=x, 1=y, 2=z", "itemtype": "property", "name": "indexRightAxis", "type": "Integer", "default": "1", "class": "RaycastVehicle"}, {"file": "src/objects/RaycastVehicle.js", "line": 51, "description": "Index of the forward axis, 0=x, 1=y, 2=z", "itemtype": "property", "name": "indexForwardAxis", "type": "Integer", "default": "0", "class": "RaycastVehicle"}, {"file": "src/objects/RaycastVehicle.js", "line": 58, "description": "Index of the up axis, 0=x, 1=y, 2=z", "itemtype": "property", "name": "indexUpAxis", "type": "Integer", "default": "2", "class": "RaycastVehicle"}, {"file": "src/objects/RaycastVehicle.js", "line": 74, "description": "Add a wheel. For information about the options, see WheelInfo.", "itemtype": "method", "name": "addWheel", "params": [{"name": "options", "description": "", "type": "Object", "optional": true}], "class": "RaycastVehicle"}, {"file": "src/objects/RaycastVehicle.js", "line": 89, "description": "Set the steering value of a wheel.", "itemtype": "method", "name": "setS<PERSON>ringV<PERSON>ue", "params": [{"name": "value", "description": "", "type": "Number"}, {"name": "wheelIndex", "description": "", "type": "Integer"}], "class": "RaycastVehicle"}, {"file": "src/objects/RaycastVehicle.js", "line": 102, "description": "Set the wheel force to apply on one of the wheels each time step", "itemtype": "method", "name": "applyEngineForce", "params": [{"name": "value", "description": "", "type": "Number"}, {"name": "wheelIndex", "description": "", "type": "Integer"}], "class": "RaycastVehicle"}, {"file": "src/objects/RaycastVehicle.js", "line": 112, "description": "Set the braking force of a wheel", "itemtype": "method", "name": "set<PERSON><PERSON><PERSON>", "params": [{"name": "brake", "description": "", "type": "Number"}, {"name": "wheelIndex", "description": "", "type": "Integer"}], "class": "RaycastVehicle"}, {"file": "src/objects/RaycastVehicle.js", "line": 122, "description": "Add the vehicle including its constraints to the world.", "itemtype": "method", "name": "addToWorld", "params": [{"name": "world", "description": "", "type": "World"}], "class": "RaycastVehicle"}, {"file": "src/objects/RaycastVehicle.js", "line": 138, "description": "Get one of the wheel axles, world-oriented.", "access": "private", "tagname": "", "itemtype": "method", "name": "getVehicleAxisWorld", "params": [{"name": "axisIndex", "description": "", "type": "Integer"}, {"name": "result", "description": "", "type": "Vec3"}], "class": "RaycastVehicle"}, {"file": "src/objects/RaycastVehicle.js", "line": 279, "description": "Remove the vehicle including its constraints from the world.", "itemtype": "method", "name": "removeFromWorld", "params": [{"name": "world", "description": "", "type": "World"}], "class": "RaycastVehicle"}, {"file": "src/objects/RaycastVehicle.js", "line": 380, "description": "Update one of the wheel transform.\nNote when rendering wheels: during each step, wheel transforms are updated BEFORE the chassis; ie. their position becomes invalid after the step. Thus when you render wheels, you must update wheel transforms before rendering them. See raycastVehicle demo for an example.", "itemtype": "method", "name": "updateWheelTransform", "params": [{"name": "wheelIndex", "description": "The wheel index to update.", "type": "Integer"}], "class": "RaycastVehicle"}, {"file": "src/objects/RaycastVehicle.js", "line": 428, "description": "Get the world transform of one of the wheels", "itemtype": "method", "name": "getWheelTransformWorld", "params": [{"name": "wheelIndex", "description": "", "type": "Integer"}], "return": {"description": "", "type": "Transform"}, "class": "RaycastVehicle"}, {"file": "src/objects/RigidVehicle.js", "line": 18, "itemtype": "property", "name": "coordinateSystem", "type": "{Vec3}", "class": "RigidVehicle"}, {"file": "src/objects/RigidVehicle.js", "line": 24, "itemtype": "property", "name": "chassisBody", "type": "Body", "class": "RigidVehicle"}, {"file": "src/objects/RigidVehicle.js", "line": 35, "itemtype": "property", "name": "constraints", "type": "{Array}", "class": "RigidVehicle"}, {"file": "src/objects/RigidVehicle.js", "line": 45, "description": "Add a wheel", "itemtype": "method", "name": "addWheel", "params": [{"name": "options", "description": "", "type": "Object", "props": [{"name": "isFrontWheel", "description": "", "type": "Boolean", "optional": true}, {"name": "position", "description": "Position of the wheel, locally in the chassis body.", "type": "Vec3", "optional": true}, {"name": "direction", "description": "Slide direction of the wheel along the suspension.", "type": "Vec3", "optional": true}, {"name": "axis", "description": "Axis of rotation of the wheel, locally defined in the chassis.", "type": "Vec3", "optional": true}, {"name": "body", "description": "The wheel body.", "type": "Body", "optional": true}]}], "class": "RigidVehicle"}, {"file": "src/objects/RigidVehicle.js", "line": 89, "description": "Set the steering value of a wheel.", "itemtype": "method", "name": "setS<PERSON>ringV<PERSON>ue", "params": [{"name": "value", "description": "", "type": "Number"}, {"name": "wheelIndex", "description": "", "type": "Integer"}], "todo": ["check coordinateSystem"], "class": "RigidVehicle"}, {"file": "src/objects/RigidVehicle.js", "line": 111, "description": "Set the target rotational speed of the hinge constraint.", "itemtype": "method", "name": "setMotorSpeed", "params": [{"name": "value", "description": "", "type": "Number"}, {"name": "wheelIndex", "description": "", "type": "Integer"}], "class": "RigidVehicle"}, {"file": "src/objects/RigidVehicle.js", "line": 123, "description": "Set the target rotational speed of the hinge constraint.", "itemtype": "method", "name": "disableMotor", "params": [{"name": "value", "description": "", "type": "Number"}, {"name": "wheelIndex", "description": "", "type": "Integer"}], "class": "RigidVehicle"}, {"file": "src/objects/RigidVehicle.js", "line": 136, "description": "Set the wheel force to apply on one of the wheels each time step", "itemtype": "method", "name": "setWheelForce", "params": [{"name": "value", "description": "", "type": "Number"}, {"name": "wheelIndex", "description": "", "type": "Integer"}], "class": "RigidVehicle"}, {"file": "src/objects/RigidVehicle.js", "line": 146, "description": "Apply a torque on one of the wheels.", "itemtype": "method", "name": "applyWheelForce", "params": [{"name": "value", "description": "", "type": "Number"}, {"name": "wheelIndex", "description": "", "type": "Integer"}], "class": "RigidVehicle"}, {"file": "src/objects/RigidVehicle.js", "line": 162, "description": "Add the vehicle including its constraints to the world.", "itemtype": "method", "name": "addToWorld", "params": [{"name": "world", "description": "", "type": "World"}], "class": "RigidVehicle"}, {"file": "src/objects/RigidVehicle.js", "line": 189, "description": "Remove the vehicle including its constraints from the world.", "itemtype": "method", "name": "removeFromWorld", "params": [{"name": "world", "description": "", "type": "World"}], "class": "RigidVehicle"}, {"file": "src/objects/RigidVehicle.js", "line": 209, "description": "Get current rotational velocity of a wheel", "itemtype": "method", "name": "getWheelSpeed", "params": [{"name": "wheelIndex", "description": "", "type": "Integer"}], "class": "RigidVehicle"}, {"file": "src/objects/SPHSystem.js", "line": 18, "description": "Density of the system (kg/m3).", "itemtype": "property", "name": "density", "type": "Number", "class": "SPHSystem"}, {"file": "src/objects/SPHSystem.js", "line": 24, "description": "Distance below which two particles are considered to be neighbors.\nIt should be adjusted so there are about 15-20 neighbor particles within this radius.", "itemtype": "property", "name": "smoothingRadius", "type": "Number", "class": "SPHSystem"}, {"file": "src/objects/SPHSystem.js", "line": 32, "description": "Viscosity of the system.", "itemtype": "property", "name": "viscosity", "type": "Number", "class": "SPHSystem"}, {"file": "src/objects/SPHSystem.js", "line": 45, "description": "Add a particle to the system.", "itemtype": "method", "name": "add", "params": [{"name": "particle", "description": "", "type": "Body"}], "class": "SPHSystem"}, {"file": "src/objects/SPHSystem.js", "line": 57, "description": "Remove a particle from the system.", "itemtype": "method", "name": "remove", "params": [{"name": "particle", "description": "", "type": "Body"}], "class": "SPHSystem"}, {"file": "src/objects/SPHSystem.js", "line": 72, "description": "Get neighbors within smoothing volume, save in the array neighbors", "itemtype": "method", "name": "getNeighbors", "params": [{"name": "particle", "description": "", "type": "Body"}, {"name": "neighbors", "description": "", "type": "Array"}], "class": "SPHSystem"}, {"file": "src/objects/Spring.js", "line": 24, "description": "Rest length of the spring.", "itemtype": "property", "name": "restLength", "type": "{number}", "class": "Spring"}, {"file": "src/objects/Spring.js", "line": 31, "description": "Stiffness of the spring.", "itemtype": "property", "name": "stiffness", "type": "{number}", "class": "Spring"}, {"file": "src/objects/Spring.js", "line": 38, "description": "Damping of the spring.", "itemtype": "property", "name": "damping", "type": "{number}", "class": "Spring"}, {"file": "src/objects/Spring.js", "line": 45, "description": "First connected body.", "itemtype": "property", "name": "bodyA", "type": "{Body}", "class": "Spring"}, {"file": "src/objects/Spring.js", "line": 52, "description": "Second connected body.", "itemtype": "property", "name": "bodyB", "type": "{Body}", "class": "Spring"}, {"file": "src/objects/Spring.js", "line": 59, "description": "Anchor for bodyA in local bodyA coordinates.", "itemtype": "property", "name": "localAnchorA", "type": "{Vec3}", "class": "Spring"}, {"file": "src/objects/Spring.js", "line": 66, "description": "Anchor for bodyB in local bodyB coordinates.", "itemtype": "property", "name": "localAnchorB", "type": "{Vec3}", "class": "Spring"}, {"file": "src/objects/Spring.js", "line": 87, "description": "Set the anchor point on body A, using world coordinates.", "itemtype": "method", "name": "setWorldAnchorA", "params": [{"name": "worldAnchorA", "description": "", "type": "Vec3"}], "class": "Spring"}, {"file": "src/objects/Spring.js", "line": 96, "description": "Set the anchor point on body B, using world coordinates.", "itemtype": "method", "name": "setWorldAnchorB", "params": [{"name": "worldAnchorB", "description": "", "type": "Vec3"}], "class": "Spring"}, {"file": "src/objects/Spring.js", "line": 105, "description": "Get the anchor point on body A, in world coordinates.", "itemtype": "method", "name": "getWorldAnchorA", "params": [{"name": "result", "description": "The vector to store the result in.", "type": "Vec3"}], "class": "Spring"}, {"file": "src/objects/Spring.js", "line": 114, "description": "Get the anchor point on body B, in world coordinates.", "itemtype": "method", "name": "getWorldAnchorB", "params": [{"name": "result", "description": "The vector to store the result in.", "type": "Vec3"}], "class": "Spring"}, {"file": "src/objects/Spring.js", "line": 135, "description": "Apply the spring force to the connected bodies.", "itemtype": "method", "name": "applyForce", "class": "Spring"}, {"file": "src/objects/WheelInfo.js", "line": 72, "description": "Max travel distance of the suspension, in meters.", "itemtype": "property", "name": "maxSuspensionTravel", "type": "Number", "class": "WheelInfo"}, {"file": "src/objects/WheelInfo.js", "line": 78, "description": "Speed to apply to the wheel rotation when the wheel is sliding.", "itemtype": "property", "name": "customSlidingRotationalSpeed", "type": "Number", "class": "WheelInfo"}, {"file": "src/objects/WheelInfo.js", "line": 84, "description": "If the customSlidingRotationalSpeed should be used.", "itemtype": "property", "name": "useCustomSlidingRotationalSpeed", "type": "Boolean", "class": "WheelInfo"}, {"file": "src/objects/WheelInfo.js", "line": 90, "itemtype": "property", "name": "sliding", "type": "Boolean", "class": "WheelInfo"}, {"file": "src/objects/WheelInfo.js", "line": 95, "description": "Connection point, defined locally in the chassis body frame.", "itemtype": "property", "name": "chassisConnectionPointLocal", "type": "Vec3", "class": "WheelInfo"}, {"file": "src/objects/WheelInfo.js", "line": 101, "itemtype": "property", "name": "chassisConnectionPointWorld", "type": "Vec3", "class": "WheelInfo"}, {"file": "src/objects/WheelInfo.js", "line": 106, "itemtype": "property", "name": "directionLocal", "type": "Vec3", "class": "WheelInfo"}, {"file": "src/objects/WheelInfo.js", "line": 111, "itemtype": "property", "name": "directionWorld", "type": "Vec3", "class": "WheelInfo"}, {"file": "src/objects/WheelInfo.js", "line": 116, "itemtype": "property", "name": "axleLocal", "type": "Vec3", "class": "WheelInfo"}, {"file": "src/objects/WheelInfo.js", "line": 121, "itemtype": "property", "name": "axleWorld", "type": "Vec3", "class": "WheelInfo"}, {"file": "src/objects/WheelInfo.js", "line": 126, "itemtype": "property", "name": "suspensionRestLength", "type": "Number", "class": "WheelInfo"}, {"file": "src/objects/WheelInfo.js", "line": 131, "itemtype": "property", "name": "suspensionMaxLength", "type": "Number", "class": "WheelInfo"}, {"file": "src/objects/WheelInfo.js", "line": 136, "itemtype": "property", "name": "radius", "type": "Number", "class": "WheelInfo"}, {"file": "src/objects/WheelInfo.js", "line": 141, "itemtype": "property", "name": "suspensionStiffness", "type": "Number", "class": "WheelInfo"}, {"file": "src/objects/WheelInfo.js", "line": 146, "itemtype": "property", "name": "dampingCompression", "type": "Number", "class": "WheelInfo"}, {"file": "src/objects/WheelInfo.js", "line": 151, "itemtype": "property", "name": "dampingRelaxation", "type": "Number", "class": "WheelInfo"}, {"file": "src/objects/WheelInfo.js", "line": 156, "itemtype": "property", "name": "frictionSlip", "type": "Number", "class": "WheelInfo"}, {"file": "src/objects/WheelInfo.js", "line": 161, "itemtype": "property", "name": "steering", "type": "Number", "class": "WheelInfo"}, {"file": "src/objects/WheelInfo.js", "line": 166, "description": "Rotation value, in radians.", "itemtype": "property", "name": "rotation", "type": "Number", "class": "WheelInfo"}, {"file": "src/objects/WheelInfo.js", "line": 172, "itemtype": "property", "name": "deltaRotation", "type": "Number", "class": "WheelInfo"}, {"file": "src/objects/WheelInfo.js", "line": 177, "itemtype": "property", "name": "rollInfluence", "type": "Number", "class": "WheelInfo"}, {"file": "src/objects/WheelInfo.js", "line": 182, "itemtype": "property", "name": "maxSuspensionForce", "type": "Number", "class": "WheelInfo"}, {"file": "src/objects/WheelInfo.js", "line": 187, "itemtype": "property", "name": "engineForce", "type": "Number", "class": "WheelInfo"}, {"file": "src/objects/WheelInfo.js", "line": 192, "itemtype": "property", "name": "brake", "type": "Number", "class": "WheelInfo"}, {"file": "src/objects/WheelInfo.js", "line": 197, "itemtype": "property", "name": "isFrontWheel", "type": "Number", "class": "WheelInfo"}, {"file": "src/objects/WheelInfo.js", "line": 202, "itemtype": "property", "name": "clippedInvContactDotSuspension", "type": "Number", "class": "WheelInfo"}, {"file": "src/objects/WheelInfo.js", "line": 207, "itemtype": "property", "name": "suspensionRelativeVelocity", "type": "Number", "class": "WheelInfo"}, {"file": "src/objects/WheelInfo.js", "line": 212, "itemtype": "property", "name": "suspensionF<PERSON>ce", "type": "Number", "class": "WheelInfo"}, {"file": "src/objects/WheelInfo.js", "line": 217, "itemtype": "property", "name": "skidInfo", "type": "Number", "class": "WheelInfo"}, {"file": "src/objects/WheelInfo.js", "line": 222, "itemtype": "property", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "Number", "class": "WheelInfo"}, {"file": "src/objects/WheelInfo.js", "line": 227, "itemtype": "property", "name": "sideImpulse", "type": "Number", "class": "WheelInfo"}, {"file": "src/objects/WheelInfo.js", "line": 232, "itemtype": "property", "name": "forwardImpulse", "type": "Number", "class": "WheelInfo"}, {"file": "src/objects/WheelInfo.js", "line": 237, "description": "The result from raycasting", "itemtype": "property", "name": "raycastResult", "type": "RaycastResult", "class": "WheelInfo"}, {"file": "src/objects/WheelInfo.js", "line": 243, "description": "Wheel world transform", "itemtype": "property", "name": "worldTransform", "type": "Transform", "class": "WheelInfo"}, {"file": "src/objects/WheelInfo.js", "line": 249, "itemtype": "property", "name": "isInContact", "type": "Boolean", "class": "WheelInfo"}, {"file": "src/shapes/Box.js", "line": 20, "itemtype": "property", "name": "halfExtents", "type": "{Vec3}", "class": "Box"}, {"file": "src/shapes/Box.js", "line": 26, "description": "Used by the contact generator to make contacts with other convex polyhedra for example", "itemtype": "property", "name": "convexPolyhedronRepresentation", "type": "{ConvexPolyhedron}", "class": "Box"}, {"file": "src/shapes/Box.js", "line": 39, "description": "Updates the local convex polyhedron representation used for some collisions.", "itemtype": "method", "name": "updateConvexPolyhedronRepresentation", "class": "Box"}, {"file": "src/shapes/Box.js", "line": 80, "itemtype": "method", "name": "calculateLocalInertia", "params": [{"name": "mass", "description": "", "type": "Number"}, {"name": "target", "description": "", "type": "Vec3"}], "return": {"description": "", "type": "Vec3"}, "class": "Box"}, {"file": "src/shapes/Box.js", "line": 99, "description": "Get the box 6 side normals", "itemtype": "method", "name": "getSideNormals", "params": [{"name": "sixTargetVectors", "description": "An array of 6 vectors, to store the resulting side normals in.", "type": "Array"}, {"name": "quat", "description": "Orientation to apply to the normal vectors. If not provided, the vectors will be in respect to the local frame.", "type": "Quaternion"}], "return": {"description": "", "type": "Array"}, "class": "Box"}, {"file": "src/shapes/ConvexPolyhedron.js", "line": 32, "description": "Array of Vec3", "itemtype": "property", "name": "vertices", "type": "{Array}", "class": "ConvexPolyhedron"}, {"file": "src/shapes/ConvexPolyhedron.js", "line": 42, "description": "Array of integer arrays, indicating which vertices each face consists of", "itemtype": "property", "name": "faces", "type": "{Array}", "class": "ConvexPolyhedron"}, {"file": "src/shapes/ConvexPolyhedron.js", "line": 49, "description": "Array of Vec3", "itemtype": "property", "name": "faceNormals", "type": "{Array}", "class": "ConvexPolyhedron"}, {"file": "src/shapes/ConvexPolyhedron.js", "line": 60, "description": "Array of Vec3", "itemtype": "property", "name": "uniqueEdges", "type": "{Array}", "class": "ConvexPolyhedron"}, {"file": "src/shapes/ConvexPolyhedron.js", "line": 67, "description": "If given, these locally defined, normalized axes are the only ones being checked when doing separating axis check.", "itemtype": "property", "name": "uniqueAxes", "type": "Array", "class": "ConvexPolyhedron"}, {"file": "src/shapes/ConvexPolyhedron.js", "line": 80, "description": "Computes uniqueEdges", "itemtype": "method", "name": "computeEdges", "class": "ConvexPolyhedron"}, {"file": "src/shapes/ConvexPolyhedron.js", "line": 116, "description": "Compute the normals of the faces. Will reuse existing Vec3 objects in the .faceNormals array if they exist.", "itemtype": "method", "name": "computeNormals", "class": "ConvexPolyhedron"}, {"file": "src/shapes/ConvexPolyhedron.js", "line": 147, "description": "Get face normal given 3 vertices", "static": 1, "itemtype": "method", "name": "getFaceNormal", "params": [{"name": "va", "description": "", "type": "Vec3"}, {"name": "vb", "description": "", "type": "Vec3"}, {"name": "vc", "description": "", "type": "Vec3"}, {"name": "target", "description": "", "type": "Vec3"}], "class": "ConvexPolyhedron"}, {"file": "src/shapes/ConvexPolyhedron.js", "line": 167, "description": "Compute the normal of a face from its vertices", "itemtype": "method", "name": "getFaceNormal", "params": [{"name": "i", "description": "", "type": "Number"}, {"name": "target", "description": "", "type": "Vec3"}], "class": "ConvexPolyhedron"}, {"file": "src/shapes/ConvexPolyhedron.js", "line": 181, "itemtype": "method", "name": "clipAgainstHull", "params": [{"name": "posA", "description": "", "type": "Vec3"}, {"name": "quatA", "description": "", "type": "Quaternion"}, {"name": "hullB", "description": "", "type": "ConvexPolyhedron"}, {"name": "posB", "description": "", "type": "Vec3"}, {"name": "quatB", "description": "", "type": "Quaternion"}, {"name": "separatingNormal", "description": "", "type": "Vec3"}, {"name": "minDist", "description": "Clamp distance", "type": "Number"}, {"name": "maxDist", "description": "", "type": "Number"}, {"name": "result", "description": "The an array of contact point objects, see clipFaceAgainstHull", "type": "Array"}], "see": ["http://bullet.googlecode.com/svn/trunk/src/BulletCollision/NarrowPhaseCollision/btPolyhedralContactClipping.cpp"], "class": "ConvexPolyhedron"}, {"file": "src/shapes/ConvexPolyhedron.js", "line": 234, "description": "Find the separating axis between this hull and another", "itemtype": "method", "name": "findSeparatingAxis", "params": [{"name": "hullB", "description": "", "type": "ConvexPolyhedron"}, {"name": "posA", "description": "", "type": "Vec3"}, {"name": "quatA", "description": "", "type": "Quaternion"}, {"name": "posB", "description": "", "type": "Vec3"}, {"name": "quatB", "description": "", "type": "Quaternion"}, {"name": "target", "description": "The target vector to save the axis in", "type": "Vec3"}], "return": {"description": "Returns false if a separation is found, else true", "type": "Bool"}, "class": "ConvexPolyhedron"}, {"file": "src/shapes/ConvexPolyhedron.js", "line": 382, "description": "Test separating axis against two hulls. Both hulls are projected onto the axis and the overlap size is returned if there is one.", "itemtype": "method", "name": "testSepAxis", "params": [{"name": "axis", "description": "", "type": "Vec3"}, {"name": "hullB", "description": "", "type": "ConvexPolyhedron"}, {"name": "posA", "description": "", "type": "Vec3"}, {"name": "quatA", "description": "", "type": "Quaternion"}, {"name": "posB", "description": "", "type": "Vec3"}, {"name": "quatB", "description": "", "type": "Quaternion"}], "return": {"description": "The overlap depth, or FALSE if no penetration.", "type": "Number"}, "class": "ConvexPolyhedron"}, {"file": "src/shapes/ConvexPolyhedron.js", "line": 413, "itemtype": "method", "name": "calculateLocalInertia", "params": [{"name": "mass", "description": "", "type": "Number"}, {"name": "target", "description": "", "type": "Vec3"}], "class": "ConvexPolyhedron"}, {"file": "src/shapes/ConvexPolyhedron.js", "line": 430, "itemtype": "method", "name": "getPlaneConstantOfFace", "params": [{"name": "face_i", "description": "Index of the face", "type": "Number"}], "return": {"description": "", "type": "Number"}, "class": "ConvexPolyhedron"}, {"file": "src/shapes/ConvexPolyhedron.js", "line": 443, "description": "Clip a face against a hull.", "itemtype": "method", "name": "clipFaceAgainstHull", "params": [{"name": "separatingNormal", "description": "", "type": "Vec3"}, {"name": "posA", "description": "", "type": "Vec3"}, {"name": "quatA", "description": "", "type": "Quaternion"}, {"name": "worldVertsB1", "description": "An array of Vec3 with vertices in the world frame.", "type": "Array"}, {"name": "minDist", "description": "Distance clamping", "type": "Number"}, {"name": "maxDist", "description": "", "type": "Number"}, {"name": "Array", "description": "result Array to store resulting contact points in. Will be objects with properties: point, depth, normal. These are represented in world coordinates."}], "class": "ConvexPolyhedron"}, {"file": "src/shapes/ConvexPolyhedron.js", "line": 588, "description": "Clip a face in a hull against the back of a plane.", "itemtype": "method", "name": "clipFaceAgainstPlane", "params": [{"name": "inVertices", "description": "", "type": "Array"}, {"name": "outVertices", "description": "", "type": "Array"}, {"name": "planeNormal", "description": "", "type": "Vec3"}, {"name": "planeConstant", "description": "The constant in the mathematical plane equation", "type": "Number"}], "class": "ConvexPolyhedron"}, {"file": "src/shapes/ConvexPolyhedron.js", "line": 689, "description": "Updates .worldVertices and sets .worldVerticesNeedsUpdate to false.", "itemtype": "method", "name": "computeWorldFaceNormals", "params": [{"name": "quat", "description": "", "type": "Quaternion"}], "class": "ConvexPolyhedron"}, {"file": "src/shapes/ConvexPolyhedron.js", "line": 709, "itemtype": "method", "name": "updateBoundingSphereRadius", "class": "ConvexPolyhedron"}, {"file": "src/shapes/ConvexPolyhedron.js", "line": 727, "itemtype": "method", "name": "calculateWorldAABB", "params": [{"name": "pos", "description": "", "type": "Vec3"}, {"name": "quat", "description": "", "type": "Quaternion"}, {"name": "min", "description": "", "type": "Vec3"}, {"name": "max", "description": "", "type": "Vec3"}], "class": "ConvexPolyhedron"}, {"file": "src/shapes/ConvexPolyhedron.js", "line": 764, "description": "Get approximate convex volume", "itemtype": "method", "name": "volume", "return": {"description": "", "type": "Number"}, "class": "ConvexPolyhedron"}, {"file": "src/shapes/ConvexPolyhedron.js", "line": 773, "description": "Get an average of all the vertices positions", "itemtype": "method", "name": "getAveragePointLocal", "params": [{"name": "target", "description": "", "type": "Vec3"}], "return": {"description": "", "type": "Vec3"}, "class": "ConvexPolyhedron"}, {"file": "src/shapes/ConvexPolyhedron.js", "line": 790, "description": "Transform all local points. Will change the .vertices", "itemtype": "method", "name": "transformAllPoints", "params": [{"name": "offset", "description": "", "type": "Vec3"}, {"name": "quat", "description": "", "type": "Quaternion"}], "class": "ConvexPolyhedron"}, {"file": "src/shapes/ConvexPolyhedron.js", "line": 829, "description": "Checks whether p is inside the polyhedra. Must be in local coords. The point lies outside of the convex hull of the other points if and only if the direction of all the vectors from it to those other points are on less than one half of a sphere around it.", "itemtype": "method", "name": "pointIsInside", "params": [{"name": "p", "description": "A point given in local coordinates", "type": "Vec3"}], "return": {"description": "", "type": "Boolean"}, "class": "ConvexPolyhedron"}, {"file": "src/shapes/ConvexPolyhedron.js", "line": 871, "description": "Get max and min dot product of a convex hull at position (pos,quat) projected onto an axis. Results are saved in the array maxmin.", "static": 1, "itemtype": "method", "name": "project", "params": [{"name": "hull", "description": "", "type": "ConvexPolyhedron"}, {"name": "axis", "description": "", "type": "Vec3"}, {"name": "pos", "description": "", "type": "Vec3"}, {"name": "quat", "description": "", "type": "Quaternion"}, {"name": "result", "description": "result[0] and result[1] will be set to maximum and minimum, respectively.", "type": "Array"}], "class": "ConvexPolyhedron"}, {"file": "src/shapes/Heightfield.js", "line": 43, "description": "An array of numbers, or height values, that are spread out along the x axis.", "itemtype": "property", "name": "data", "type": "Array", "class": "Heightfield"}, {"file": "src/shapes/Heightfield.js", "line": 49, "description": "Max value of the data", "itemtype": "property", "name": "maxValue", "type": "Number", "class": "Heightfield"}, {"file": "src/shapes/Heightfield.js", "line": 55, "description": "Max value of the data", "itemtype": "property", "name": "minValue", "type": "Number", "class": "Heightfield"}, {"file": "src/shapes/Heightfield.js", "line": 61, "description": "The width of each element", "itemtype": "property", "name": "elementSize", "type": "Number", "todo": ["elementSizeX and Y"], "class": "Heightfield"}, {"file": "src/shapes/Heightfield.js", "line": 92, "description": "Call whenever you change the data array.", "itemtype": "method", "name": "update", "class": "Heightfield"}, {"file": "src/shapes/Heightfield.js", "line": 100, "description": "Update the .minValue property", "itemtype": "method", "name": "updateMinValue", "class": "Heightfield"}, {"file": "src/shapes/Heightfield.js", "line": 118, "description": "Update the .maxValue property", "itemtype": "method", "name": "updateMaxValue", "class": "Heightfield"}, {"file": "src/shapes/Heightfield.js", "line": 136, "description": "Set the height value at an index. Don't forget to update maxValue and minValue after you're done.", "itemtype": "method", "name": "setHeightValueAtIndex", "params": [{"name": "xi", "description": "", "type": "Integer"}, {"name": "yi", "description": "", "type": "Integer"}, {"name": "value", "description": "", "type": "Number"}], "class": "Heightfield"}, {"file": "src/shapes/Heightfield.js", "line": 162, "description": "Get max/min in a rectangle in the matrix data", "itemtype": "method", "name": "getRectMinMax", "params": [{"name": "iMinX", "description": "", "type": "Integer"}, {"name": "iMinY", "description": "", "type": "Integer"}, {"name": "iMaxX", "description": "", "type": "Integer"}, {"name": "iMaxY", "description": "", "type": "Integer"}, {"name": "result", "description": "An array to store the results in.", "type": "Array", "optional": true}], "return": {"description": "The result array, if it was passed in. Minimum will be at position 0 and max at 1.", "type": "Array"}, "class": "Heightfield"}, {"file": "src/shapes/Heightfield.js", "line": 191, "description": "Get the index of a local position on the heightfield. The indexes indicate the rectangles, so if your terrain is made of N x N height data points, you will have rectangle indexes ranging from 0 to N-1.", "itemtype": "method", "name": "getIndexOfPosition", "params": [{"name": "x", "description": "", "type": "Number"}, {"name": "y", "description": "", "type": "Number"}, {"name": "result", "description": "Two-element array", "type": "Array"}, {"name": "clamp", "description": "If the position should be clamped to the heightfield edge.", "type": "Boolean"}], "return": {"description": "", "type": "Boolean"}, "class": "Heightfield"}, {"file": "src/shapes/Heightfield.js", "line": 257, "description": "Get a triangle in the terrain in the form of a triangular convex shape.", "itemtype": "method", "name": "getConvexTrianglePillar", "params": [{"name": "i", "description": "", "type": "Integer"}, {"name": "j", "description": "", "type": "Integer"}, {"name": "getUpper<PERSON>riangle", "description": "", "type": "Boolean"}], "class": "Heightfield"}, {"file": "src/shapes/Particle.js", "line": 21, "itemtype": "method", "name": "calculateLocalInertia", "params": [{"name": "mass", "description": "", "type": "Number"}, {"name": "target", "description": "", "type": "Vec3"}], "return": {"description": "", "type": "Vec3"}, "class": "Particle"}, {"file": "src/shapes/Shape.js", "line": 17, "description": "Identifyer of the Shape.", "itemtype": "property", "name": "id", "type": "Number", "class": "<PERSON><PERSON><PERSON>"}, {"file": "src/shapes/Shape.js", "line": 23, "description": "The type of this shape. Must be set to an int > 0 by subclasses.", "itemtype": "property", "name": "type", "type": "{Number}", "see": ["Shape.types"], "class": "<PERSON><PERSON><PERSON>"}, {"file": "src/shapes/Shape.js", "line": 31, "description": "The local bounding sphere radius of this shape.", "itemtype": "property", "name": "boundingSphereRadius", "type": "Number", "class": "<PERSON><PERSON><PERSON>"}, {"file": "src/shapes/Shape.js", "line": 37, "description": "Whether to produce contact forces when in contact with other bodies. Note that contacts will be generated, but they will be disabled.", "itemtype": "property", "name": "collisionResponse", "type": "Boolean", "class": "<PERSON><PERSON><PERSON>"}, {"file": "src/shapes/Shape.js", "line": 43, "itemtype": "property", "name": "material", "type": "Material", "class": "<PERSON><PERSON><PERSON>"}, {"file": "src/shapes/Shape.js", "line": 50, "description": "Computes the bounding sphere radius. The result is stored in the property .boundingSphereRadius", "itemtype": "method", "name": "updateBoundingSphereRadius", "return": {"description": "", "type": "Number"}, "class": "<PERSON><PERSON><PERSON>"}, {"file": "src/shapes/Shape.js", "line": 59, "description": "Get the volume of this shape", "itemtype": "method", "name": "volume", "return": {"description": "", "type": "Number"}, "class": "<PERSON><PERSON><PERSON>"}, {"file": "src/shapes/Shape.js", "line": 68, "description": "Calculates the inertia in the local frame for this shape.", "itemtype": "method", "name": "calculateLocalInertia", "return": {"description": "", "type": "Vec3"}, "see": ["http://en.wikipedia.org/wiki/List_of_moments_of_inertia"], "class": "<PERSON><PERSON><PERSON>"}, {"file": "src/shapes/Shape.js", "line": 80, "description": "The available shape types.", "static": 1, "itemtype": "property", "name": "types", "type": "{Object}", "class": "<PERSON><PERSON><PERSON>"}, {"file": "src/shapes/Sphere.js", "line": 17, "itemtype": "property", "name": "radius", "type": "Number", "class": "Sphere"}, {"file": "src/shapes/Trimesh.js", "line": 32, "itemtype": "property", "name": "vertices", "type": "{Array}", "class": "Trimesh"}, {"file": "src/shapes/Trimesh.js", "line": 38, "description": "Array of integers, indicating which vertices each triangle consists of. The length of this array is thus 3 times the number of triangles.", "itemtype": "property", "name": "indices", "type": "{Array}", "class": "Trimesh"}, {"file": "src/shapes/Trimesh.js", "line": 45, "description": "The normals data.", "itemtype": "property", "name": "normals", "type": "{Array}", "class": "Trimesh"}, {"file": "src/shapes/Trimesh.js", "line": 52, "description": "The local AABB of the mesh.", "itemtype": "property", "name": "aabb", "type": "{Array}", "class": "Trimesh"}, {"file": "src/shapes/Trimesh.js", "line": 59, "description": "References to vertex pairs, making up all unique edges in the trimesh.", "itemtype": "property", "name": "edges", "type": "Array", "class": "Trimesh"}, {"file": "src/shapes/Trimesh.js", "line": 65, "description": "Local scaling of the mesh. Use .setScale() to set it.", "itemtype": "property", "name": "scale", "type": "Vec3", "class": "Trimesh"}, {"file": "src/shapes/Trimesh.js", "line": 71, "description": "The indexed triangles. Use .updateTree() to update it.", "itemtype": "property", "name": "tree", "type": "Octree", "class": "Trimesh"}, {"file": "src/shapes/Trimesh.js", "line": 88, "itemtype": "method", "name": "updateTree", "class": "Trimesh"}, {"file": "src/shapes/Trimesh.js", "line": 127, "description": "Get triangles in a local AABB from the trimesh.", "itemtype": "method", "name": "getTrianglesInAABB", "params": [{"name": "aabb", "description": "", "type": "AABB"}, {"name": "result", "description": "An array of integers, referencing the queried triangles.", "type": "Array"}], "class": "Trimesh"}, {"file": "src/shapes/Trimesh.js", "line": 153, "itemtype": "method", "name": "setScale", "params": [{"name": "scale", "description": "", "type": "Vec3"}], "class": "Trimesh"}, {"file": "src/shapes/Trimesh.js", "line": 170, "description": "Compute the normals of the faces. Will save in the .normals array.", "itemtype": "method", "name": "updateNormals", "class": "Trimesh"}, {"file": "src/shapes/Trimesh.js", "line": 198, "description": "Update the .edges property", "itemtype": "method", "name": "updateEdges", "class": "Trimesh"}, {"file": "src/shapes/Trimesh.js", "line": 226, "description": "Get an edge vertex", "itemtype": "method", "name": "getEdgeVertex", "params": [{"name": "edgeIndex", "description": "", "type": "Number"}, {"name": "firstOrSecond", "description": "0 or 1, depending on which one of the vertices you need.", "type": "Number"}, {"name": "vertexStore", "description": "Where to store the result", "type": "Vec3"}], "class": "Trimesh"}, {"file": "src/shapes/Trimesh.js", "line": 241, "description": "Get a vector along an edge.", "itemtype": "method", "name": "getEdgeVector", "params": [{"name": "edgeIndex", "description": "", "type": "Number"}, {"name": "vectorStore", "description": "", "type": "Vec3"}], "class": "Trimesh"}, {"file": "src/shapes/Trimesh.js", "line": 255, "description": "Get face normal given 3 vertices", "static": 1, "itemtype": "method", "name": "computeNormal", "params": [{"name": "va", "description": "", "type": "Vec3"}, {"name": "vb", "description": "", "type": "Vec3"}, {"name": "vc", "description": "", "type": "Vec3"}, {"name": "target", "description": "", "type": "Vec3"}], "class": "Trimesh"}, {"file": "src/shapes/Trimesh.js", "line": 279, "description": "Get vertex i.", "itemtype": "method", "name": "getVertex", "params": [{"name": "i", "description": "", "type": "Number"}, {"name": "out", "description": "", "type": "Vec3"}], "return": {"description": "The \"out\" vector object", "type": "Vec3"}, "class": "Trimesh"}, {"file": "src/shapes/Trimesh.js", "line": 295, "description": "Get raw vertex i", "access": "private", "tagname": "", "itemtype": "method", "name": "_getUnscaledVertex", "params": [{"name": "i", "description": "", "type": "Number"}, {"name": "out", "description": "", "type": "Vec3"}], "return": {"description": "The \"out\" vector object", "type": "Vec3"}, "class": "Trimesh"}, {"file": "src/shapes/Trimesh.js", "line": 313, "description": "Get a vertex from the trimesh,transformed by the given position and quaternion.", "itemtype": "method", "name": "getWorldVertex", "params": [{"name": "i", "description": "", "type": "Number"}, {"name": "pos", "description": "", "type": "Vec3"}, {"name": "quat", "description": "", "type": "Quaternion"}, {"name": "out", "description": "", "type": "Vec3"}], "return": {"description": "The \"out\" vector object", "type": "Vec3"}, "class": "Trimesh"}, {"file": "src/shapes/Trimesh.js", "line": 328, "description": "Get the three vertices for triangle i.", "itemtype": "method", "name": "getTriangleVertices", "params": [{"name": "i", "description": "", "type": "Number"}, {"name": "a", "description": "", "type": "Vec3"}, {"name": "b", "description": "", "type": "Vec3"}, {"name": "c", "description": "", "type": "Vec3"}], "class": "Trimesh"}, {"file": "src/shapes/Trimesh.js", "line": 343, "description": "Compute the normal of triangle i.", "itemtype": "method", "name": "getNormal", "params": [{"name": "i", "description": "", "type": "Number"}, {"name": "target", "description": "", "type": "Vec3"}], "return": {"description": "The \"target\" vector object", "type": "Vec3"}, "class": "Trimesh"}, {"file": "src/shapes/Trimesh.js", "line": 361, "itemtype": "method", "name": "calculateLocalInertia", "params": [{"name": "mass", "description": "", "type": "Number"}, {"name": "target", "description": "", "type": "Vec3"}], "return": {"description": "The \"target\" vector object", "type": "Vec3"}, "class": "Trimesh"}, {"file": "src/shapes/Trimesh.js", "line": 383, "description": "Compute the local AABB for the trimesh", "itemtype": "method", "name": "computeLocalAABB", "params": [{"name": "aabb", "description": "", "type": "AABB"}], "class": "Trimesh"}, {"file": "src/shapes/Trimesh.js", "line": 423, "description": "Update the .aabb property", "itemtype": "method", "name": "updateAABB", "class": "Trimesh"}, {"file": "src/shapes/Trimesh.js", "line": 431, "description": "Will update the .boundingSphereRadius property", "itemtype": "method", "name": "updateBoundingSphereRadius", "class": "Trimesh"}, {"file": "src/shapes/Trimesh.js", "line": 454, "itemtype": "method", "name": "calculateWorldAABB", "params": [{"name": "pos", "description": "", "type": "Vec3"}, {"name": "quat", "description": "", "type": "Quaternion"}, {"name": "min", "description": "", "type": "Vec3"}, {"name": "max", "description": "", "type": "Vec3"}], "class": "Trimesh"}, {"file": "src/shapes/Trimesh.js", "line": 504, "description": "Get approximate volume", "itemtype": "method", "name": "volume", "return": {"description": "", "type": "Number"}, "class": "Trimesh"}, {"file": "src/shapes/Trimesh.js", "line": 513, "description": "Create a Trimesh instance, shaped as a torus.", "static": 1, "itemtype": "method", "name": "createTorus", "params": [{"name": "radius", "description": "", "type": "Number", "optional": true, "optdefault": "1"}, {"name": "tube", "description": "", "type": "Number", "optional": true, "optdefault": "0.5"}, {"name": "radialSegments", "description": "", "type": "Number", "optional": true, "optdefault": "8"}, {"name": "tubularSegments", "description": "", "type": "Number", "optional": true, "optdefault": "6"}, {"name": "arc", "description": "", "type": "Number", "optional": true, "optdefault": "6.283185307179586"}], "return": {"description": "A torus", "type": "Trimesh"}, "class": "Trimesh"}, {"file": "src/solver/GSSolver.js", "line": 19, "description": "The number of solver iterations determines quality of the constraints in the world. The more iterations, the more correct simulation. More iterations need more computations though. If you have a large gravity force in your world, you will need more iterations.", "itemtype": "property", "name": "iterations", "type": "{Number}", "todo": ["write more about solver and iterations in the wiki"], "class": "GSSolver"}, {"file": "src/solver/GSSolver.js", "line": 27, "description": "When tolerance is reached, the system is assumed to be converged.", "itemtype": "property", "name": "tolerance", "type": "{Number}", "class": "GSSolver"}, {"file": "src/solver/Solver.js", "line": 10, "description": "All equations to be solved", "itemtype": "property", "name": "equations", "type": "Array", "class": "Solver"}, {"file": "src/solver/Solver.js", "line": 17, "description": "Should be implemented in subclasses!", "itemtype": "method", "name": "solve", "params": [{"name": "dt", "description": "", "type": "Number"}, {"name": "world", "description": "", "type": "World"}], "class": "Solver"}, {"file": "src/solver/Solver.js", "line": 28, "description": "Add an equation", "itemtype": "method", "name": "addEquation", "params": [{"name": "eq", "description": "", "type": "Equation"}], "class": "Solver"}, {"file": "src/solver/Solver.js", "line": 39, "description": "Remove an equation", "itemtype": "method", "name": "removeEquation", "params": [{"name": "eq", "description": "", "type": "Equation"}], "class": "Solver"}, {"file": "src/solver/Solver.js", "line": 52, "description": "Add all equations", "itemtype": "method", "name": "removeAllEquations", "class": "Solver"}, {"file": "src/solver/SplitSolver.js", "line": 81, "description": "Solve the subsystems", "itemtype": "method", "name": "solve", "params": [{"name": "dt", "description": "", "type": "Number"}, {"name": "world", "description": "", "type": "World"}], "class": "SplitSolver"}, {"file": "src/utils/EventTarget.js", "line": 15, "description": "Add an event listener", "itemtype": "method", "name": "addEventListener", "params": [{"name": "type", "description": "", "type": "String"}, {"name": "listener", "description": "", "type": "Function"}], "return": {"description": "The self object, for chainability.", "type": "EventTarget"}, "class": "EventTarget"}, {"file": "src/utils/EventTarget.js", "line": 34, "description": "Check if an event listener is added", "itemtype": "method", "name": "hasEventListener", "params": [{"name": "type", "description": "", "type": "String"}, {"name": "listener", "description": "", "type": "Function"}], "return": {"description": "", "type": "Boolean"}, "class": "EventTarget"}, {"file": "src/utils/EventTarget.js", "line": 50, "description": "Remove an event listener", "itemtype": "method", "name": "removeEventListener", "params": [{"name": "type", "description": "", "type": "String"}, {"name": "listener", "description": "", "type": "Function"}], "return": {"description": "The self object, for chainability.", "type": "EventTarget"}, "class": "EventTarget"}, {"file": "src/utils/EventTarget.js", "line": 68, "description": "Emit an event.", "itemtype": "method", "name": "dispatchEvent", "params": [{"name": "event", "description": "", "type": "Object", "props": [{"name": "type", "description": "", "type": "String"}]}], "return": {"description": "The self object, for chainability.", "type": "EventTarget"}, "class": "EventTarget"}, {"file": "src/utils/Octree.js", "line": 15, "description": "The root node", "itemtype": "property", "name": "root", "type": "OctreeNode", "class": "OctreeNode"}, {"file": "src/utils/Octree.js", "line": 21, "description": "Boundary of this node", "itemtype": "property", "name": "aabb", "type": "AABB", "class": "OctreeNode"}, {"file": "src/utils/Octree.js", "line": 27, "description": "Contained data at the current node level.", "itemtype": "property", "name": "data", "type": "Array", "class": "OctreeNode"}, {"file": "src/utils/Octree.js", "line": 33, "description": "Children to this node", "itemtype": "property", "name": "children", "type": "Array", "class": "OctreeNode"}, {"file": "src/utils/Octree.js", "line": 53, "description": "Maximum subdivision depth", "itemtype": "property", "name": "max<PERSON><PERSON><PERSON>", "type": "Number", "class": "Octree"}, {"file": "src/utils/Octree.js", "line": 65, "description": "Insert data into this node", "itemtype": "method", "name": "insert", "params": [{"name": "aabb", "description": "", "type": "AABB"}, {"name": "elementData", "description": "", "type": "Object"}], "return": {"description": "True if successful, otherwise false", "type": "Boolean"}, "class": "Octree"}, {"file": "src/utils/Octree.js", "line": 112, "description": "Create 8 equally sized children nodes and put them in the .children array.", "itemtype": "method", "name": "subdivide", "class": "Octree"}, {"file": "src/utils/Octree.js", "line": 158, "description": "Get all data, potentially within an AABB", "itemtype": "method", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "aabb", "description": "", "type": "AABB"}, {"name": "result", "description": "", "type": "Array"}], "return": {"description": "The \"result\" object", "type": "Array"}, "class": "Octree"}, {"file": "src/utils/Octree.js", "line": 200, "description": "Get all data, potentially intersected by a ray.", "itemtype": "method", "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "ray", "description": "", "type": "<PERSON>"}, {"name": "treeTransform", "description": "", "type": "Transform"}, {"name": "result", "description": "", "type": "Array"}], "return": {"description": "The \"result\" object", "type": "Array"}, "class": "Octree"}, {"file": "src/utils/Octree.js", "line": 219, "itemtype": "method", "name": "removeEmptyNodes", "class": "Octree"}, {"file": "src/utils/Pool.js", "line": 9, "description": "The pooled objects", "itemtype": "property", "name": "objects", "type": "Array", "class": "Pool"}, {"file": "src/utils/Pool.js", "line": 15, "description": "Constructor of the objects", "itemtype": "property", "name": "type", "type": "Mixed", "class": "Pool"}, {"file": "src/utils/Pool.js", "line": 22, "description": "Release an object after use", "itemtype": "method", "name": "release", "params": [{"name": "obj", "description": "", "type": "Object"}], "class": "Pool"}, {"file": "src/utils/Pool.js", "line": 34, "description": "Get an object", "itemtype": "method", "name": "get", "return": {"description": "", "type": "Mixed"}, "class": "Pool"}, {"file": "src/utils/Pool.js", "line": 47, "description": "Construct an object. Should be implmented in each subclass.", "itemtype": "method", "name": "constructObject", "return": {"description": "", "type": "Mixed"}, "class": "Pool"}, {"file": "src/utils/TupleDictionary.js", "line": 9, "description": "The data storage", "itemtype": "property", "name": "data", "type": "{Object}", "class": "TupleDictionary"}, {"file": "src/utils/TupleDictionary.js", "line": 17, "itemtype": "method", "name": "get", "params": [{"name": "i", "description": "", "type": "Number"}, {"name": "j", "description": "", "type": "Number"}], "return": {"description": "", "type": "Number"}, "class": "TupleDictionary"}, {"file": "src/utils/TupleDictionary.js", "line": 33, "itemtype": "method", "name": "set", "params": [{"name": "i", "description": "", "type": "Number"}, {"name": "j", "description": "", "type": "Number"}, {"name": "value", "description": "", "type": "Number"}], "class": "TupleDictionary"}, {"file": "src/utils/TupleDictionary.js", "line": 55, "itemtype": "method", "name": "reset", "class": "TupleDictionary"}, {"file": "src/utils/Utils.js", "line": 5, "description": "Extend an options object with default values.", "static": 1, "itemtype": "method", "name": "defaults", "params": [{"name": "options", "description": "The options object. May be falsy: in this case, a new object is created and returned.", "type": "Object"}, {"name": "defaults", "description": "An object containing default values.", "type": "Object"}], "return": {"description": "The modified options object.", "type": "Object"}, "class": "Vec3Pool"}, {"file": "src/utils/Vec3Pool.js", "line": 17, "description": "Construct a vector", "itemtype": "method", "name": "constructObject", "return": {"description": "", "type": "Vec3"}, "class": "Vec3Pool"}, {"file": "src/world/Narrowphase.js", "line": 25, "description": "Internal storage of pooled contact points.", "itemtype": "property", "name": "contactPointPool", "type": "Array", "class": "Narrowphase"}, {"file": "src/world/Narrowphase.js", "line": 36, "description": "Pooled vectors.", "itemtype": "property", "name": "v3pool", "type": "Vec3Pool", "class": "Narrowphase"}, {"file": "src/world/Narrowphase.js", "line": 45, "itemtype": "property", "name": "enableFrictionReduction", "type": "Boolean", "class": "Narrowphase"}, {"file": "src/world/Narrowphase.js", "line": 51, "description": "Make a contact object, by using the internal pool or creating a new one.", "itemtype": "method", "name": "createContactEquation", "return": {"description": "", "type": "ContactEquation"}, "class": "Narrowphase"}, {"file": "src/world/Narrowphase.js", "line": 199, "description": "Generate all contacts between a list of body pairs", "itemtype": "method", "name": "getContacts", "params": [{"name": "p1", "description": "Array of body indices", "type": "Array"}, {"name": "p2", "description": "Array of body indices", "type": "Array"}, {"name": "world", "description": "", "type": "World"}, {"name": "result", "description": "Array to store generated contacts", "type": "Array"}, {"name": "oldcontacts", "description": "Optional. Array of reusable contact objects", "type": "Array"}], "class": "Narrowphase"}, {"file": "src/world/Narrowphase.js", "line": 308, "itemtype": "method", "name": "sphereSphere", "params": [{"name": "si", "description": "", "type": "<PERSON><PERSON><PERSON>"}, {"name": "sj", "description": "", "type": "<PERSON><PERSON><PERSON>"}, {"name": "xi", "description": "", "type": "Vec3"}, {"name": "xj", "description": "", "type": "Vec3"}, {"name": "qi", "description": "", "type": "Quaternion"}, {"name": "qj", "description": "", "type": "Quaternion"}, {"name": "bi", "description": "", "type": "Body"}, {"name": "bj", "description": "", "type": "Body"}], "class": "Narrowphase"}, {"file": "src/world/Narrowphase.js", "line": 345, "itemtype": "method", "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "si", "description": "", "type": "<PERSON><PERSON><PERSON>"}, {"name": "sj", "description": "", "type": "<PERSON><PERSON><PERSON>"}, {"name": "xi", "description": "", "type": "Vec3"}, {"name": "xj", "description": "", "type": "Vec3"}, {"name": "qi", "description": "", "type": "Quaternion"}, {"name": "qj", "description": "", "type": "Quaternion"}, {"name": "bi", "description": "", "type": "Body"}, {"name": "bj", "description": "", "type": "Body"}], "class": "Narrowphase"}, {"file": "src/world/Narrowphase.js", "line": 416, "itemtype": "method", "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "sphereShape", "description": "", "type": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "<PERSON><PERSON><PERSON>"}, {"name": "spherePos", "description": "", "type": "Vec3"}, {"name": "trimeshPos", "description": "", "type": "Vec3"}, {"name": "sphereQuat", "description": "", "type": "Quaternion"}, {"name": "trimeshQuat", "description": "", "type": "Quaternion"}, {"name": "sphereBody", "description": "", "type": "Body"}, {"name": "trimeshBody", "description": "", "type": "Body"}], "class": "Narrowphase"}, {"file": "src/world/Narrowphase.js", "line": 612, "itemtype": "method", "name": "spherePlane", "params": [{"name": "si", "description": "", "type": "<PERSON><PERSON><PERSON>"}, {"name": "sj", "description": "", "type": "<PERSON><PERSON><PERSON>"}, {"name": "xi", "description": "", "type": "Vec3"}, {"name": "xj", "description": "", "type": "Vec3"}, {"name": "qi", "description": "", "type": "Quaternion"}, {"name": "qj", "description": "", "type": "Quaternion"}, {"name": "bi", "description": "", "type": "Body"}, {"name": "bj", "description": "", "type": "Body"}], "class": "Narrowphase"}, {"file": "src/world/Narrowphase.js", "line": 708, "itemtype": "method", "name": "sphereBox", "params": [{"name": "si", "description": "", "type": "<PERSON><PERSON><PERSON>"}, {"name": "sj", "description": "", "type": "<PERSON><PERSON><PERSON>"}, {"name": "xi", "description": "", "type": "Vec3"}, {"name": "xj", "description": "", "type": "Vec3"}, {"name": "qi", "description": "", "type": "Quaternion"}, {"name": "qj", "description": "", "type": "Quaternion"}, {"name": "bi", "description": "", "type": "Body"}, {"name": "bj", "description": "", "type": "Body"}], "class": "Narrowphase"}, {"file": "src/world/Narrowphase.js", "line": 929, "itemtype": "method", "name": "sphereConvex", "params": [{"name": "si", "description": "", "type": "<PERSON><PERSON><PERSON>"}, {"name": "sj", "description": "", "type": "<PERSON><PERSON><PERSON>"}, {"name": "xi", "description": "", "type": "Vec3"}, {"name": "xj", "description": "", "type": "Vec3"}, {"name": "qi", "description": "", "type": "Quaternion"}, {"name": "qj", "description": "", "type": "Quaternion"}, {"name": "bi", "description": "", "type": "Body"}, {"name": "bj", "description": "", "type": "Body"}], "class": "Narrowphase"}, {"file": "src/world/Narrowphase.js", "line": 1152, "itemtype": "method", "name": "planeBox", "params": [{"name": "result", "description": "", "type": "Array"}, {"name": "si", "description": "", "type": "<PERSON><PERSON><PERSON>"}, {"name": "sj", "description": "", "type": "<PERSON><PERSON><PERSON>"}, {"name": "xi", "description": "", "type": "Vec3"}, {"name": "xj", "description": "", "type": "Vec3"}, {"name": "qi", "description": "", "type": "Quaternion"}, {"name": "qj", "description": "", "type": "Quaternion"}, {"name": "bi", "description": "", "type": "Body"}, {"name": "bj", "description": "", "type": "Body"}], "class": "Narrowphase"}, {"file": "src/world/Narrowphase.js", "line": 1176, "itemtype": "method", "name": "planeConvex", "params": [{"name": "si", "description": "", "type": "<PERSON><PERSON><PERSON>"}, {"name": "sj", "description": "", "type": "<PERSON><PERSON><PERSON>"}, {"name": "xi", "description": "", "type": "Vec3"}, {"name": "xj", "description": "", "type": "Vec3"}, {"name": "qi", "description": "", "type": "Quaternion"}, {"name": "qj", "description": "", "type": "Quaternion"}, {"name": "bi", "description": "", "type": "Body"}, {"name": "bj", "description": "", "type": "Body"}], "class": "Narrowphase"}, {"file": "src/world/Narrowphase.js", "line": 1252, "itemtype": "method", "name": "convexConvex", "params": [{"name": "si", "description": "", "type": "<PERSON><PERSON><PERSON>"}, {"name": "sj", "description": "", "type": "<PERSON><PERSON><PERSON>"}, {"name": "xi", "description": "", "type": "Vec3"}, {"name": "xj", "description": "", "type": "Vec3"}, {"name": "qi", "description": "", "type": "Quaternion"}, {"name": "qj", "description": "", "type": "Quaternion"}, {"name": "bi", "description": "", "type": "Body"}, {"name": "bj", "description": "", "type": "Body"}], "class": "Narrowphase"}, {"file": "src/world/Narrowphase.js", "line": 1309, "itemtype": "method", "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "result", "description": "", "type": "Array"}, {"name": "si", "description": "", "type": "<PERSON><PERSON><PERSON>"}, {"name": "sj", "description": "", "type": "<PERSON><PERSON><PERSON>"}, {"name": "xi", "description": "", "type": "Vec3"}, {"name": "xj", "description": "", "type": "Vec3"}, {"name": "qi", "description": "", "type": "Quaternion"}, {"name": "qj", "description": "", "type": "Quaternion"}, {"name": "bi", "description": "", "type": "Body"}, {"name": "bj", "description": "", "type": "Body"}], "class": "Narrowphase"}, {"file": "src/world/Narrowphase.js", "line": 1393, "itemtype": "method", "name": "particlePlane", "params": [{"name": "result", "description": "", "type": "Array"}, {"name": "si", "description": "", "type": "<PERSON><PERSON><PERSON>"}, {"name": "sj", "description": "", "type": "<PERSON><PERSON><PERSON>"}, {"name": "xi", "description": "", "type": "Vec3"}, {"name": "xj", "description": "", "type": "Vec3"}, {"name": "qi", "description": "", "type": "Quaternion"}, {"name": "qj", "description": "", "type": "Quaternion"}, {"name": "bi", "description": "", "type": "Body"}, {"name": "bj", "description": "", "type": "Body"}], "class": "Narrowphase"}, {"file": "src/world/Narrowphase.js", "line": 1434, "itemtype": "method", "name": "particleSphere", "params": [{"name": "result", "description": "", "type": "Array"}, {"name": "si", "description": "", "type": "<PERSON><PERSON><PERSON>"}, {"name": "sj", "description": "", "type": "<PERSON><PERSON><PERSON>"}, {"name": "xi", "description": "", "type": "Vec3"}, {"name": "xj", "description": "", "type": "Vec3"}, {"name": "qi", "description": "", "type": "Quaternion"}, {"name": "qj", "description": "", "type": "Quaternion"}, {"name": "bi", "description": "", "type": "Body"}, {"name": "bj", "description": "", "type": "Body"}], "class": "Narrowphase"}, {"file": "src/world/Narrowphase.js", "line": 1475, "itemtype": "method", "name": "convexParticle", "params": [{"name": "result", "description": "", "type": "Array"}, {"name": "si", "description": "", "type": "<PERSON><PERSON><PERSON>"}, {"name": "sj", "description": "", "type": "<PERSON><PERSON><PERSON>"}, {"name": "xi", "description": "", "type": "Vec3"}, {"name": "xj", "description": "", "type": "Vec3"}, {"name": "qi", "description": "", "type": "Quaternion"}, {"name": "qj", "description": "", "type": "Quaternion"}, {"name": "bi", "description": "", "type": "Body"}, {"name": "bj", "description": "", "type": "Body"}], "class": "Narrowphase"}, {"file": "src/world/Narrowphase.js", "line": 1573, "itemtype": "method", "name": "convexHeightfield", "class": "Narrowphase"}, {"file": "src/world/Narrowphase.js", "line": 1651, "itemtype": "method", "name": "sphereHeightfield", "class": "Narrowphase"}, {"file": "src/world/World.js", "line": 33, "description": "Currently / last used timestep. Is set to -1 if not available. This value is updated before each internal step, which means that it is \"fresh\" inside event callbacks.", "itemtype": "property", "name": "dt", "type": "Number", "class": "World"}, {"file": "src/world/World.js", "line": 39, "description": "Makes bodies go to sleep when they've been inactive", "itemtype": "property", "name": "allowSleep", "type": "{<PERSON><PERSON><PERSON>}", "class": "World"}, {"file": "src/world/World.js", "line": 46, "description": "All the current contacts (instances of ContactEquation) in the world.", "itemtype": "property", "name": "contacts", "type": "{Array}", "class": "World"}, {"file": "src/world/World.js", "line": 54, "description": "How often to normalize quaternions. Set to 0 for every step, 1 for every second etc.. A larger value increases performance. If bodies tend to explode, set to a smaller value (zero to be sure nothing can go wrong).", "itemtype": "property", "name": "quatNormalizeSkip", "type": "{Number}", "class": "World"}, {"file": "src/world/World.js", "line": 61, "description": "Set to true to use fast quaternion normalization. It is often enough accurate to use. If bodies tend to explode, set to false.", "itemtype": "property", "name": "quatNormalizeFast", "type": "{<PERSON><PERSON><PERSON>}", "see": ["Quaternion.normalizeFast", "Quaternion.normalize"], "class": "World"}, {"file": "src/world/World.js", "line": 70, "description": "The wall-clock time since simulation start", "itemtype": "property", "name": "time", "type": "{Number}", "class": "World"}, {"file": "src/world/World.js", "line": 77, "description": "Number of timesteps taken since start", "itemtype": "property", "name": "stepnumber", "type": "{Number}", "class": "World"}, {"file": "src/world/World.js", "line": 88, "itemtype": "property", "name": "gravity", "type": "{Vec3}", "class": "World"}, {"file": "src/world/World.js", "line": 94, "itemtype": "property", "name": "broadphase", "type": "{Broadphase}", "class": "World"}, {"file": "src/world/World.js", "line": 100, "itemtype": "property", "name": "bodies", "type": "{Array}", "class": "World"}, {"file": "src/world/World.js", "line": 106, "itemtype": "property", "name": "solver", "type": "{Solver}", "class": "World"}, {"file": "src/world/World.js", "line": 112, "itemtype": "property", "name": "constraints", "type": "{Array}", "class": "World"}, {"file": "src/world/World.js", "line": 118, "itemtype": "property", "name": "narrowphase", "type": "{Narrowphase}", "class": "World"}, {"file": "src/world/World.js", "line": 124, "itemtype": "property", "name": "collisionMatrix", "type": "{ArrayCollisionMatrix}", "class": "World"}, {"file": "src/world/World.js", "line": 130, "description": "CollisionMatrix from the previous step.", "itemtype": "property", "name": "collisionMatrixPrevious", "type": "{ArrayCollisionMatrix}", "class": "World"}, {"file": "src/world/World.js", "line": 137, "description": "All added materials", "itemtype": "property", "name": "materials", "type": "{Array}", "class": "World"}, {"file": "src/world/World.js", "line": 144, "itemtype": "property", "name": "contactmaterials", "type": "{Array}", "class": "World"}, {"file": "src/world/World.js", "line": 150, "description": "Used to look up a ContactMaterial given two instances of Material.", "itemtype": "property", "name": "contactMaterialTable", "type": "TupleDictionary", "class": "World"}, {"file": "src/world/World.js", "line": 158, "description": "This contact material is used if no suitable contactmaterial is found for a contact.", "itemtype": "property", "name": "defaultContactMaterial", "type": "{ContactMaterial}", "class": "World"}, {"file": "src/world/World.js", "line": 165, "itemtype": "property", "name": "doProfiling", "type": "{<PERSON><PERSON><PERSON>}", "class": "World"}, {"file": "src/world/World.js", "line": 171, "itemtype": "property", "name": "profile", "type": "{Object}", "class": "World"}, {"file": "src/world/World.js", "line": 183, "itemtype": "property", "name": "subsystems", "type": "{Array}", "class": "World"}, {"file": "src/world/World.js", "line": 206, "description": "Get the contact material between materials m1 and m2", "itemtype": "method", "name": "getContactMaterial", "params": [{"name": "m1", "description": "", "type": "Material"}, {"name": "m2", "description": "", "type": "Material"}], "return": {"description": "The contact material if it was found.", "type": "ContactMaterial"}, "class": "World"}, {"file": "src/world/World.js", "line": 217, "description": "Get number of objects in the world.", "itemtype": "method", "name": "numObjects", "return": {"description": "", "type": "Number"}, "deprecated": true, "class": "World"}, {"file": "src/world/World.js", "line": 227, "description": "Store old collision state info", "itemtype": "method", "name": "collisionMatrixTick", "class": "World"}, {"file": "src/world/World.js", "line": 238, "description": "Add a rigid body to the simulation.", "itemtype": "method", "name": "add", "params": [{"name": "body", "description": "", "type": "Body"}], "todo": ["If the simulation has not yet started", "why recrete and copy arrays for each body? Accumulate in dynamic arrays in this case.", "Adding an array of bodies should be possible. This would save some loops too"], "deprecated": true, "deprecationMessage": "Use .addBody instead", "class": "World"}, {"file": "src/world/World.js", "line": 265, "description": "Add a constraint to the simulation.", "itemtype": "method", "name": "addConstraint", "params": [{"name": "c", "description": "", "type": "Constraint"}], "class": "World"}, {"file": "src/world/World.js", "line": 274, "description": "Removes a constraint", "itemtype": "method", "name": "removeConstraint", "params": [{"name": "c", "description": "", "type": "Constraint"}], "class": "World"}, {"file": "src/world/World.js", "line": 286, "description": "Raycast test", "itemtype": "method", "name": "rayTest", "params": [{"name": "from", "description": "", "type": "Vec3"}, {"name": "to", "description": "", "type": "Vec3"}, {"name": "result", "description": "", "type": "Function|RaycastResult"}], "deprecated": true, "deprecationMessage": "Use .raycastAll, .raycastClosest or .raycastAny instead.", "class": "World"}, {"file": "src/world/World.js", "line": 308, "description": "Ray cast against all bodies. The provided callback will be executed for each hit with a RaycastResult as single argument.", "itemtype": "method", "name": "raycastAll", "params": [{"name": "from", "description": "", "type": "Vec3"}, {"name": "to", "description": "", "type": "Vec3"}, {"name": "options", "description": "", "type": "Object", "props": [{"name": "collisionFilterMask", "description": "", "type": "Number", "optional": true, "optdefault": "-1"}, {"name": "collisionFilterGroup", "description": "", "type": "Number", "optional": true, "optdefault": "-1"}, {"name": "skipBackfaces", "description": "", "type": "Boolean", "optional": true, "optdefault": "false"}, {"name": "checkCollisionResponse", "description": "", "type": "Boolean", "optional": true, "optdefault": "true"}]}, {"name": "callback", "description": "", "type": "Function"}], "return": {"description": "True if any body was hit.", "type": "Boolean"}, "class": "World"}, {"file": "src/world/World.js", "line": 329, "description": "<PERSON> cast, and stop at the first result. Note that the order is random - but the method is fast.", "itemtype": "method", "name": "raycastAny", "params": [{"name": "from", "description": "", "type": "Vec3"}, {"name": "to", "description": "", "type": "Vec3"}, {"name": "options", "description": "", "type": "Object", "props": [{"name": "collisionFilterMask", "description": "", "type": "Number", "optional": true, "optdefault": "-1"}, {"name": "collisionFilterGroup", "description": "", "type": "Number", "optional": true, "optdefault": "-1"}, {"name": "skipBackfaces", "description": "", "type": "Boolean", "optional": true, "optdefault": "false"}, {"name": "checkCollisionResponse", "description": "", "type": "Boolean", "optional": true, "optdefault": "true"}]}, {"name": "result", "description": "", "type": "RaycastResult"}], "return": {"description": "True if any body was hit.", "type": "Boolean"}, "class": "World"}, {"file": "src/world/World.js", "line": 350, "description": "<PERSON> cast, and return information of the closest hit.", "itemtype": "method", "name": "raycastClosest", "params": [{"name": "from", "description": "", "type": "Vec3"}, {"name": "to", "description": "", "type": "Vec3"}, {"name": "options", "description": "", "type": "Object", "props": [{"name": "collisionFilterMask", "description": "", "type": "Number", "optional": true, "optdefault": "-1"}, {"name": "collisionFilterGroup", "description": "", "type": "Number", "optional": true, "optdefault": "-1"}, {"name": "skipBackfaces", "description": "", "type": "Boolean", "optional": true, "optdefault": "false"}, {"name": "checkCollisionResponse", "description": "", "type": "Boolean", "optional": true, "optdefault": "true"}]}, {"name": "result", "description": "", "type": "RaycastResult"}], "return": {"description": "True if any body was hit.", "type": "Boolean"}, "class": "World"}, {"file": "src/world/World.js", "line": 371, "description": "Remove a rigid body from the simulation.", "itemtype": "method", "name": "remove", "params": [{"name": "body", "description": "", "type": "Body"}], "deprecated": true, "deprecationMessage": "Use .removeBody instead", "class": "World"}, {"file": "src/world/World.js", "line": 396, "description": "Remove a rigid body from the simulation.", "itemtype": "method", "name": "removeBody", "params": [{"name": "body", "description": "", "type": "Body"}], "class": "World"}, {"file": "src/world/World.js", "line": 403, "description": "Adds a material to the World.", "itemtype": "method", "name": "addMaterial", "params": [{"name": "m", "description": "", "type": "Material"}], "todo": ["Necessary?"], "class": "World"}, {"file": "src/world/World.js", "line": 413, "description": "Adds a contact material to the World", "itemtype": "method", "name": "addContactMaterial", "params": [{"name": "cmat", "description": "", "type": "ContactMaterial"}], "class": "World"}, {"file": "src/world/World.js", "line": 443, "description": "Step the physics world forward in time.\n\nThere are two modes. The simple mode is fixed timestepping without interpolation. In this case you only use the first argument. The second case uses interpolation. In that you also provide the time since the function was last used, as well as the maximum fixed timesteps to take.", "itemtype": "method", "name": "step", "params": [{"name": "dt", "description": "The fixed time step size to use.", "type": "Number"}, {"name": "timeSinceLastCalled", "description": "The time elapsed since the function was last called.", "type": "Number", "optional": true}, {"name": "maxSubSteps", "description": "Maximum number of fixed steps to take per function call.", "type": "Number", "optional": true, "optdefault": "10"}], "example": ["\n    // fixed timestepping without interpolation\n    world.step(1/60);"], "see": ["http://bulletphysics.org/mediawiki-1.5.8/index.php/Stepping_The_World"], "class": "World"}, {"file": "src/world/World.js", "line": 517, "description": "Step the simulation", "itemtype": "method", "name": "step", "params": [{"name": "dt", "description": "", "type": "Number"}], "class": "World"}, {"file": "src/world/World.js", "line": 936, "description": "Sets all body forces in the world to zero.", "itemtype": "method", "name": "clearForces", "class": "World"}], "warnings": []}