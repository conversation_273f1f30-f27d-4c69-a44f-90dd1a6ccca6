<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Heightfield - cannon</title>
    <link rel="stylesheet" href="http://yui.yahooapis.com/3.9.1/build/cssgrids/cssgrids-min.css">
    <link rel="stylesheet" href="../assets/vendor/prettify/prettify-min.css">
    <link rel="stylesheet" href="../assets/css/main.css" id="site_styles">
    <link rel="icon" href="../assets/favicon.ico">
    <script src="http://yui.yahooapis.com/combo?3.9.1/build/yui/yui-min.js"></script>
</head>
<body class="yui3-skin-sam">

<div id="doc">
    <div id="hd" class="yui3-g header">
        <div class="yui3-u-3-4">
                <h1><img src="../assets/css/logo.png" title="cannon" width="117" height="52"></h1>
        </div>
        <div class="yui3-u-1-4 version">
            <em>API Docs for: 0.6.1</em>
        </div>
    </div>
    <div id="bd" class="yui3-g">

        <div class="yui3-u-1-4">
            <div id="docs-sidebar" class="sidebar apidocs">
                <div id="api-list">
                    <h2 class="off-left">APIs</h2>
                    <div id="api-tabview" class="tabview">
                        <ul class="tabs">
                            <li><a href="#api-classes">Classes</a></li>
                            <li><a href="#api-modules">Modules</a></li>
                        </ul>
                
                        <div id="api-tabview-filter">
                            <input type="search" id="api-filter" placeholder="Type to filter APIs">
                        </div>
                
                        <div id="api-tabview-panel">
                            <ul id="api-classes" class="apis classes">
                                <li><a href="../classes/AABB.html">AABB</a></li>
                                <li><a href="../classes/ArrayCollisionMatrix.html">ArrayCollisionMatrix</a></li>
                                <li><a href="../classes/Body.html">Body</a></li>
                                <li><a href="../classes/Box.html">Box</a></li>
                                <li><a href="../classes/Broadphase.html">Broadphase</a></li>
                                <li><a href="../classes/ConeEquation.html">ConeEquation</a></li>
                                <li><a href="../classes/ConeTwistConstraint.html">ConeTwistConstraint</a></li>
                                <li><a href="../classes/Constraint.html">Constraint</a></li>
                                <li><a href="../classes/ContactEquation.html">ContactEquation</a></li>
                                <li><a href="../classes/ContactMaterial.html">ContactMaterial</a></li>
                                <li><a href="../classes/ConvexPolyhedron.html">ConvexPolyhedron</a></li>
                                <li><a href="../classes/Cylinder.html">Cylinder</a></li>
                                <li><a href="../classes/Demo.html">Demo</a></li>
                                <li><a href="../classes/DistanceConstraint.html">DistanceConstraint</a></li>
                                <li><a href="../classes/Equation.html">Equation</a></li>
                                <li><a href="../classes/EventTarget.html">EventTarget</a></li>
                                <li><a href="../classes/FrictionEquation.html">FrictionEquation</a></li>
                                <li><a href="../classes/GridBroadphase.html">GridBroadphase</a></li>
                                <li><a href="../classes/GSSolver.html">GSSolver</a></li>
                                <li><a href="../classes/Heightfield.html">Heightfield</a></li>
                                <li><a href="../classes/HingeConstraint.html">HingeConstraint</a></li>
                                <li><a href="../classes/JacobianElement.html">JacobianElement</a></li>
                                <li><a href="../classes/LockConstraint.html">LockConstraint</a></li>
                                <li><a href="../classes/Mat3.html">Mat3</a></li>
                                <li><a href="../classes/Material.html">Material</a></li>
                                <li><a href="../classes/NaiveBroadphase.html">NaiveBroadphase</a></li>
                                <li><a href="../classes/Narrowphase.html">Narrowphase</a></li>
                                <li><a href="../classes/ObjectCollisionMatrix.html">ObjectCollisionMatrix</a></li>
                                <li><a href="../classes/Octree.html">Octree</a></li>
                                <li><a href="../classes/OctreeNode.html">OctreeNode</a></li>
                                <li><a href="../classes/Particle.html">Particle</a></li>
                                <li><a href="../classes/Plane.html">Plane</a></li>
                                <li><a href="../classes/PointToPointConstraint.html">PointToPointConstraint</a></li>
                                <li><a href="../classes/Pool.html">Pool</a></li>
                                <li><a href="../classes/Quaternion.html">Quaternion</a></li>
                                <li><a href="../classes/Ray.html">Ray</a></li>
                                <li><a href="../classes/RaycastResult.html">RaycastResult</a></li>
                                <li><a href="../classes/RaycastVehicle.html">RaycastVehicle</a></li>
                                <li><a href="../classes/RigidVehicle.html">RigidVehicle</a></li>
                                <li><a href="../classes/RotationalEquation.html">RotationalEquation</a></li>
                                <li><a href="../classes/RotationalMotorEquation.html">RotationalMotorEquation</a></li>
                                <li><a href="../classes/SAPBroadphase.html">SAPBroadphase</a></li>
                                <li><a href="../classes/Shape.html">Shape</a></li>
                                <li><a href="../classes/Solver.html">Solver</a></li>
                                <li><a href="../classes/Sphere.html">Sphere</a></li>
                                <li><a href="../classes/SPHSystem.html">SPHSystem</a></li>
                                <li><a href="../classes/SplitSolver.html">SplitSolver</a></li>
                                <li><a href="../classes/Spring.html">Spring</a></li>
                                <li><a href="../classes/Transform.html">Transform</a></li>
                                <li><a href="../classes/Trimesh.html">Trimesh</a></li>
                                <li><a href="../classes/TupleDictionary.html">TupleDictionary</a></li>
                                <li><a href="../classes/Vec3.html">Vec3</a></li>
                                <li><a href="../classes/Vec3Pool.html">Vec3Pool</a></li>
                                <li><a href="../classes/WheelInfo.html">WheelInfo</a></li>
                                <li><a href="../classes/World.html">World</a></li>
                            </ul>
                
                            <ul id="api-modules" class="apis modules">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="yui3-u-3-4">
                <div id="api-options">
                    Show:
                    <label for="api-show-inherited">
                        <input type="checkbox" id="api-show-inherited" checked>
                        Inherited
                    </label>
            
                    <label for="api-show-protected">
                        <input type="checkbox" id="api-show-protected">
                        Protected
                    </label>
            
                    <label for="api-show-private">
                        <input type="checkbox" id="api-show-private">
                        Private
                    </label>
                    <label for="api-show-deprecated">
                        <input type="checkbox" id="api-show-deprecated">
                        Deprecated
                    </label>
            
                </div>
            
            <div class="apidocs">
                <div id="docs-main">
                    <div class="content">
<h1>Heightfield Class</h1>
<div class="box meta">

        <div class="extends">
            Extends <a href="../classes/Shape.html" class="crosslink">Shape</a>
        </div>

        <div class="foundat">
            Defined in: <a href="../files/src_shapes_Heightfield.js.html#l8"><code>src&#x2F;shapes&#x2F;Heightfield.js:8</code></a>
        </div>


</div>


<div class="box intro">
    <p>Heightfield shape class. Height data is given as an array. These data points are spread out evenly with a given distance.</p>

</div>

    <div class="constructor">
        <h2>Constructor</h2>
        <div id="method_Heightfield" class="method item">
            <h3 class="name"><code>Heightfield</code></h3>
        
                <div class="args">
                    <span class="paren">(</span><ul class="args-list inline commas">
                        <li class="arg">
                                <code>data</code>
                        </li>
                        <li class="arg">
                                <code>options</code>
                        </li>
                    </ul><span class="paren">)</span>
                </div>
        
        
        
        
        
        
        
        
            <div class="meta">
                        <p>
                        Defined in
                <a href="../files/src_shapes_Heightfield.js.html#l8"><code>src&#x2F;shapes&#x2F;Heightfield.js:8</code></a>
                </p>
        
        
        
            </div>
        
            <div class="description">
                
            </div>
        
                <div class="params">
                    <h4>Parameters:</h4>
        
                    <ul class="params-list">
                        <li class="param">
                                <code class="param-name">data</code>
                                <span class="type">Array</span>
        
        
                            <div class="param-description">
                                <p>An array of Y values that will be used to construct the terrain.</p>
        
                            </div>
        
                        </li>
                        <li class="param">
                                <code class="param-name">options</code>
                                <span class="type">Object</span>
        
        
                            <div class="param-description">
                                 
                            </div>
        
                                <ul class="params-list">
                                    <li class="param">
                                            <code class="param-name optional">[minValue]</code>
                                            <span class="type">Number</span>
                                            <span class="flag optional" title="This parameter is optional.">optional</span>
        
                                        <div class="param-description">
                                            <p>Minimum value of the data points in the data array. Will be computed automatically if not given.</p>
        
                                        </div>
        
                                    </li>
                                    <li class="param">
                                            <code class="param-name optional">[maxValue]</code>
                                            <span class="type">Number</span>
                                            <span class="flag optional" title="This parameter is optional.">optional</span>
        
                                        <div class="param-description">
                                            <p>Maximum value.</p>
        
                                        </div>
        
                                    </li>
                                    <li class="param">
                                            <code class="param-name optional">[elementSize=0.1]</code>
                                            <span class="type">Number</span>
                                            <span class="flag optional" title="This parameter is optional.">optional</span>
        
                                        <div class="param-description">
                                            <p>World spacing between the data points in X direction.</p>
        
                                        </div>
        
                                    </li>
                                </ul>
                        </li>
                    </ul>
                </div>
        
        
        
                <div class="example">
                    <h4>Example:</h4>
        
                    <div class="example-content">
                        <pre class="code prettyprint"><code>// Generate some height data (y-values).
        var data = [];
        for(var i = 0; i &lt; 1000; i++){
            var y = 0.5 * Math.cos(0.2 * i);
            data.push(y);
        }
        
        // Create the heightfield shape
        var heightfieldShape = new Heightfield(data, {
            elementSize: 1 // Distance between the data points in X and Y directions
        });
        var heightfieldBody = new Body();
        heightfieldBody.addShape(heightfieldShape);
        world.addBody(heightfieldBody);
        </code></pre>
                    </div>
                </div>
        </div>
    </div>

<div id="classdocs" class="tabview">
    <ul class="api-class-tabs">
        <li class="api-class-tab index"><a href="#index">Index</a></li>

            <li class="api-class-tab methods"><a href="#methods">Methods</a></li>
            <li class="api-class-tab properties"><a href="#properties">Properties</a></li>
    </ul>

    <div>
        <div id="index" class="api-class-tabpanel index">
            <h2 class="off-left">Item Index</h2>

                <div class="index-section methods">
                    <h3>Methods</h3>

                    <ul class="index-list methods extends">
                            <li class="index-item method inherited">
                                <a href="#method_calculateLocalInertia">calculateLocalInertia</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_getConvexTrianglePillar">getConvexTrianglePillar</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_getIndexOfPosition">getIndexOfPosition</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_getRectMinMax">getRectMinMax</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_setHeightValueAtIndex">setHeightValueAtIndex</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_update">update</a>

                            </li>
                            <li class="index-item method inherited">
                                <a href="#method_updateBoundingSphereRadius">updateBoundingSphereRadius</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_updateMaxValue">updateMaxValue</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_updateMinValue">updateMinValue</a>

                            </li>
                            <li class="index-item method inherited">
                                <a href="#method_volume">volume</a>

                            </li>
                    </ul>
                </div>

                <div class="index-section properties">
                    <h3>Properties</h3>

                    <ul class="index-list properties extends">
                            <li class="index-item property inherited">
                                <a href="#property_boundingSphereRadius">boundingSphereRadius</a>

                            </li>
                            <li class="index-item property inherited">
                                <a href="#property_collisionResponse">collisionResponse</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_data">data</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_elementSize">elementSize</a>

                            </li>
                            <li class="index-item property inherited">
                                <a href="#property_id">id</a>

                            </li>
                            <li class="index-item property inherited">
                                <a href="#property_material">material</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_maxValue">maxValue</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_minValue">minValue</a>

                            </li>
                            <li class="index-item property inherited">
                                <a href="#property_type">type</a>

                            </li>
                    </ul>
                </div>


        </div>

            <div id="methods" class="api-class-tabpanel">
                <h2 class="off-left">Methods</h2>

<div id="method_calculateLocalInertia" class="method item inherited">
    <h3 class="name"><code>calculateLocalInertia</code></h3>

        <span class="paren">()</span>

        <span class="returns-inline">
            <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
        </span>







    <div class="meta">
                <p>Inherited from
                <a href="../classes/Shape.html#method_calculateLocalInertia">Shape</a>:
        <a href="../files/src_shapes_Shape.js.html#l68"><code>src&#x2F;shapes&#x2F;Shape.js:68</code></a>
        </p>



    </div>

    <div class="description">
        <p>Calculates the inertia in the local frame for this shape.</p>

    </div>


        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>:
            </div>
        </div>


</div>
<div id="method_getConvexTrianglePillar" class="method item">
    <h3 class="name"><code>getConvexTrianglePillar</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>i</code>
                </li>
                <li class="arg">
                        <code>j</code>
                </li>
                <li class="arg">
                        <code>getUpperTriangle</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_shapes_Heightfield.js.html#l257"><code>src&#x2F;shapes&#x2F;Heightfield.js:257</code></a>
        </p>



    </div>

    <div class="description">
        <p>Get a triangle in the terrain in the form of a triangular convex shape.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">i</code>
                        <span class="type">Integer</span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">j</code>
                        <span class="type">Integer</span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">getUpperTriangle</code>
                        <span class="type">Boolean</span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_getIndexOfPosition" class="method item">
    <h3 class="name"><code>getIndexOfPosition</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>x</code>
                </li>
                <li class="arg">
                        <code>y</code>
                </li>
                <li class="arg">
                        <code>result</code>
                </li>
                <li class="arg">
                        <code>clamp</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type">Boolean</span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_shapes_Heightfield.js.html#l191"><code>src&#x2F;shapes&#x2F;Heightfield.js:191</code></a>
        </p>



    </div>

    <div class="description">
        <p>Get the index of a local position on the heightfield. The indexes indicate the rectangles, so if your terrain is made of N x N height data points, you will have rectangle indexes ranging from 0 to N-1.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">x</code>
                        <span class="type">Number</span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">y</code>
                        <span class="type">Number</span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">result</code>
                        <span class="type">Array</span>


                    <div class="param-description">
                        <p>Two-element array</p>

                    </div>

                </li>
                <li class="param">
                        <code class="param-name">clamp</code>
                        <span class="type">Boolean</span>


                    <div class="param-description">
                        <p>If the position should be clamped to the heightfield edge.</p>

                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type">Boolean</span>:
            </div>
        </div>


</div>
<div id="method_getRectMinMax" class="method item">
    <h3 class="name"><code>getRectMinMax</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>iMinX</code>
                </li>
                <li class="arg">
                        <code>iMinY</code>
                </li>
                <li class="arg">
                        <code>iMaxX</code>
                </li>
                <li class="arg">
                        <code>iMaxY</code>
                </li>
                <li class="arg">
                        <code class="optional">[result]</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type">Array</span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_shapes_Heightfield.js.html#l162"><code>src&#x2F;shapes&#x2F;Heightfield.js:162</code></a>
        </p>



    </div>

    <div class="description">
        <p>Get max/min in a rectangle in the matrix data</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">iMinX</code>
                        <span class="type">Integer</span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">iMinY</code>
                        <span class="type">Integer</span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">iMaxX</code>
                        <span class="type">Integer</span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">iMaxY</code>
                        <span class="type">Integer</span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name optional">[result]</code>
                        <span class="type">Array</span>
                        <span class="flag optional" title="This parameter is optional.">optional</span>


                    <div class="param-description">
                        <p>An array to store the results in.</p>

                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type">Array</span>:
                    <p>The result array, if it was passed in. Minimum will be at position 0 and max at 1.</p>

            </div>
        </div>


</div>
<div id="method_setHeightValueAtIndex" class="method item">
    <h3 class="name"><code>setHeightValueAtIndex</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>xi</code>
                </li>
                <li class="arg">
                        <code>yi</code>
                </li>
                <li class="arg">
                        <code>value</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_shapes_Heightfield.js.html#l136"><code>src&#x2F;shapes&#x2F;Heightfield.js:136</code></a>
        </p>



    </div>

    <div class="description">
        <p>Set the height value at an index. Don&#39;t forget to update maxValue and minValue after you&#39;re done.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">xi</code>
                        <span class="type">Integer</span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">yi</code>
                        <span class="type">Integer</span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">value</code>
                        <span class="type">Number</span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_update" class="method item">
    <h3 class="name"><code>update</code></h3>

        <span class="paren">()</span>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_shapes_Heightfield.js.html#l92"><code>src&#x2F;shapes&#x2F;Heightfield.js:92</code></a>
        </p>



    </div>

    <div class="description">
        <p>Call whenever you change the data array.</p>

    </div>




</div>
<div id="method_updateBoundingSphereRadius" class="method item inherited">
    <h3 class="name"><code>updateBoundingSphereRadius</code></h3>

        <span class="paren">()</span>

        <span class="returns-inline">
            <span class="type">Number</span>
        </span>







    <div class="meta">
                <p>Inherited from
                <a href="../classes/Shape.html#method_updateBoundingSphereRadius">Shape</a>:
        <a href="../files/src_shapes_Shape.js.html#l50"><code>src&#x2F;shapes&#x2F;Shape.js:50</code></a>
        </p>



    </div>

    <div class="description">
        <p>Computes the bounding sphere radius. The result is stored in the property .boundingSphereRadius</p>

    </div>


        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type">Number</span>:
            </div>
        </div>


</div>
<div id="method_updateMaxValue" class="method item">
    <h3 class="name"><code>updateMaxValue</code></h3>

        <span class="paren">()</span>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_shapes_Heightfield.js.html#l118"><code>src&#x2F;shapes&#x2F;Heightfield.js:118</code></a>
        </p>



    </div>

    <div class="description">
        <p>Update the .maxValue property</p>

    </div>




</div>
<div id="method_updateMinValue" class="method item">
    <h3 class="name"><code>updateMinValue</code></h3>

        <span class="paren">()</span>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_shapes_Heightfield.js.html#l100"><code>src&#x2F;shapes&#x2F;Heightfield.js:100</code></a>
        </p>



    </div>

    <div class="description">
        <p>Update the .minValue property</p>

    </div>




</div>
<div id="method_volume" class="method item inherited">
    <h3 class="name"><code>volume</code></h3>

        <span class="paren">()</span>

        <span class="returns-inline">
            <span class="type">Number</span>
        </span>







    <div class="meta">
                <p>Inherited from
                <a href="../classes/Shape.html#method_volume">Shape</a>:
        <a href="../files/src_shapes_Shape.js.html#l59"><code>src&#x2F;shapes&#x2F;Shape.js:59</code></a>
        </p>



    </div>

    <div class="description">
        <p>Get the volume of this shape</p>

    </div>


        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type">Number</span>:
            </div>
        </div>


</div>
            </div>

            <div id="properties" class="api-class-tabpanel">
                <h2 class="off-left">Properties</h2>

                    <div id="property_boundingSphereRadius" class="property item inherited">
                        <h3 class="name"><code>boundingSphereRadius</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>Inherited from
                                    <a href="../classes/Shape.html#property_boundingSphereRadius">Shape</a>:
                            <a href="../files/src_shapes_Shape.js.html#l31"><code>src&#x2F;shapes&#x2F;Shape.js:31</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>The local bounding sphere radius of this shape.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_collisionResponse" class="property item inherited">
                        <h3 class="name"><code>collisionResponse</code></h3>
                        <span class="type">Boolean</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>Inherited from
                                    <a href="../classes/Shape.html#property_collisionResponse">Shape</a>:
                            <a href="../files/src_shapes_Shape.js.html#l37"><code>src&#x2F;shapes&#x2F;Shape.js:37</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Whether to produce contact forces when in contact with other bodies. Note that contacts will be generated, but they will be disabled.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_data" class="property item">
                        <h3 class="name"><code>data</code></h3>
                        <span class="type">Array</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_shapes_Heightfield.js.html#l43"><code>src&#x2F;shapes&#x2F;Heightfield.js:43</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>An array of numbers, or height values, that are spread out along the x axis.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_elementSize" class="property item">
                        <h3 class="name"><code>elementSize</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_shapes_Heightfield.js.html#l61"><code>src&#x2F;shapes&#x2F;Heightfield.js:61</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>The width of each element</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_id" class="property item inherited">
                        <h3 class="name"><code>id</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>Inherited from
                                    <a href="../classes/Shape.html#property_id">Shape</a>:
                            <a href="../files/src_shapes_Shape.js.html#l17"><code>src&#x2F;shapes&#x2F;Shape.js:17</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Identifyer of the Shape.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_material" class="property item inherited">
                        <h3 class="name"><code>material</code></h3>
                        <span class="type"><a href="../classes/Material.html" class="crosslink">Material</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>Inherited from
                                    <a href="../classes/Shape.html#property_material">Shape</a>:
                            <a href="../files/src_shapes_Shape.js.html#l43"><code>src&#x2F;shapes&#x2F;Shape.js:43</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_maxValue" class="property item">
                        <h3 class="name"><code>maxValue</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_shapes_Heightfield.js.html#l49"><code>src&#x2F;shapes&#x2F;Heightfield.js:49</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Max value of the data</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_minValue" class="property item">
                        <h3 class="name"><code>minValue</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_shapes_Heightfield.js.html#l55"><code>src&#x2F;shapes&#x2F;Heightfield.js:55</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Max value of the data</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_type" class="property item inherited">
                        <h3 class="name"><code>type</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>Inherited from
                                    <a href="../classes/Shape.html#property_type">Shape</a>:
                            <a href="../files/src_shapes_Shape.js.html#l23"><code>src&#x2F;shapes&#x2F;Shape.js:23</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>The type of this shape. Must be set to an int &gt; 0 by subclasses.</p>
                    
                        </div>
                    
                    
                    
                    </div>
            </div>


    </div>
</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="../assets/vendor/prettify/prettify-min.js"></script>
<script>prettyPrint();</script>
<script src="../assets/js/yui-prettify.js"></script>
<script src="../assets/../api.js"></script>
<script src="../assets/js/api-filter.js"></script>
<script src="../assets/js/api-list.js"></script>
<script src="../assets/js/api-search.js"></script>
<script src="../assets/js/apidocs.js"></script>
</body>
</html>
