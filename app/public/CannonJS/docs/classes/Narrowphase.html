<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Narrowphase - cannon</title>
    <link rel="stylesheet" href="http://yui.yahooapis.com/3.9.1/build/cssgrids/cssgrids-min.css">
    <link rel="stylesheet" href="../assets/vendor/prettify/prettify-min.css">
    <link rel="stylesheet" href="../assets/css/main.css" id="site_styles">
    <link rel="icon" href="../assets/favicon.ico">
    <script src="http://yui.yahooapis.com/combo?3.9.1/build/yui/yui-min.js"></script>
</head>
<body class="yui3-skin-sam">

<div id="doc">
    <div id="hd" class="yui3-g header">
        <div class="yui3-u-3-4">
                <h1><img src="../assets/css/logo.png" title="cannon" width="117" height="52"></h1>
        </div>
        <div class="yui3-u-1-4 version">
            <em>API Docs for: 0.6.1</em>
        </div>
    </div>
    <div id="bd" class="yui3-g">

        <div class="yui3-u-1-4">
            <div id="docs-sidebar" class="sidebar apidocs">
                <div id="api-list">
                    <h2 class="off-left">APIs</h2>
                    <div id="api-tabview" class="tabview">
                        <ul class="tabs">
                            <li><a href="#api-classes">Classes</a></li>
                            <li><a href="#api-modules">Modules</a></li>
                        </ul>
                
                        <div id="api-tabview-filter">
                            <input type="search" id="api-filter" placeholder="Type to filter APIs">
                        </div>
                
                        <div id="api-tabview-panel">
                            <ul id="api-classes" class="apis classes">
                                <li><a href="../classes/AABB.html">AABB</a></li>
                                <li><a href="../classes/ArrayCollisionMatrix.html">ArrayCollisionMatrix</a></li>
                                <li><a href="../classes/Body.html">Body</a></li>
                                <li><a href="../classes/Box.html">Box</a></li>
                                <li><a href="../classes/Broadphase.html">Broadphase</a></li>
                                <li><a href="../classes/ConeEquation.html">ConeEquation</a></li>
                                <li><a href="../classes/ConeTwistConstraint.html">ConeTwistConstraint</a></li>
                                <li><a href="../classes/Constraint.html">Constraint</a></li>
                                <li><a href="../classes/ContactEquation.html">ContactEquation</a></li>
                                <li><a href="../classes/ContactMaterial.html">ContactMaterial</a></li>
                                <li><a href="../classes/ConvexPolyhedron.html">ConvexPolyhedron</a></li>
                                <li><a href="../classes/Cylinder.html">Cylinder</a></li>
                                <li><a href="../classes/Demo.html">Demo</a></li>
                                <li><a href="../classes/DistanceConstraint.html">DistanceConstraint</a></li>
                                <li><a href="../classes/Equation.html">Equation</a></li>
                                <li><a href="../classes/EventTarget.html">EventTarget</a></li>
                                <li><a href="../classes/FrictionEquation.html">FrictionEquation</a></li>
                                <li><a href="../classes/GridBroadphase.html">GridBroadphase</a></li>
                                <li><a href="../classes/GSSolver.html">GSSolver</a></li>
                                <li><a href="../classes/Heightfield.html">Heightfield</a></li>
                                <li><a href="../classes/HingeConstraint.html">HingeConstraint</a></li>
                                <li><a href="../classes/JacobianElement.html">JacobianElement</a></li>
                                <li><a href="../classes/LockConstraint.html">LockConstraint</a></li>
                                <li><a href="../classes/Mat3.html">Mat3</a></li>
                                <li><a href="../classes/Material.html">Material</a></li>
                                <li><a href="../classes/NaiveBroadphase.html">NaiveBroadphase</a></li>
                                <li><a href="../classes/Narrowphase.html">Narrowphase</a></li>
                                <li><a href="../classes/ObjectCollisionMatrix.html">ObjectCollisionMatrix</a></li>
                                <li><a href="../classes/Octree.html">Octree</a></li>
                                <li><a href="../classes/OctreeNode.html">OctreeNode</a></li>
                                <li><a href="../classes/Particle.html">Particle</a></li>
                                <li><a href="../classes/Plane.html">Plane</a></li>
                                <li><a href="../classes/PointToPointConstraint.html">PointToPointConstraint</a></li>
                                <li><a href="../classes/Pool.html">Pool</a></li>
                                <li><a href="../classes/Quaternion.html">Quaternion</a></li>
                                <li><a href="../classes/Ray.html">Ray</a></li>
                                <li><a href="../classes/RaycastResult.html">RaycastResult</a></li>
                                <li><a href="../classes/RaycastVehicle.html">RaycastVehicle</a></li>
                                <li><a href="../classes/RigidVehicle.html">RigidVehicle</a></li>
                                <li><a href="../classes/RotationalEquation.html">RotationalEquation</a></li>
                                <li><a href="../classes/RotationalMotorEquation.html">RotationalMotorEquation</a></li>
                                <li><a href="../classes/SAPBroadphase.html">SAPBroadphase</a></li>
                                <li><a href="../classes/Shape.html">Shape</a></li>
                                <li><a href="../classes/Solver.html">Solver</a></li>
                                <li><a href="../classes/Sphere.html">Sphere</a></li>
                                <li><a href="../classes/SPHSystem.html">SPHSystem</a></li>
                                <li><a href="../classes/SplitSolver.html">SplitSolver</a></li>
                                <li><a href="../classes/Spring.html">Spring</a></li>
                                <li><a href="../classes/Transform.html">Transform</a></li>
                                <li><a href="../classes/Trimesh.html">Trimesh</a></li>
                                <li><a href="../classes/TupleDictionary.html">TupleDictionary</a></li>
                                <li><a href="../classes/Vec3.html">Vec3</a></li>
                                <li><a href="../classes/Vec3Pool.html">Vec3Pool</a></li>
                                <li><a href="../classes/WheelInfo.html">WheelInfo</a></li>
                                <li><a href="../classes/World.html">World</a></li>
                            </ul>
                
                            <ul id="api-modules" class="apis modules">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="yui3-u-3-4">
                <div id="api-options">
                    Show:
                    <label for="api-show-inherited">
                        <input type="checkbox" id="api-show-inherited" checked>
                        Inherited
                    </label>
            
                    <label for="api-show-protected">
                        <input type="checkbox" id="api-show-protected">
                        Protected
                    </label>
            
                    <label for="api-show-private">
                        <input type="checkbox" id="api-show-private">
                        Private
                    </label>
                    <label for="api-show-deprecated">
                        <input type="checkbox" id="api-show-deprecated">
                        Deprecated
                    </label>
            
                </div>
            
            <div class="apidocs">
                <div id="docs-main">
                    <div class="content">
<h1>Narrowphase Class</h1>
<div class="box meta">


        <div class="foundat">
            Defined in: <a href="../files/src_world_Narrowphase.js.html#l15"><code>src&#x2F;world&#x2F;Narrowphase.js:15</code></a>
        </div>


</div>


<div class="box intro">
    <p>Helper class for the World. Generates ContactEquations.</p>

</div>

    <div class="constructor">
        <h2>Constructor</h2>
        <div id="method_Narrowphase" class="method item">
            <h3 class="name"><code>Narrowphase</code></h3>
        
                <span class="paren">()</span>
        
        
        
        
        
        
        
        
            <div class="meta">
                        <p>
                        Defined in
                <a href="../files/src_world_Narrowphase.js.html#l15"><code>src&#x2F;world&#x2F;Narrowphase.js:15</code></a>
                </p>
        
        
        
            </div>
        
            <div class="description">
                
            </div>
        
        
        
        
        </div>
    </div>

<div id="classdocs" class="tabview">
    <ul class="api-class-tabs">
        <li class="api-class-tab index"><a href="#index">Index</a></li>

            <li class="api-class-tab methods"><a href="#methods">Methods</a></li>
            <li class="api-class-tab properties"><a href="#properties">Properties</a></li>
    </ul>

    <div>
        <div id="index" class="api-class-tabpanel index">
            <h2 class="off-left">Item Index</h2>

                <div class="index-section methods">
                    <h3>Methods</h3>

                    <ul class="index-list methods">
                            <li class="index-item method">
                                <a href="#method_convexConvex">convexConvex</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_convexHeightfield">convexHeightfield</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_convexParticle">convexParticle</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_convexTrimesh">convexTrimesh</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_createContactEquation">createContactEquation</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_getContacts">getContacts</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_particlePlane">particlePlane</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_particleSphere">particleSphere</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_planeBox">planeBox</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_planeConvex">planeConvex</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_planeTrimesh">planeTrimesh</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_sphereBox">sphereBox</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_sphereConvex">sphereConvex</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_sphereHeightfield">sphereHeightfield</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_spherePlane">spherePlane</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_sphereSphere">sphereSphere</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_sphereTrimesh">sphereTrimesh</a>

                            </li>
                    </ul>
                </div>

                <div class="index-section properties">
                    <h3>Properties</h3>

                    <ul class="index-list properties">
                            <li class="index-item property">
                                <a href="#property_contactPointPool">contactPointPool</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_enableFrictionReduction">enableFrictionReduction</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_v3pool">v3pool</a>

                            </li>
                    </ul>
                </div>


        </div>

            <div id="methods" class="api-class-tabpanel">
                <h2 class="off-left">Methods</h2>

<div id="method_convexConvex" class="method item">
    <h3 class="name"><code>convexConvex</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>si</code>
                </li>
                <li class="arg">
                        <code>sj</code>
                </li>
                <li class="arg">
                        <code>xi</code>
                </li>
                <li class="arg">
                        <code>xj</code>
                </li>
                <li class="arg">
                        <code>qi</code>
                </li>
                <li class="arg">
                        <code>qj</code>
                </li>
                <li class="arg">
                        <code>bi</code>
                </li>
                <li class="arg">
                        <code>bj</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_world_Narrowphase.js.html#l1252"><code>src&#x2F;world&#x2F;Narrowphase.js:1252</code></a>
        </p>



    </div>

    <div class="description">
        
    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">si</code>
                        <span class="type"><a href="../classes/Shape.html" class="crosslink">Shape</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">sj</code>
                        <span class="type"><a href="../classes/Shape.html" class="crosslink">Shape</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">xi</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">xj</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">qi</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">qj</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">bi</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">bj</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_convexHeightfield" class="method item">
    <h3 class="name"><code>convexHeightfield</code></h3>

        <span class="paren">()</span>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_world_Narrowphase.js.html#l1573"><code>src&#x2F;world&#x2F;Narrowphase.js:1573</code></a>
        </p>



    </div>

    <div class="description">
        
    </div>




</div>
<div id="method_convexParticle" class="method item">
    <h3 class="name"><code>convexParticle</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>result</code>
                </li>
                <li class="arg">
                        <code>si</code>
                </li>
                <li class="arg">
                        <code>sj</code>
                </li>
                <li class="arg">
                        <code>xi</code>
                </li>
                <li class="arg">
                        <code>xj</code>
                </li>
                <li class="arg">
                        <code>qi</code>
                </li>
                <li class="arg">
                        <code>qj</code>
                </li>
                <li class="arg">
                        <code>bi</code>
                </li>
                <li class="arg">
                        <code>bj</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_world_Narrowphase.js.html#l1475"><code>src&#x2F;world&#x2F;Narrowphase.js:1475</code></a>
        </p>



    </div>

    <div class="description">
        
    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">result</code>
                        <span class="type">Array</span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">si</code>
                        <span class="type"><a href="../classes/Shape.html" class="crosslink">Shape</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">sj</code>
                        <span class="type"><a href="../classes/Shape.html" class="crosslink">Shape</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">xi</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">xj</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">qi</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">qj</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">bi</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">bj</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_convexTrimesh" class="method item">
    <h3 class="name"><code>convexTrimesh</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>result</code>
                </li>
                <li class="arg">
                        <code>si</code>
                </li>
                <li class="arg">
                        <code>sj</code>
                </li>
                <li class="arg">
                        <code>xi</code>
                </li>
                <li class="arg">
                        <code>xj</code>
                </li>
                <li class="arg">
                        <code>qi</code>
                </li>
                <li class="arg">
                        <code>qj</code>
                </li>
                <li class="arg">
                        <code>bi</code>
                </li>
                <li class="arg">
                        <code>bj</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_world_Narrowphase.js.html#l1309"><code>src&#x2F;world&#x2F;Narrowphase.js:1309</code></a>
        </p>



    </div>

    <div class="description">
        
    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">result</code>
                        <span class="type">Array</span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">si</code>
                        <span class="type"><a href="../classes/Shape.html" class="crosslink">Shape</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">sj</code>
                        <span class="type"><a href="../classes/Shape.html" class="crosslink">Shape</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">xi</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">xj</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">qi</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">qj</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">bi</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">bj</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_createContactEquation" class="method item">
    <h3 class="name"><code>createContactEquation</code></h3>

        <span class="paren">()</span>

        <span class="returns-inline">
            <span class="type"><a href="../classes/ContactEquation.html" class="crosslink">ContactEquation</a></span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_world_Narrowphase.js.html#l51"><code>src&#x2F;world&#x2F;Narrowphase.js:51</code></a>
        </p>



    </div>

    <div class="description">
        <p>Make a contact object, by using the internal pool or creating a new one.</p>

    </div>


        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type"><a href="../classes/ContactEquation.html" class="crosslink">ContactEquation</a></span>:
            </div>
        </div>


</div>
<div id="method_getContacts" class="method item">
    <h3 class="name"><code>getContacts</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>p1</code>
                </li>
                <li class="arg">
                        <code>p2</code>
                </li>
                <li class="arg">
                        <code>world</code>
                </li>
                <li class="arg">
                        <code>result</code>
                </li>
                <li class="arg">
                        <code>oldcontacts</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_world_Narrowphase.js.html#l199"><code>src&#x2F;world&#x2F;Narrowphase.js:199</code></a>
        </p>



    </div>

    <div class="description">
        <p>Generate all contacts between a list of body pairs</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">p1</code>
                        <span class="type">Array</span>


                    <div class="param-description">
                        <p>Array of body indices</p>

                    </div>

                </li>
                <li class="param">
                        <code class="param-name">p2</code>
                        <span class="type">Array</span>


                    <div class="param-description">
                        <p>Array of body indices</p>

                    </div>

                </li>
                <li class="param">
                        <code class="param-name">world</code>
                        <span class="type"><a href="../classes/World.html" class="crosslink">World</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">result</code>
                        <span class="type">Array</span>


                    <div class="param-description">
                        <p>Array to store generated contacts</p>

                    </div>

                </li>
                <li class="param">
                        <code class="param-name">oldcontacts</code>
                        <span class="type">Array</span>


                    <div class="param-description">
                        <p>Optional. Array of reusable contact objects</p>

                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_particlePlane" class="method item">
    <h3 class="name"><code>particlePlane</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>result</code>
                </li>
                <li class="arg">
                        <code>si</code>
                </li>
                <li class="arg">
                        <code>sj</code>
                </li>
                <li class="arg">
                        <code>xi</code>
                </li>
                <li class="arg">
                        <code>xj</code>
                </li>
                <li class="arg">
                        <code>qi</code>
                </li>
                <li class="arg">
                        <code>qj</code>
                </li>
                <li class="arg">
                        <code>bi</code>
                </li>
                <li class="arg">
                        <code>bj</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_world_Narrowphase.js.html#l1393"><code>src&#x2F;world&#x2F;Narrowphase.js:1393</code></a>
        </p>



    </div>

    <div class="description">
        
    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">result</code>
                        <span class="type">Array</span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">si</code>
                        <span class="type"><a href="../classes/Shape.html" class="crosslink">Shape</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">sj</code>
                        <span class="type"><a href="../classes/Shape.html" class="crosslink">Shape</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">xi</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">xj</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">qi</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">qj</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">bi</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">bj</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_particleSphere" class="method item">
    <h3 class="name"><code>particleSphere</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>result</code>
                </li>
                <li class="arg">
                        <code>si</code>
                </li>
                <li class="arg">
                        <code>sj</code>
                </li>
                <li class="arg">
                        <code>xi</code>
                </li>
                <li class="arg">
                        <code>xj</code>
                </li>
                <li class="arg">
                        <code>qi</code>
                </li>
                <li class="arg">
                        <code>qj</code>
                </li>
                <li class="arg">
                        <code>bi</code>
                </li>
                <li class="arg">
                        <code>bj</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_world_Narrowphase.js.html#l1434"><code>src&#x2F;world&#x2F;Narrowphase.js:1434</code></a>
        </p>



    </div>

    <div class="description">
        
    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">result</code>
                        <span class="type">Array</span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">si</code>
                        <span class="type"><a href="../classes/Shape.html" class="crosslink">Shape</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">sj</code>
                        <span class="type"><a href="../classes/Shape.html" class="crosslink">Shape</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">xi</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">xj</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">qi</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">qj</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">bi</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">bj</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_planeBox" class="method item">
    <h3 class="name"><code>planeBox</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>result</code>
                </li>
                <li class="arg">
                        <code>si</code>
                </li>
                <li class="arg">
                        <code>sj</code>
                </li>
                <li class="arg">
                        <code>xi</code>
                </li>
                <li class="arg">
                        <code>xj</code>
                </li>
                <li class="arg">
                        <code>qi</code>
                </li>
                <li class="arg">
                        <code>qj</code>
                </li>
                <li class="arg">
                        <code>bi</code>
                </li>
                <li class="arg">
                        <code>bj</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_world_Narrowphase.js.html#l1152"><code>src&#x2F;world&#x2F;Narrowphase.js:1152</code></a>
        </p>



    </div>

    <div class="description">
        
    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">result</code>
                        <span class="type">Array</span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">si</code>
                        <span class="type"><a href="../classes/Shape.html" class="crosslink">Shape</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">sj</code>
                        <span class="type"><a href="../classes/Shape.html" class="crosslink">Shape</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">xi</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">xj</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">qi</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">qj</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">bi</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">bj</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_planeConvex" class="method item">
    <h3 class="name"><code>planeConvex</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>si</code>
                </li>
                <li class="arg">
                        <code>sj</code>
                </li>
                <li class="arg">
                        <code>xi</code>
                </li>
                <li class="arg">
                        <code>xj</code>
                </li>
                <li class="arg">
                        <code>qi</code>
                </li>
                <li class="arg">
                        <code>qj</code>
                </li>
                <li class="arg">
                        <code>bi</code>
                </li>
                <li class="arg">
                        <code>bj</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_world_Narrowphase.js.html#l1176"><code>src&#x2F;world&#x2F;Narrowphase.js:1176</code></a>
        </p>



    </div>

    <div class="description">
        
    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">si</code>
                        <span class="type"><a href="../classes/Shape.html" class="crosslink">Shape</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">sj</code>
                        <span class="type"><a href="../classes/Shape.html" class="crosslink">Shape</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">xi</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">xj</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">qi</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">qj</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">bi</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">bj</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_planeTrimesh" class="method item">
    <h3 class="name"><code>planeTrimesh</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>si</code>
                </li>
                <li class="arg">
                        <code>sj</code>
                </li>
                <li class="arg">
                        <code>xi</code>
                </li>
                <li class="arg">
                        <code>xj</code>
                </li>
                <li class="arg">
                        <code>qi</code>
                </li>
                <li class="arg">
                        <code>qj</code>
                </li>
                <li class="arg">
                        <code>bi</code>
                </li>
                <li class="arg">
                        <code>bj</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_world_Narrowphase.js.html#l345"><code>src&#x2F;world&#x2F;Narrowphase.js:345</code></a>
        </p>



    </div>

    <div class="description">
        
    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">si</code>
                        <span class="type"><a href="../classes/Shape.html" class="crosslink">Shape</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">sj</code>
                        <span class="type"><a href="../classes/Shape.html" class="crosslink">Shape</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">xi</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">xj</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">qi</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">qj</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">bi</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">bj</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_sphereBox" class="method item">
    <h3 class="name"><code>sphereBox</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>si</code>
                </li>
                <li class="arg">
                        <code>sj</code>
                </li>
                <li class="arg">
                        <code>xi</code>
                </li>
                <li class="arg">
                        <code>xj</code>
                </li>
                <li class="arg">
                        <code>qi</code>
                </li>
                <li class="arg">
                        <code>qj</code>
                </li>
                <li class="arg">
                        <code>bi</code>
                </li>
                <li class="arg">
                        <code>bj</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_world_Narrowphase.js.html#l708"><code>src&#x2F;world&#x2F;Narrowphase.js:708</code></a>
        </p>



    </div>

    <div class="description">
        
    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">si</code>
                        <span class="type"><a href="../classes/Shape.html" class="crosslink">Shape</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">sj</code>
                        <span class="type"><a href="../classes/Shape.html" class="crosslink">Shape</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">xi</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">xj</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">qi</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">qj</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">bi</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">bj</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_sphereConvex" class="method item">
    <h3 class="name"><code>sphereConvex</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>si</code>
                </li>
                <li class="arg">
                        <code>sj</code>
                </li>
                <li class="arg">
                        <code>xi</code>
                </li>
                <li class="arg">
                        <code>xj</code>
                </li>
                <li class="arg">
                        <code>qi</code>
                </li>
                <li class="arg">
                        <code>qj</code>
                </li>
                <li class="arg">
                        <code>bi</code>
                </li>
                <li class="arg">
                        <code>bj</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_world_Narrowphase.js.html#l929"><code>src&#x2F;world&#x2F;Narrowphase.js:929</code></a>
        </p>



    </div>

    <div class="description">
        
    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">si</code>
                        <span class="type"><a href="../classes/Shape.html" class="crosslink">Shape</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">sj</code>
                        <span class="type"><a href="../classes/Shape.html" class="crosslink">Shape</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">xi</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">xj</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">qi</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">qj</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">bi</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">bj</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_sphereHeightfield" class="method item">
    <h3 class="name"><code>sphereHeightfield</code></h3>

        <span class="paren">()</span>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_world_Narrowphase.js.html#l1651"><code>src&#x2F;world&#x2F;Narrowphase.js:1651</code></a>
        </p>



    </div>

    <div class="description">
        
    </div>




</div>
<div id="method_spherePlane" class="method item">
    <h3 class="name"><code>spherePlane</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>si</code>
                </li>
                <li class="arg">
                        <code>sj</code>
                </li>
                <li class="arg">
                        <code>xi</code>
                </li>
                <li class="arg">
                        <code>xj</code>
                </li>
                <li class="arg">
                        <code>qi</code>
                </li>
                <li class="arg">
                        <code>qj</code>
                </li>
                <li class="arg">
                        <code>bi</code>
                </li>
                <li class="arg">
                        <code>bj</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_world_Narrowphase.js.html#l612"><code>src&#x2F;world&#x2F;Narrowphase.js:612</code></a>
        </p>



    </div>

    <div class="description">
        
    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">si</code>
                        <span class="type"><a href="../classes/Shape.html" class="crosslink">Shape</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">sj</code>
                        <span class="type"><a href="../classes/Shape.html" class="crosslink">Shape</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">xi</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">xj</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">qi</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">qj</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">bi</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">bj</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_sphereSphere" class="method item">
    <h3 class="name"><code>sphereSphere</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>si</code>
                </li>
                <li class="arg">
                        <code>sj</code>
                </li>
                <li class="arg">
                        <code>xi</code>
                </li>
                <li class="arg">
                        <code>xj</code>
                </li>
                <li class="arg">
                        <code>qi</code>
                </li>
                <li class="arg">
                        <code>qj</code>
                </li>
                <li class="arg">
                        <code>bi</code>
                </li>
                <li class="arg">
                        <code>bj</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_world_Narrowphase.js.html#l308"><code>src&#x2F;world&#x2F;Narrowphase.js:308</code></a>
        </p>



    </div>

    <div class="description">
        
    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">si</code>
                        <span class="type"><a href="../classes/Shape.html" class="crosslink">Shape</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">sj</code>
                        <span class="type"><a href="../classes/Shape.html" class="crosslink">Shape</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">xi</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">xj</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">qi</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">qj</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">bi</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">bj</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_sphereTrimesh" class="method item">
    <h3 class="name"><code>sphereTrimesh</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>sphereShape</code>
                </li>
                <li class="arg">
                        <code>trimeshShape</code>
                </li>
                <li class="arg">
                        <code>spherePos</code>
                </li>
                <li class="arg">
                        <code>trimeshPos</code>
                </li>
                <li class="arg">
                        <code>sphereQuat</code>
                </li>
                <li class="arg">
                        <code>trimeshQuat</code>
                </li>
                <li class="arg">
                        <code>sphereBody</code>
                </li>
                <li class="arg">
                        <code>trimeshBody</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_world_Narrowphase.js.html#l416"><code>src&#x2F;world&#x2F;Narrowphase.js:416</code></a>
        </p>



    </div>

    <div class="description">
        
    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">sphereShape</code>
                        <span class="type"><a href="../classes/Shape.html" class="crosslink">Shape</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">trimeshShape</code>
                        <span class="type"><a href="../classes/Shape.html" class="crosslink">Shape</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">spherePos</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">trimeshPos</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">sphereQuat</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">trimeshQuat</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">sphereBody</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">trimeshBody</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
            </div>

            <div id="properties" class="api-class-tabpanel">
                <h2 class="off-left">Properties</h2>

                    <div id="property_contactPointPool" class="property item">
                        <h3 class="name"><code>contactPointPool</code></h3>
                        <span class="type">Array</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_world_Narrowphase.js.html#l25"><code>src&#x2F;world&#x2F;Narrowphase.js:25</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Internal storage of pooled contact points.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_enableFrictionReduction" class="property item">
                        <h3 class="name"><code>enableFrictionReduction</code></h3>
                        <span class="type">Boolean</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_world_Narrowphase.js.html#l45"><code>src&#x2F;world&#x2F;Narrowphase.js:45</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_v3pool" class="property item">
                        <h3 class="name"><code>v3pool</code></h3>
                        <span class="type"><a href="../classes/Vec3Pool.html" class="crosslink">Vec3Pool</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_world_Narrowphase.js.html#l36"><code>src&#x2F;world&#x2F;Narrowphase.js:36</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Pooled vectors.</p>
                    
                        </div>
                    
                    
                    
                    </div>
            </div>


    </div>
</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="../assets/vendor/prettify/prettify-min.js"></script>
<script>prettyPrint();</script>
<script src="../assets/js/yui-prettify.js"></script>
<script src="../assets/../api.js"></script>
<script src="../assets/js/api-filter.js"></script>
<script src="../assets/js/api-list.js"></script>
<script src="../assets/js/api-search.js"></script>
<script src="../assets/js/apidocs.js"></script>
</body>
</html>
