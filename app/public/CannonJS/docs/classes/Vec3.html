<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Vec3 - cannon</title>
    <link rel="stylesheet" href="http://yui.yahooapis.com/3.9.1/build/cssgrids/cssgrids-min.css">
    <link rel="stylesheet" href="../assets/vendor/prettify/prettify-min.css">
    <link rel="stylesheet" href="../assets/css/main.css" id="site_styles">
    <link rel="icon" href="../assets/favicon.ico">
    <script src="http://yui.yahooapis.com/combo?3.9.1/build/yui/yui-min.js"></script>
</head>
<body class="yui3-skin-sam">

<div id="doc">
    <div id="hd" class="yui3-g header">
        <div class="yui3-u-3-4">
                <h1><img src="../assets/css/logo.png" title="cannon" width="117" height="52"></h1>
        </div>
        <div class="yui3-u-1-4 version">
            <em>API Docs for: 0.6.1</em>
        </div>
    </div>
    <div id="bd" class="yui3-g">

        <div class="yui3-u-1-4">
            <div id="docs-sidebar" class="sidebar apidocs">
                <div id="api-list">
                    <h2 class="off-left">APIs</h2>
                    <div id="api-tabview" class="tabview">
                        <ul class="tabs">
                            <li><a href="#api-classes">Classes</a></li>
                            <li><a href="#api-modules">Modules</a></li>
                        </ul>
                
                        <div id="api-tabview-filter">
                            <input type="search" id="api-filter" placeholder="Type to filter APIs">
                        </div>
                
                        <div id="api-tabview-panel">
                            <ul id="api-classes" class="apis classes">
                                <li><a href="../classes/AABB.html">AABB</a></li>
                                <li><a href="../classes/ArrayCollisionMatrix.html">ArrayCollisionMatrix</a></li>
                                <li><a href="../classes/Body.html">Body</a></li>
                                <li><a href="../classes/Box.html">Box</a></li>
                                <li><a href="../classes/Broadphase.html">Broadphase</a></li>
                                <li><a href="../classes/ConeEquation.html">ConeEquation</a></li>
                                <li><a href="../classes/ConeTwistConstraint.html">ConeTwistConstraint</a></li>
                                <li><a href="../classes/Constraint.html">Constraint</a></li>
                                <li><a href="../classes/ContactEquation.html">ContactEquation</a></li>
                                <li><a href="../classes/ContactMaterial.html">ContactMaterial</a></li>
                                <li><a href="../classes/ConvexPolyhedron.html">ConvexPolyhedron</a></li>
                                <li><a href="../classes/Cylinder.html">Cylinder</a></li>
                                <li><a href="../classes/Demo.html">Demo</a></li>
                                <li><a href="../classes/DistanceConstraint.html">DistanceConstraint</a></li>
                                <li><a href="../classes/Equation.html">Equation</a></li>
                                <li><a href="../classes/EventTarget.html">EventTarget</a></li>
                                <li><a href="../classes/FrictionEquation.html">FrictionEquation</a></li>
                                <li><a href="../classes/GridBroadphase.html">GridBroadphase</a></li>
                                <li><a href="../classes/GSSolver.html">GSSolver</a></li>
                                <li><a href="../classes/Heightfield.html">Heightfield</a></li>
                                <li><a href="../classes/HingeConstraint.html">HingeConstraint</a></li>
                                <li><a href="../classes/JacobianElement.html">JacobianElement</a></li>
                                <li><a href="../classes/LockConstraint.html">LockConstraint</a></li>
                                <li><a href="../classes/Mat3.html">Mat3</a></li>
                                <li><a href="../classes/Material.html">Material</a></li>
                                <li><a href="../classes/NaiveBroadphase.html">NaiveBroadphase</a></li>
                                <li><a href="../classes/Narrowphase.html">Narrowphase</a></li>
                                <li><a href="../classes/ObjectCollisionMatrix.html">ObjectCollisionMatrix</a></li>
                                <li><a href="../classes/Octree.html">Octree</a></li>
                                <li><a href="../classes/OctreeNode.html">OctreeNode</a></li>
                                <li><a href="../classes/Particle.html">Particle</a></li>
                                <li><a href="../classes/Plane.html">Plane</a></li>
                                <li><a href="../classes/PointToPointConstraint.html">PointToPointConstraint</a></li>
                                <li><a href="../classes/Pool.html">Pool</a></li>
                                <li><a href="../classes/Quaternion.html">Quaternion</a></li>
                                <li><a href="../classes/Ray.html">Ray</a></li>
                                <li><a href="../classes/RaycastResult.html">RaycastResult</a></li>
                                <li><a href="../classes/RaycastVehicle.html">RaycastVehicle</a></li>
                                <li><a href="../classes/RigidVehicle.html">RigidVehicle</a></li>
                                <li><a href="../classes/RotationalEquation.html">RotationalEquation</a></li>
                                <li><a href="../classes/RotationalMotorEquation.html">RotationalMotorEquation</a></li>
                                <li><a href="../classes/SAPBroadphase.html">SAPBroadphase</a></li>
                                <li><a href="../classes/Shape.html">Shape</a></li>
                                <li><a href="../classes/Solver.html">Solver</a></li>
                                <li><a href="../classes/Sphere.html">Sphere</a></li>
                                <li><a href="../classes/SPHSystem.html">SPHSystem</a></li>
                                <li><a href="../classes/SplitSolver.html">SplitSolver</a></li>
                                <li><a href="../classes/Spring.html">Spring</a></li>
                                <li><a href="../classes/Transform.html">Transform</a></li>
                                <li><a href="../classes/Trimesh.html">Trimesh</a></li>
                                <li><a href="../classes/TupleDictionary.html">TupleDictionary</a></li>
                                <li><a href="../classes/Vec3.html">Vec3</a></li>
                                <li><a href="../classes/Vec3Pool.html">Vec3Pool</a></li>
                                <li><a href="../classes/WheelInfo.html">WheelInfo</a></li>
                                <li><a href="../classes/World.html">World</a></li>
                            </ul>
                
                            <ul id="api-modules" class="apis modules">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="yui3-u-3-4">
                <div id="api-options">
                    Show:
                    <label for="api-show-inherited">
                        <input type="checkbox" id="api-show-inherited" checked>
                        Inherited
                    </label>
            
                    <label for="api-show-protected">
                        <input type="checkbox" id="api-show-protected">
                        Protected
                    </label>
            
                    <label for="api-show-private">
                        <input type="checkbox" id="api-show-private">
                        Private
                    </label>
                    <label for="api-show-deprecated">
                        <input type="checkbox" id="api-show-deprecated">
                        Deprecated
                    </label>
            
                </div>
            
            <div class="apidocs">
                <div id="docs-main">
                    <div class="content">
<h1>Vec3 Class</h1>
<div class="box meta">


        <div class="foundat">
            Defined in: <a href="../files/src_math_Vec3.js.html#l5"><code>src&#x2F;math&#x2F;Vec3.js:5</code></a>
        </div>


</div>


<div class="box intro">
    <p>3-dimensional vector</p>

</div>

    <div class="constructor">
        <h2>Constructor</h2>
        <div id="method_Vec3" class="method item">
            <h3 class="name"><code>Vec3</code></h3>
        
                <div class="args">
                    <span class="paren">(</span><ul class="args-list inline commas">
                        <li class="arg">
                                <code>x</code>
                        </li>
                        <li class="arg">
                                <code>y</code>
                        </li>
                        <li class="arg">
                                <code>z</code>
                        </li>
                    </ul><span class="paren">)</span>
                </div>
        
        
        
        
        
        
        
        
            <div class="meta">
                        <p>
                        Defined in
                <a href="../files/src_math_Vec3.js.html#l5"><code>src&#x2F;math&#x2F;Vec3.js:5</code></a>
                </p>
        
        
        
            </div>
        
            <div class="description">
                
            </div>
        
                <div class="params">
                    <h4>Parameters:</h4>
        
                    <ul class="params-list">
                        <li class="param">
                                <code class="param-name">x</code>
                                <span class="type">Number</span>
        
        
                            <div class="param-description">
                                 
                            </div>
        
                        </li>
                        <li class="param">
                                <code class="param-name">y</code>
                                <span class="type">Number</span>
        
        
                            <div class="param-description">
                                 
                            </div>
        
                        </li>
                        <li class="param">
                                <code class="param-name">z</code>
                                <span class="type">Number</span>
        
        
                            <div class="param-description">
                                 
                            </div>
        
                        </li>
                    </ul>
                </div>
        
        
        
                <div class="example">
                    <h4>Example:</h4>
        
                    <div class="example-content">
                        <pre class="code prettyprint"><code>var v = new Vec3(1, 2, 3);
        console.log(&#39;x=&#39; + v.x); // x=1
        </code></pre>
                    </div>
                </div>
        </div>
    </div>

<div id="classdocs" class="tabview">
    <ul class="api-class-tabs">
        <li class="api-class-tab index"><a href="#index">Index</a></li>

            <li class="api-class-tab methods"><a href="#methods">Methods</a></li>
            <li class="api-class-tab properties"><a href="#properties">Properties</a></li>
    </ul>

    <div>
        <div id="index" class="api-class-tabpanel index">
            <h2 class="off-left">Item Index</h2>

                <div class="index-section methods">
                    <h3>Methods</h3>

                    <ul class="index-list methods">
                            <li class="index-item method">
                                <a href="#method_almostEquals">almostEquals</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_almostZero">almostZero</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_clone">clone</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_copy">copy</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_cross">cross</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_crossmat">crossmat</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_distanceSquared">distanceSquared</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_distanceTo">distanceTo</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_dot">dot</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_isAntiparallelTo">isAntiparallelTo</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_isZero">isZero</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_length">length</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_lengthSquared">lengthSquared</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_lerp">lerp</a>

                            </li>
                            <li class="index-item method deprecated">
                                <a href="#method_mult">mult</a>

                                    <span class="flag deprecated">deprecated</span>
                            </li>
                            <li class="index-item method">
                                <a href="#method_negate">negate</a>

                            </li>
                            <li class="index-item method deprecated">
                                <a href="#method_norm">norm</a>

                                    <span class="flag deprecated">deprecated</span>
                            </li>
                            <li class="index-item method deprecated">
                                <a href="#method_norm2">norm2</a>

                                    <span class="flag deprecated">deprecated</span>
                            </li>
                            <li class="index-item method">
                                <a href="#method_normalize">normalize</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_scale">scale</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_set">set</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_setZero">setZero</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_tangents">tangents</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_toArray">toArray</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_toString">toString</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_unit">unit</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_vadd">vadd</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_vsub">vsub</a>

                            </li>
                    </ul>
                </div>

                <div class="index-section properties">
                    <h3>Properties</h3>

                    <ul class="index-list properties">
                            <li class="index-item property">
                                <a href="#property_UNIT_X">UNIT_X</a>

                                    <span class="flag static">static</span>
                            </li>
                            <li class="index-item property">
                                <a href="#property_UNIT_Y">UNIT_Y</a>

                                    <span class="flag static">static</span>
                            </li>
                            <li class="index-item property">
                                <a href="#property_UNIT_Z">UNIT_Z</a>

                                    <span class="flag static">static</span>
                            </li>
                            <li class="index-item property">
                                <a href="#property_x">x</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_y">y</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_z">z</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_ZERO">ZERO</a>

                                    <span class="flag static">static</span>
                            </li>
                    </ul>
                </div>


        </div>

            <div id="methods" class="api-class-tabpanel">
                <h2 class="off-left">Methods</h2>

<div id="method_almostEquals" class="method item">
    <h3 class="name"><code>almostEquals</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>v</code>
                </li>
                <li class="arg">
                        <code>precision</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type"></span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Vec3.js.html#l395"><code>src&#x2F;math&#x2F;Vec3.js:395</code></a>
        </p>



    </div>

    <div class="description">
        <p>Check if a vector equals is almost equal to another one.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">v</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">precision</code>
                        <span class="type">Number</span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                    <p>bool</p>

            </div>
        </div>


</div>
<div id="method_almostZero" class="method item">
    <h3 class="name"><code>almostZero</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>precision</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Vec3.js.html#l414"><code>src&#x2F;math&#x2F;Vec3.js:414</code></a>
        </p>



    </div>

    <div class="description">
        <p>Check if a vector is almost zero</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">precision</code>
                        <span class="type">Number</span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_clone" class="method item">
    <h3 class="name"><code>clone</code></h3>

        <span class="paren">()</span>

        <span class="returns-inline">
            <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Vec3.js.html#l445"><code>src&#x2F;math&#x2F;Vec3.js:445</code></a>
        </p>



    </div>

    <div class="description">
        <p>Clone the vector</p>

    </div>


        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>:
            </div>
        </div>


</div>
<div id="method_copy" class="method item">
    <h3 class="name"><code>copy</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>source</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Vec3.js.html#l367"><code>src&#x2F;math&#x2F;Vec3.js:367</code></a>
        </p>



    </div>

    <div class="description">
        <p>Copies value of source to this vector.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">source</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>:
                    <p>this</p>

            </div>
        </div>


</div>
<div id="method_cross" class="method item">
    <h3 class="name"><code>cross</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>v</code>
                </li>
                <li class="arg">
                        <code>target</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Vec3.js.html#l61"><code>src&#x2F;math&#x2F;Vec3.js:61</code></a>
        </p>



    </div>

    <div class="description">
        <p>Vector cross product</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">v</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">target</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                        <p>Optional. Target to save in.</p>

                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>:
            </div>
        </div>


</div>
<div id="method_crossmat" class="method item">
    <h3 class="name"><code>crossmat</code></h3>

        <span class="paren">()</span>

        <span class="returns-inline">
            <span class="type"><a href="../classes/Mat3.html" class="crosslink">Mat3</a></span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Vec3.js.html#l140"><code>src&#x2F;math&#x2F;Vec3.js:140</code></a>
        </p>



    </div>

    <div class="description">
        <p>Get the cross product matrix a_cross from a vector, such that a x b = a_cross * b = c</p>

    </div>


        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type"><a href="../classes/Mat3.html" class="crosslink">Mat3</a></span>:
            </div>
        </div>


</div>
<div id="method_distanceSquared" class="method item">
    <h3 class="name"><code>distanceSquared</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>p</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type">Number</span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Vec3.js.html#l246"><code>src&#x2F;math&#x2F;Vec3.js:246</code></a>
        </p>



    </div>

    <div class="description">
        <p>Get squared distance from this point to another point</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">p</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type">Number</span>:
            </div>
        </div>


</div>
<div id="method_distanceTo" class="method item">
    <h3 class="name"><code>distanceTo</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>p</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type">Number</span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Vec3.js.html#l232"><code>src&#x2F;math&#x2F;Vec3.js:232</code></a>
        </p>



    </div>

    <div class="description">
        <p>Get distance from this point to another point</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">p</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type">Number</span>:
            </div>
        </div>


</div>
<div id="method_dot" class="method item">
    <h3 class="name"><code>dot</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>v</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type">Number</span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Vec3.js.html#l287"><code>src&#x2F;math&#x2F;Vec3.js:287</code></a>
        </p>



    </div>

    <div class="description">
        <p>Calculate dot product</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">v</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type">Number</span>:
            </div>
        </div>


</div>
<div id="method_isAntiparallelTo" class="method item">
    <h3 class="name"><code>isAntiparallelTo</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>v</code>
                </li>
                <li class="arg">
                        <code>precision</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type">Boolean</span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Vec3.js.html#l433"><code>src&#x2F;math&#x2F;Vec3.js:433</code></a>
        </p>



    </div>

    <div class="description">
        <p>Check if the vector is anti-parallel to another vector.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">v</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">precision</code>
                        <span class="type">Number</span>


                    <div class="param-description">
                        <p>Set to zero for exact comparisons</p>

                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type">Boolean</span>:
            </div>
        </div>


</div>
<div id="method_isZero" class="method item">
    <h3 class="name"><code>isZero</code></h3>

        <span class="paren">()</span>

        <span class="returns-inline">
            <span class="type"></span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Vec3.js.html#l297"><code>src&#x2F;math&#x2F;Vec3.js:297</code></a>
        </p>



    </div>

    <div class="description">
        
    </div>


        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                    <p>bool</p>

            </div>
        </div>


</div>
<div id="method_length" class="method item">
    <h3 class="name"><code>length</code></h3>

        <span class="paren">()</span>

        <span class="returns-inline">
            <span class="type">Number</span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Vec3.js.html#l208"><code>src&#x2F;math&#x2F;Vec3.js:208</code></a>
        </p>



    </div>

    <div class="description">
        <p>Get the length of the vector</p>

    </div>


        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type">Number</span>:
            </div>
        </div>


</div>
<div id="method_lengthSquared" class="method item">
    <h3 class="name"><code>lengthSquared</code></h3>

        <span class="paren">()</span>

        <span class="returns-inline">
            <span class="type">Number</span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Vec3.js.html#l225"><code>src&#x2F;math&#x2F;Vec3.js:225</code></a>
        </p>



    </div>

    <div class="description">
        <p>Get the squared length of the vector.</p>

    </div>


        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type">Number</span>:
            </div>
        </div>


</div>
<div id="method_lerp" class="method item">
    <h3 class="name"><code>lerp</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>v</code>
                </li>
                <li class="arg">
                        <code>t</code>
                </li>
                <li class="arg">
                        <code>target</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Vec3.js.html#l381"><code>src&#x2F;math&#x2F;Vec3.js:381</code></a>
        </p>



    </div>

    <div class="description">
        <p>Do a linear interpolation between two vectors</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">v</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">t</code>
                        <span class="type">Number</span>


                    <div class="param-description">
                        <p>A number between 0 and 1. 0 will make this function return u, and 1 will make it return v. Numbers in between will generate a vector in between them.</p>

                    </div>

                </li>
                <li class="param">
                        <code class="param-name">target</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_mult" class="method item deprecated">
    <h3 class="name"><code>mult</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>scalar</code>
                </li>
                <li class="arg">
                        <code>target</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
        </span>

        <span class="flag deprecated" title="Use .scale() instead">deprecated</span>






    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Vec3.js.html#l258"><code>src&#x2F;math&#x2F;Vec3.js:258</code></a>
        </p>


            <p>Deprecated: Use .scale() instead</p>

    </div>

    <div class="description">
        <p>Multiply all the components of the vector with a scalar.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">scalar</code>
                        <span class="type">Number</span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">target</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                        <p>The vector to save the result in.</p>

                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>:
            </div>
        </div>


</div>
<div id="method_negate" class="method item">
    <h3 class="name"><code>negate</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>target</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Vec3.js.html#l305"><code>src&#x2F;math&#x2F;Vec3.js:305</code></a>
        </p>



    </div>

    <div class="description">
        <p>Make the vector point in the opposite direction.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">target</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                        <p>Optional target to save in</p>

                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>:
            </div>
        </div>


</div>
<div id="method_norm" class="method item deprecated">
    <h3 class="name"><code>norm</code></h3>

        <span class="paren">()</span>

        <span class="returns-inline">
            <span class="type">Number</span>
        </span>

        <span class="flag deprecated" title="Use .length() instead">deprecated</span>






    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Vec3.js.html#l197"><code>src&#x2F;math&#x2F;Vec3.js:197</code></a>
        </p>


            <p>Deprecated: Use .length() instead</p>

    </div>

    <div class="description">
        <p>Get the length of the vector</p>

    </div>


        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type">Number</span>:
            </div>
        </div>


</div>
<div id="method_norm2" class="method item deprecated">
    <h3 class="name"><code>norm2</code></h3>

        <span class="paren">()</span>

        <span class="returns-inline">
            <span class="type">Number</span>
        </span>

        <span class="flag deprecated" title="Use .lengthSquared() instead.">deprecated</span>






    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Vec3.js.html#l215"><code>src&#x2F;math&#x2F;Vec3.js:215</code></a>
        </p>


            <p>Deprecated: Use .lengthSquared() instead.</p>

    </div>

    <div class="description">
        <p>Get the squared length of the vector</p>

    </div>


        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type">Number</span>:
            </div>
        </div>


</div>
<div id="method_normalize" class="method item">
    <h3 class="name"><code>normalize</code></h3>

        <span class="paren">()</span>

        <span class="returns-inline">
            <span class="type">Number</span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Vec3.js.html#l152"><code>src&#x2F;math&#x2F;Vec3.js:152</code></a>
        </p>



    </div>

    <div class="description">
        <p>Normalize the vector. Note that this changes the values in the vector.</p>

    </div>


        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type">Number</span>:
                    <p>Returns the norm of the vector</p>

            </div>
        </div>


</div>
<div id="method_scale" class="method item">
    <h3 class="name"><code>scale</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>scalar</code>
                </li>
                <li class="arg">
                        <code>target</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Vec3.js.html#l278"><code>src&#x2F;math&#x2F;Vec3.js:278</code></a>
        </p>



    </div>

    <div class="description">
        <p>Multiply the vector with a scalar.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">scalar</code>
                        <span class="type">Number</span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">target</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>:
            </div>
        </div>


</div>
<div id="method_set" class="method item">
    <h3 class="name"><code>set</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>x</code>
                </li>
                <li class="arg">
                        <code>y</code>
                </li>
                <li class="arg">
                        <code>z</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type"></span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Vec3.js.html#l79"><code>src&#x2F;math&#x2F;Vec3.js:79</code></a>
        </p>



    </div>

    <div class="description">
        <p>Set the vectors&#39; 3 elements</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">x</code>
                        <span class="type">Number</span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">y</code>
                        <span class="type">Number</span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">z</code>
                        <span class="type">Number</span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                    <p>Vec3</p>

            </div>
        </div>


</div>
<div id="method_setZero" class="method item">
    <h3 class="name"><code>setZero</code></h3>

        <span class="paren">()</span>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Vec3.js.html#l94"><code>src&#x2F;math&#x2F;Vec3.js:94</code></a>
        </p>



    </div>

    <div class="description">
        <p>Set all components of the vector to zero.</p>

    </div>




</div>
<div id="method_tangents" class="method item">
    <h3 class="name"><code>tangents</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>t1</code>
                </li>
                <li class="arg">
                        <code>t2</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Vec3.js.html#l319"><code>src&#x2F;math&#x2F;Vec3.js:319</code></a>
        </p>



    </div>

    <div class="description">
        <p>Compute two artificial tangents to the vector</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">t1</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                        <p>Vector object to save the first tangent in</p>

                    </div>

                </li>
                <li class="param">
                        <code class="param-name">t2</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                        <p>Vector object to save the second tangent in</p>

                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_toArray" class="method item">
    <h3 class="name"><code>toArray</code></h3>

        <span class="paren">()</span>

        <span class="returns-inline">
            <span class="type"></span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Vec3.js.html#l358"><code>src&#x2F;math&#x2F;Vec3.js:358</code></a>
        </p>



    </div>

    <div class="description">
        <p>Converts to an array</p>

    </div>


        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                    <p>Array</p>

            </div>
        </div>


</div>
<div id="method_toString" class="method item">
    <h3 class="name"><code>toString</code></h3>

        <span class="paren">()</span>

        <span class="returns-inline">
            <span class="type"></span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Vec3.js.html#l349"><code>src&#x2F;math&#x2F;Vec3.js:349</code></a>
        </p>



    </div>

    <div class="description">
        <p>Converts to a more readable format</p>

    </div>


        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                    <p>string</p>

            </div>
        </div>


</div>
<div id="method_unit" class="method item">
    <h3 class="name"><code>unit</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>target</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Vec3.js.html#l174"><code>src&#x2F;math&#x2F;Vec3.js:174</code></a>
        </p>



    </div>

    <div class="description">
        <p>Get the version of this vector that is of length 1.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">target</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                        <p>Optional target to save in</p>

                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>:
                    <p>Returns the unit vector</p>

            </div>
        </div>


</div>
<div id="method_vadd" class="method item">
    <h3 class="name"><code>vadd</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>v</code>
                </li>
                <li class="arg">
                        <code>target</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Vec3.js.html#l102"><code>src&#x2F;math&#x2F;Vec3.js:102</code></a>
        </p>



    </div>

    <div class="description">
        <p>Vector addition</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">v</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">target</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                        <p>Optional.</p>

                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>:
            </div>
        </div>


</div>
<div id="method_vsub" class="method item">
    <h3 class="name"><code>vsub</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>v</code>
                </li>
                <li class="arg">
                        <code>target</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Vec3.js.html#l121"><code>src&#x2F;math&#x2F;Vec3.js:121</code></a>
        </p>



    </div>

    <div class="description">
        <p>Vector subtraction</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">v</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">target</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                        <p>Optional. Target to save in.</p>

                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>:
            </div>
        </div>


</div>
            </div>

            <div id="properties" class="api-class-tabpanel">
                <h2 class="off-left">Properties</h2>

                    <div id="property_UNIT_X" class="property item">
                        <h3 class="name"><code>UNIT_X</code></h3>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
                    
                    
                    
                    
                            <span class="flag static">static</span>
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_math_Vec3.js.html#l43"><code>src&#x2F;math&#x2F;Vec3.js:43</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_UNIT_Y" class="property item">
                        <h3 class="name"><code>UNIT_Y</code></h3>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
                    
                    
                    
                    
                            <span class="flag static">static</span>
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_math_Vec3.js.html#l49"><code>src&#x2F;math&#x2F;Vec3.js:49</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_UNIT_Z" class="property item">
                        <h3 class="name"><code>UNIT_Z</code></h3>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
                    
                    
                    
                    
                            <span class="flag static">static</span>
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_math_Vec3.js.html#l55"><code>src&#x2F;math&#x2F;Vec3.js:55</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_x" class="property item">
                        <h3 class="name"><code>x</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_math_Vec3.js.html#l18"><code>src&#x2F;math&#x2F;Vec3.js:18</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_y" class="property item">
                        <h3 class="name"><code>y</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_math_Vec3.js.html#l24"><code>src&#x2F;math&#x2F;Vec3.js:24</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_z" class="property item">
                        <h3 class="name"><code>z</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_math_Vec3.js.html#l30"><code>src&#x2F;math&#x2F;Vec3.js:30</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_ZERO" class="property item">
                        <h3 class="name"><code>ZERO</code></h3>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
                    
                    
                    
                    
                            <span class="flag static">static</span>
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_math_Vec3.js.html#l37"><code>src&#x2F;math&#x2F;Vec3.js:37</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
            </div>


    </div>
</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="../assets/vendor/prettify/prettify-min.js"></script>
<script>prettyPrint();</script>
<script src="../assets/js/yui-prettify.js"></script>
<script src="../assets/../api.js"></script>
<script src="../assets/js/api-filter.js"></script>
<script src="../assets/js/api-list.js"></script>
<script src="../assets/js/api-search.js"></script>
<script src="../assets/js/apidocs.js"></script>
</body>
</html>
