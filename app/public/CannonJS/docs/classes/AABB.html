<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>AABB - cannon</title>
    <link rel="stylesheet" href="http://yui.yahooapis.com/3.9.1/build/cssgrids/cssgrids-min.css">
    <link rel="stylesheet" href="../assets/vendor/prettify/prettify-min.css">
    <link rel="stylesheet" href="../assets/css/main.css" id="site_styles">
    <link rel="icon" href="../assets/favicon.ico">
    <script src="http://yui.yahooapis.com/combo?3.9.1/build/yui/yui-min.js"></script>
</head>
<body class="yui3-skin-sam">

<div id="doc">
    <div id="hd" class="yui3-g header">
        <div class="yui3-u-3-4">
                <h1><img src="../assets/css/logo.png" title="cannon" width="117" height="52"></h1>
        </div>
        <div class="yui3-u-1-4 version">
            <em>API Docs for: 0.6.1</em>
        </div>
    </div>
    <div id="bd" class="yui3-g">

        <div class="yui3-u-1-4">
            <div id="docs-sidebar" class="sidebar apidocs">
                <div id="api-list">
                    <h2 class="off-left">APIs</h2>
                    <div id="api-tabview" class="tabview">
                        <ul class="tabs">
                            <li><a href="#api-classes">Classes</a></li>
                            <li><a href="#api-modules">Modules</a></li>
                        </ul>
                
                        <div id="api-tabview-filter">
                            <input type="search" id="api-filter" placeholder="Type to filter APIs">
                        </div>
                
                        <div id="api-tabview-panel">
                            <ul id="api-classes" class="apis classes">
                                <li><a href="../classes/AABB.html">AABB</a></li>
                                <li><a href="../classes/ArrayCollisionMatrix.html">ArrayCollisionMatrix</a></li>
                                <li><a href="../classes/Body.html">Body</a></li>
                                <li><a href="../classes/Box.html">Box</a></li>
                                <li><a href="../classes/Broadphase.html">Broadphase</a></li>
                                <li><a href="../classes/ConeEquation.html">ConeEquation</a></li>
                                <li><a href="../classes/ConeTwistConstraint.html">ConeTwistConstraint</a></li>
                                <li><a href="../classes/Constraint.html">Constraint</a></li>
                                <li><a href="../classes/ContactEquation.html">ContactEquation</a></li>
                                <li><a href="../classes/ContactMaterial.html">ContactMaterial</a></li>
                                <li><a href="../classes/ConvexPolyhedron.html">ConvexPolyhedron</a></li>
                                <li><a href="../classes/Cylinder.html">Cylinder</a></li>
                                <li><a href="../classes/Demo.html">Demo</a></li>
                                <li><a href="../classes/DistanceConstraint.html">DistanceConstraint</a></li>
                                <li><a href="../classes/Equation.html">Equation</a></li>
                                <li><a href="../classes/EventTarget.html">EventTarget</a></li>
                                <li><a href="../classes/FrictionEquation.html">FrictionEquation</a></li>
                                <li><a href="../classes/GridBroadphase.html">GridBroadphase</a></li>
                                <li><a href="../classes/GSSolver.html">GSSolver</a></li>
                                <li><a href="../classes/Heightfield.html">Heightfield</a></li>
                                <li><a href="../classes/HingeConstraint.html">HingeConstraint</a></li>
                                <li><a href="../classes/JacobianElement.html">JacobianElement</a></li>
                                <li><a href="../classes/LockConstraint.html">LockConstraint</a></li>
                                <li><a href="../classes/Mat3.html">Mat3</a></li>
                                <li><a href="../classes/Material.html">Material</a></li>
                                <li><a href="../classes/NaiveBroadphase.html">NaiveBroadphase</a></li>
                                <li><a href="../classes/Narrowphase.html">Narrowphase</a></li>
                                <li><a href="../classes/ObjectCollisionMatrix.html">ObjectCollisionMatrix</a></li>
                                <li><a href="../classes/Octree.html">Octree</a></li>
                                <li><a href="../classes/OctreeNode.html">OctreeNode</a></li>
                                <li><a href="../classes/Particle.html">Particle</a></li>
                                <li><a href="../classes/Plane.html">Plane</a></li>
                                <li><a href="../classes/PointToPointConstraint.html">PointToPointConstraint</a></li>
                                <li><a href="../classes/Pool.html">Pool</a></li>
                                <li><a href="../classes/Quaternion.html">Quaternion</a></li>
                                <li><a href="../classes/Ray.html">Ray</a></li>
                                <li><a href="../classes/RaycastResult.html">RaycastResult</a></li>
                                <li><a href="../classes/RaycastVehicle.html">RaycastVehicle</a></li>
                                <li><a href="../classes/RigidVehicle.html">RigidVehicle</a></li>
                                <li><a href="../classes/RotationalEquation.html">RotationalEquation</a></li>
                                <li><a href="../classes/RotationalMotorEquation.html">RotationalMotorEquation</a></li>
                                <li><a href="../classes/SAPBroadphase.html">SAPBroadphase</a></li>
                                <li><a href="../classes/Shape.html">Shape</a></li>
                                <li><a href="../classes/Solver.html">Solver</a></li>
                                <li><a href="../classes/Sphere.html">Sphere</a></li>
                                <li><a href="../classes/SPHSystem.html">SPHSystem</a></li>
                                <li><a href="../classes/SplitSolver.html">SplitSolver</a></li>
                                <li><a href="../classes/Spring.html">Spring</a></li>
                                <li><a href="../classes/Transform.html">Transform</a></li>
                                <li><a href="../classes/Trimesh.html">Trimesh</a></li>
                                <li><a href="../classes/TupleDictionary.html">TupleDictionary</a></li>
                                <li><a href="../classes/Vec3.html">Vec3</a></li>
                                <li><a href="../classes/Vec3Pool.html">Vec3Pool</a></li>
                                <li><a href="../classes/WheelInfo.html">WheelInfo</a></li>
                                <li><a href="../classes/World.html">World</a></li>
                            </ul>
                
                            <ul id="api-modules" class="apis modules">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="yui3-u-3-4">
                <div id="api-options">
                    Show:
                    <label for="api-show-inherited">
                        <input type="checkbox" id="api-show-inherited" checked>
                        Inherited
                    </label>
            
                    <label for="api-show-protected">
                        <input type="checkbox" id="api-show-protected">
                        Protected
                    </label>
            
                    <label for="api-show-private">
                        <input type="checkbox" id="api-show-private">
                        Private
                    </label>
                    <label for="api-show-deprecated">
                        <input type="checkbox" id="api-show-deprecated">
                        Deprecated
                    </label>
            
                </div>
            
            <div class="apidocs">
                <div id="docs-main">
                    <div class="content">
<h1>AABB Class</h1>
<div class="box meta">


        <div class="foundat">
            Defined in: <a href="../files/src_collision_AABB.js.html#l6"><code>src&#x2F;collision&#x2F;AABB.js:6</code></a>
        </div>


</div>


<div class="box intro">
    <p>Axis aligned bounding box class.</p>

</div>

    <div class="constructor">
        <h2>Constructor</h2>
        <div id="method_AABB" class="method item">
            <h3 class="name"><code>AABB</code></h3>
        
                <div class="args">
                    <span class="paren">(</span><ul class="args-list inline commas">
                        <li class="arg">
                                <code class="optional">[options]</code>
                        </li>
                    </ul><span class="paren">)</span>
                </div>
        
        
        
        
        
        
        
        
            <div class="meta">
                        <p>
                        Defined in
                <a href="../files/src_collision_AABB.js.html#l6"><code>src&#x2F;collision&#x2F;AABB.js:6</code></a>
                </p>
        
        
        
            </div>
        
            <div class="description">
                
            </div>
        
                <div class="params">
                    <h4>Parameters:</h4>
        
                    <ul class="params-list">
                        <li class="param">
                                <code class="param-name optional">[options]</code>
                                <span class="type">Object</span>
                                <span class="flag optional" title="This parameter is optional.">optional</span>
        
        
                            <div class="param-description">
                                 
                            </div>
        
                                <ul class="params-list">
                                    <li class="param">
                                            <code class="param-name optional">[upperBound]</code>
                                            <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
                                            <span class="flag optional" title="This parameter is optional.">optional</span>
        
                                        <div class="param-description">
                                            
                                        </div>
        
                                    </li>
                                    <li class="param">
                                            <code class="param-name optional">[lowerBound]</code>
                                            <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
                                            <span class="flag optional" title="This parameter is optional.">optional</span>
        
                                        <div class="param-description">
                                            
                                        </div>
        
                                    </li>
                                </ul>
                        </li>
                    </ul>
                </div>
        
        
        
        </div>
    </div>

<div id="classdocs" class="tabview">
    <ul class="api-class-tabs">
        <li class="api-class-tab index"><a href="#index">Index</a></li>

            <li class="api-class-tab methods"><a href="#methods">Methods</a></li>
            <li class="api-class-tab properties"><a href="#properties">Properties</a></li>
    </ul>

    <div>
        <div id="index" class="api-class-tabpanel index">
            <h2 class="off-left">Item Index</h2>

                <div class="index-section methods">
                    <h3>Methods</h3>

                    <ul class="index-list methods">
                            <li class="index-item method">
                                <a href="#method_clone">clone</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_contains">contains</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_copy">copy</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_extend">extend</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_getCorners">getCorners</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_overlaps">overlaps</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_setFromPoints">setFromPoints</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_toLocalFrame">toLocalFrame</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_toWorldFrame">toWorldFrame</a>

                            </li>
                    </ul>
                </div>

                <div class="index-section properties">
                    <h3>Properties</h3>

                    <ul class="index-list properties">
                            <li class="index-item property">
                                <a href="#property_lowerBound">lowerBound</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_upperBound">upperBound</a>

                            </li>
                    </ul>
                </div>


        </div>

            <div id="methods" class="api-class-tabpanel">
                <h2 class="off-left">Methods</h2>

<div id="method_clone" class="method item">
    <h3 class="name"><code>clone</code></h3>

        <span class="paren">()</span>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_collision_AABB.js.html#l107"><code>src&#x2F;collision&#x2F;AABB.js:107</code></a>
        </p>



    </div>

    <div class="description">
        <p>Clone an AABB</p>

    </div>




</div>
<div id="method_contains" class="method item">
    <h3 class="name"><code>contains</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>aabb</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type">Boolean</span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_collision_AABB.js.html#l180"><code>src&#x2F;collision&#x2F;AABB.js:180</code></a>
        </p>



    </div>

    <div class="description">
        <p>Returns true if the given AABB is fully contained in this AABB.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">aabb</code>
                        <span class="type"><a href="../classes/AABB.html" class="crosslink">AABB</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type">Boolean</span>:
            </div>
        </div>


</div>
<div id="method_copy" class="method item">
    <h3 class="name"><code>copy</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>aabb</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type"><a href="../classes/AABB.html" class="crosslink">AABB</a></span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_collision_AABB.js.html#l95"><code>src&#x2F;collision&#x2F;AABB.js:95</code></a>
        </p>



    </div>

    <div class="description">
        <p>Copy bounds from an AABB to this AABB</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">aabb</code>
                        <span class="type"><a href="../classes/AABB.html" class="crosslink">AABB</a></span>


                    <div class="param-description">
                        <p>Source to copy from</p>

                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type"><a href="../classes/AABB.html" class="crosslink">AABB</a></span>:
                    <p>The this object, for chainability</p>

            </div>
        </div>


</div>
<div id="method_extend" class="method item">
    <h3 class="name"><code>extend</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>aabb</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_collision_AABB.js.html#l115"><code>src&#x2F;collision&#x2F;AABB.js:115</code></a>
        </p>



    </div>

    <div class="description">
        <p>Extend this AABB so that it covers the given AABB too.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">aabb</code>
                        <span class="type"><a href="../classes/AABB.html" class="crosslink">AABB</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_getCorners" class="method item">
    <h3 class="name"><code>getCorners</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>a</code>
                </li>
                <li class="arg">
                        <code>b</code>
                </li>
                <li class="arg">
                        <code>c</code>
                </li>
                <li class="arg">
                        <code>d</code>
                </li>
                <li class="arg">
                        <code>e</code>
                </li>
                <li class="arg">
                        <code>f</code>
                </li>
                <li class="arg">
                        <code>g</code>
                </li>
                <li class="arg">
                        <code>h</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_collision_AABB.js.html#l204"><code>src&#x2F;collision&#x2F;AABB.js:204</code></a>
        </p>



    </div>

    <div class="description">
        
    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">a</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">b</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">c</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">d</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">e</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">f</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">g</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">h</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_overlaps" class="method item">
    <h3 class="name"><code>overlaps</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>aabb</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type">Boolean</span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_collision_AABB.js.html#l158"><code>src&#x2F;collision&#x2F;AABB.js:158</code></a>
        </p>



    </div>

    <div class="description">
        <p>Returns true if the given AABB overlaps this AABB.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">aabb</code>
                        <span class="type"><a href="../classes/AABB.html" class="crosslink">AABB</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type">Boolean</span>:
            </div>
        </div>


</div>
<div id="method_setFromPoints" class="method item">
    <h3 class="name"><code>setFromPoints</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>points</code>
                </li>
                <li class="arg">
                        <code>position</code>
                </li>
                <li class="arg">
                        <code>quaternion</code>
                </li>
                <li class="arg">
                        <code>skinSize</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type"><a href="../classes/AABB.html" class="crosslink">AABB</a></span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_collision_AABB.js.html#l40"><code>src&#x2F;collision&#x2F;AABB.js:40</code></a>
        </p>



    </div>

    <div class="description">
        <p>Set the AABB bounds from a set of points.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">points</code>
                        <span class="type">Array</span>


                    <div class="param-description">
                        <p>An array of Vec3&#39;s.</p>

                    </div>

                </li>
                <li class="param">
                        <code class="param-name">position</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">quaternion</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">skinSize</code>
                        <span class="type">Number</span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type"><a href="../classes/AABB.html" class="crosslink">AABB</a></span>:
                    <p>The self object</p>

            </div>
        </div>


</div>
<div id="method_toLocalFrame" class="method item">
    <h3 class="name"><code>toLocalFrame</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>frame</code>
                </li>
                <li class="arg">
                        <code>target</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type"><a href="../classes/AABB.html" class="crosslink">AABB</a></span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_collision_AABB.js.html#l240"><code>src&#x2F;collision&#x2F;AABB.js:240</code></a>
        </p>



    </div>

    <div class="description">
        <p>Get the representation of an AABB in another frame.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">frame</code>
                        <span class="type"><a href="../classes/Transform.html" class="crosslink">Transform</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">target</code>
                        <span class="type"><a href="../classes/AABB.html" class="crosslink">AABB</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type"><a href="../classes/AABB.html" class="crosslink">AABB</a></span>:
                    <p>The &quot;target&quot; AABB object.</p>

            </div>
        </div>


</div>
<div id="method_toWorldFrame" class="method item">
    <h3 class="name"><code>toWorldFrame</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>frame</code>
                </li>
                <li class="arg">
                        <code>target</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type"><a href="../classes/AABB.html" class="crosslink">AABB</a></span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_collision_AABB.js.html#l271"><code>src&#x2F;collision&#x2F;AABB.js:271</code></a>
        </p>



    </div>

    <div class="description">
        <p>Get the representation of an AABB in the global frame.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">frame</code>
                        <span class="type"><a href="../classes/Transform.html" class="crosslink">Transform</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">target</code>
                        <span class="type"><a href="../classes/AABB.html" class="crosslink">AABB</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type"><a href="../classes/AABB.html" class="crosslink">AABB</a></span>:
                    <p>The &quot;target&quot; AABB object.</p>

            </div>
        </div>


</div>
            </div>

            <div id="properties" class="api-class-tabpanel">
                <h2 class="off-left">Properties</h2>

                    <div id="property_lowerBound" class="property item">
                        <h3 class="name"><code>lowerBound</code></h3>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_collision_AABB.js.html#l17"><code>src&#x2F;collision&#x2F;AABB.js:17</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>The lower bound of the bounding box.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_upperBound" class="property item">
                        <h3 class="name"><code>upperBound</code></h3>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_collision_AABB.js.html#l27"><code>src&#x2F;collision&#x2F;AABB.js:27</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>The upper bound of the bounding box.</p>
                    
                        </div>
                    
                    
                    
                    </div>
            </div>


    </div>
</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="../assets/vendor/prettify/prettify-min.js"></script>
<script>prettyPrint();</script>
<script src="../assets/js/yui-prettify.js"></script>
<script src="../assets/../api.js"></script>
<script src="../assets/js/api-filter.js"></script>
<script src="../assets/js/api-list.js"></script>
<script src="../assets/js/api-search.js"></script>
<script src="../assets/js/apidocs.js"></script>
</body>
</html>
