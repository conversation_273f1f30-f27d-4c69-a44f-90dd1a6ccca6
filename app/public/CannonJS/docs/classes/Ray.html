<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Ray - <PERSON></title>
    <link rel="stylesheet" href="http://yui.yahooapis.com/3.9.1/build/cssgrids/cssgrids-min.css">
    <link rel="stylesheet" href="../assets/vendor/prettify/prettify-min.css">
    <link rel="stylesheet" href="../assets/css/main.css" id="site_styles">
    <link rel="icon" href="../assets/favicon.ico">
    <script src="http://yui.yahooapis.com/combo?3.9.1/build/yui/yui-min.js"></script>
</head>
<body class="yui3-skin-sam">

<div id="doc">
    <div id="hd" class="yui3-g header">
        <div class="yui3-u-3-4">
                <h1><img src="../assets/css/logo.png" title="cannon" width="117" height="52"></h1>
        </div>
        <div class="yui3-u-1-4 version">
            <em>API Docs for: 0.6.1</em>
        </div>
    </div>
    <div id="bd" class="yui3-g">

        <div class="yui3-u-1-4">
            <div id="docs-sidebar" class="sidebar apidocs">
                <div id="api-list">
                    <h2 class="off-left">APIs</h2>
                    <div id="api-tabview" class="tabview">
                        <ul class="tabs">
                            <li><a href="#api-classes">Classes</a></li>
                            <li><a href="#api-modules">Modules</a></li>
                        </ul>
                
                        <div id="api-tabview-filter">
                            <input type="search" id="api-filter" placeholder="Type to filter APIs">
                        </div>
                
                        <div id="api-tabview-panel">
                            <ul id="api-classes" class="apis classes">
                                <li><a href="../classes/AABB.html">AABB</a></li>
                                <li><a href="../classes/ArrayCollisionMatrix.html">ArrayCollisionMatrix</a></li>
                                <li><a href="../classes/Body.html">Body</a></li>
                                <li><a href="../classes/Box.html">Box</a></li>
                                <li><a href="../classes/Broadphase.html">Broadphase</a></li>
                                <li><a href="../classes/ConeEquation.html">ConeEquation</a></li>
                                <li><a href="../classes/ConeTwistConstraint.html">ConeTwistConstraint</a></li>
                                <li><a href="../classes/Constraint.html">Constraint</a></li>
                                <li><a href="../classes/ContactEquation.html">ContactEquation</a></li>
                                <li><a href="../classes/ContactMaterial.html">ContactMaterial</a></li>
                                <li><a href="../classes/ConvexPolyhedron.html">ConvexPolyhedron</a></li>
                                <li><a href="../classes/Cylinder.html">Cylinder</a></li>
                                <li><a href="../classes/Demo.html">Demo</a></li>
                                <li><a href="../classes/DistanceConstraint.html">DistanceConstraint</a></li>
                                <li><a href="../classes/Equation.html">Equation</a></li>
                                <li><a href="../classes/EventTarget.html">EventTarget</a></li>
                                <li><a href="../classes/FrictionEquation.html">FrictionEquation</a></li>
                                <li><a href="../classes/GridBroadphase.html">GridBroadphase</a></li>
                                <li><a href="../classes/GSSolver.html">GSSolver</a></li>
                                <li><a href="../classes/Heightfield.html">Heightfield</a></li>
                                <li><a href="../classes/HingeConstraint.html">HingeConstraint</a></li>
                                <li><a href="../classes/JacobianElement.html">JacobianElement</a></li>
                                <li><a href="../classes/LockConstraint.html">LockConstraint</a></li>
                                <li><a href="../classes/Mat3.html">Mat3</a></li>
                                <li><a href="../classes/Material.html">Material</a></li>
                                <li><a href="../classes/NaiveBroadphase.html">NaiveBroadphase</a></li>
                                <li><a href="../classes/Narrowphase.html">Narrowphase</a></li>
                                <li><a href="../classes/ObjectCollisionMatrix.html">ObjectCollisionMatrix</a></li>
                                <li><a href="../classes/Octree.html">Octree</a></li>
                                <li><a href="../classes/OctreeNode.html">OctreeNode</a></li>
                                <li><a href="../classes/Particle.html">Particle</a></li>
                                <li><a href="../classes/Plane.html">Plane</a></li>
                                <li><a href="../classes/PointToPointConstraint.html">PointToPointConstraint</a></li>
                                <li><a href="../classes/Pool.html">Pool</a></li>
                                <li><a href="../classes/Quaternion.html">Quaternion</a></li>
                                <li><a href="../classes/Ray.html">Ray</a></li>
                                <li><a href="../classes/RaycastResult.html">RaycastResult</a></li>
                                <li><a href="../classes/RaycastVehicle.html">RaycastVehicle</a></li>
                                <li><a href="../classes/RigidVehicle.html">RigidVehicle</a></li>
                                <li><a href="../classes/RotationalEquation.html">RotationalEquation</a></li>
                                <li><a href="../classes/RotationalMotorEquation.html">RotationalMotorEquation</a></li>
                                <li><a href="../classes/SAPBroadphase.html">SAPBroadphase</a></li>
                                <li><a href="../classes/Shape.html">Shape</a></li>
                                <li><a href="../classes/Solver.html">Solver</a></li>
                                <li><a href="../classes/Sphere.html">Sphere</a></li>
                                <li><a href="../classes/SPHSystem.html">SPHSystem</a></li>
                                <li><a href="../classes/SplitSolver.html">SplitSolver</a></li>
                                <li><a href="../classes/Spring.html">Spring</a></li>
                                <li><a href="../classes/Transform.html">Transform</a></li>
                                <li><a href="../classes/Trimesh.html">Trimesh</a></li>
                                <li><a href="../classes/TupleDictionary.html">TupleDictionary</a></li>
                                <li><a href="../classes/Vec3.html">Vec3</a></li>
                                <li><a href="../classes/Vec3Pool.html">Vec3Pool</a></li>
                                <li><a href="../classes/WheelInfo.html">WheelInfo</a></li>
                                <li><a href="../classes/World.html">World</a></li>
                            </ul>
                
                            <ul id="api-modules" class="apis modules">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="yui3-u-3-4">
                <div id="api-options">
                    Show:
                    <label for="api-show-inherited">
                        <input type="checkbox" id="api-show-inherited" checked>
                        Inherited
                    </label>
            
                    <label for="api-show-protected">
                        <input type="checkbox" id="api-show-protected">
                        Protected
                    </label>
            
                    <label for="api-show-private">
                        <input type="checkbox" id="api-show-private">
                        Private
                    </label>
                    <label for="api-show-deprecated">
                        <input type="checkbox" id="api-show-deprecated">
                        Deprecated
                    </label>
            
                </div>
            
            <div class="apidocs">
                <div id="docs-main">
                    <div class="content">
<h1>Ray Class</h1>
<div class="box meta">


        <div class="foundat">
            Defined in: <a href="../files/src_collision_Ray.js.html#l12"><code>src&#x2F;collision&#x2F;Ray.js:12</code></a>
        </div>


</div>


<div class="box intro">
    <p>A line in 3D space that intersects bodies and return points.</p>

</div>

    <div class="constructor">
        <h2>Constructor</h2>
        <div id="method_Ray" class="method item">
            <h3 class="name"><code>Ray</code></h3>
        
                <div class="args">
                    <span class="paren">(</span><ul class="args-list inline commas">
                        <li class="arg">
                                <code>from</code>
                        </li>
                        <li class="arg">
                                <code>to</code>
                        </li>
                    </ul><span class="paren">)</span>
                </div>
        
        
        
        
        
        
        
        
            <div class="meta">
                        <p>
                        Defined in
                <a href="../files/src_collision_Ray.js.html#l12"><code>src&#x2F;collision&#x2F;Ray.js:12</code></a>
                </p>
        
        
        
            </div>
        
            <div class="description">
                
            </div>
        
                <div class="params">
                    <h4>Parameters:</h4>
        
                    <ul class="params-list">
                        <li class="param">
                                <code class="param-name">from</code>
                                <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
        
        
                            <div class="param-description">
                                 
                            </div>
        
                        </li>
                        <li class="param">
                                <code class="param-name">to</code>
                                <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
        
        
                            <div class="param-description">
                                 
                            </div>
        
                        </li>
                    </ul>
                </div>
        
        
        
        </div>
    </div>

<div id="classdocs" class="tabview">
    <ul class="api-class-tabs">
        <li class="api-class-tab index"><a href="#index">Index</a></li>

            <li class="api-class-tab methods"><a href="#methods">Methods</a></li>
            <li class="api-class-tab properties"><a href="#properties">Properties</a></li>
    </ul>

    <div>
        <div id="index" class="api-class-tabpanel index">
            <h2 class="off-left">Item Index</h2>

                <div class="index-section methods">
                    <h3>Methods</h3>

                    <ul class="index-list methods">
                            <li class="index-item method private">
                                <a href="#method__updateDirection">_updateDirection</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_getAABB">getAABB</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_intersectBodies">intersectBodies</a>

                            </li>
                            <li class="index-item method private">
                                <a href="#method_intersectBody">intersectBody</a>

                            </li>
                            <li class="index-item method private">
                                <a href="#method_intersectBox">intersectBox</a>

                            </li>
                            <li class="index-item method private">
                                <a href="#method_intersectConvex">intersectConvex</a>

                            </li>
                            <li class="index-item method private">
                                <a href="#method_intersectHeightfield">intersectHeightfield</a>

                            </li>
                            <li class="index-item method private">
                                <a href="#method_intersectPlane">intersectPlane</a>

                            </li>
                            <li class="index-item method private">
                                <a href="#method_intersectShape">intersectShape</a>

                            </li>
                            <li class="index-item method private">
                                <a href="#method_intersectSphere">intersectSphere</a>

                            </li>
                            <li class="index-item method private">
                                <a href="#method_intersectTrimesh">intersectTrimesh</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_intersectWorld">intersectWorld</a>

                            </li>
                            <li class="index-item method private">
                                <a href="#method_reportIntersection">reportIntersection</a>

                            </li>
                    </ul>
                </div>

                <div class="index-section properties">
                    <h3>Properties</h3>

                    <ul class="index-list properties">
                            <li class="index-item property private">
                                <a href="#property__direction">_direction</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_callback">callback</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_checkCollisionResponse">checkCollisionResponse</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_collisionFilterGroup">collisionFilterGroup</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_collisionFilterMask">collisionFilterMask</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_from">from</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_hasHit">hasHit</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_mode">mode</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_precision">precision</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_result">result</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_skipBackfaces">skipBackfaces</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_to">to</a>

                            </li>
                    </ul>
                </div>


        </div>

            <div id="methods" class="api-class-tabpanel">
                <h2 class="off-left">Methods</h2>

<div id="method__updateDirection" class="method item private">
    <h3 class="name"><code>_updateDirection</code></h3>

        <span class="paren">()</span>



        <span class="flag private">private</span>





    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_collision_Ray.js.html#l224"><code>src&#x2F;collision&#x2F;Ray.js:224</code></a>
        </p>



    </div>

    <div class="description">
        <p>Updates the _direction vector.</p>

    </div>




</div>
<div id="method_getAABB" class="method item">
    <h3 class="name"><code>getAABB</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>aabb</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_collision_Ray.js.html#l334"><code>src&#x2F;collision&#x2F;Ray.js:334</code></a>
        </p>



    </div>

    <div class="description">
        <p>Get the world AABB of the ray.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">aabb</code>
                        <span class="type"><a href="../classes/AABB.html" class="crosslink">AABB</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_intersectBodies" class="method item">
    <h3 class="name"><code>intersectBodies</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>bodies</code>
                </li>
                <li class="arg">
                        <code class="optional">[result]</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_collision_Ray.js.html#l208"><code>src&#x2F;collision&#x2F;Ray.js:208</code></a>
        </p>



    </div>

    <div class="description">
        
    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">bodies</code>
                        <span class="type">Array</span>


                    <div class="param-description">
                        <p>An array of Body objects.</p>

                    </div>

                </li>
                <li class="param">
                        <code class="param-name optional">[result]</code>
                        <span class="type"><a href="../classes/RaycastResult.html" class="crosslink">RaycastResult</a></span>
                        <span class="flag optional" title="This parameter is optional.">optional</span>


                    <div class="param-description">
                        <p>Deprecated</p>

                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_intersectBody" class="method item private">
    <h3 class="name"><code>intersectBody</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>body</code>
                </li>
                <li class="arg">
                        <code class="optional">[result]</code>
                </li>
            </ul><span class="paren">)</span>
        </div>



        <span class="flag private">private</span>





    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_collision_Ray.js.html#l157"><code>src&#x2F;collision&#x2F;Ray.js:157</code></a>
        </p>



    </div>

    <div class="description">
        <p>Shoot a ray at a body, get back information about the hit.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">body</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name optional">[result]</code>
                        <span class="type"><a href="../classes/RaycastResult.html" class="crosslink">RaycastResult</a></span>
                        <span class="flag optional" title="This parameter is optional.">optional</span>


                    <div class="param-description">
                        <p>Deprecated - set the result property of the Ray instead.</p>

                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_intersectBox" class="method item private">
    <h3 class="name"><code>intersectBox</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>shape</code>
                </li>
                <li class="arg">
                        <code>quat</code>
                </li>
                <li class="arg">
                        <code>position</code>
                </li>
                <li class="arg">
                        <code>body</code>
                </li>
            </ul><span class="paren">)</span>
        </div>



        <span class="flag private">private</span>





    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_collision_Ray.js.html#l269"><code>src&#x2F;collision&#x2F;Ray.js:269</code></a>
        </p>



    </div>

    <div class="description">
        
    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">shape</code>
                        <span class="type"><a href="../classes/Shape.html" class="crosslink">Shape</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">quat</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">position</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">body</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_intersectConvex" class="method item private">
    <h3 class="name"><code>intersectConvex</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>shape</code>
                </li>
                <li class="arg">
                        <code>quat</code>
                </li>
                <li class="arg">
                        <code>position</code>
                </li>
                <li class="arg">
                        <code>body</code>
                </li>
                <li class="arg">
                        <code class="optional">[options]</code>
                </li>
            </ul><span class="paren">)</span>
        </div>



        <span class="flag private">private</span>





    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_collision_Ray.js.html#l505"><code>src&#x2F;collision&#x2F;Ray.js:505</code></a>
        </p>



    </div>

    <div class="description">
        
    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">shape</code>
                        <span class="type"><a href="../classes/Shape.html" class="crosslink">Shape</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">quat</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">position</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">body</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name optional">[options]</code>
                        <span class="type">Object</span>
                        <span class="flag optional" title="This parameter is optional.">optional</span>


                    <div class="param-description">
                         
                    </div>

                        <ul class="params-list">
                            <li class="param">
                                    <code class="param-name optional">[faceList]</code>
                                    <span class="type">Array</span>
                                    <span class="flag optional" title="This parameter is optional.">optional</span>

                                <div class="param-description">
                                    
                                </div>

                            </li>
                        </ul>
                </li>
            </ul>
        </div>



</div>
<div id="method_intersectHeightfield" class="method item private">
    <h3 class="name"><code>intersectHeightfield</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>shape</code>
                </li>
                <li class="arg">
                        <code>quat</code>
                </li>
                <li class="arg">
                        <code>position</code>
                </li>
                <li class="arg">
                        <code>body</code>
                </li>
            </ul><span class="paren">)</span>
        </div>



        <span class="flag private">private</span>





    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_collision_Ray.js.html#l354"><code>src&#x2F;collision&#x2F;Ray.js:354</code></a>
        </p>



    </div>

    <div class="description">
        
    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">shape</code>
                        <span class="type"><a href="../classes/Shape.html" class="crosslink">Shape</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">quat</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">position</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">body</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_intersectPlane" class="method item private">
    <h3 class="name"><code>intersectPlane</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>shape</code>
                </li>
                <li class="arg">
                        <code>quat</code>
                </li>
                <li class="arg">
                        <code>position</code>
                </li>
                <li class="arg">
                        <code>body</code>
                </li>
            </ul><span class="paren">)</span>
        </div>



        <span class="flag private">private</span>





    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_collision_Ray.js.html#l282"><code>src&#x2F;collision&#x2F;Ray.js:282</code></a>
        </p>



    </div>

    <div class="description">
        
    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">shape</code>
                        <span class="type"><a href="../classes/Shape.html" class="crosslink">Shape</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">quat</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">position</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">body</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_intersectShape" class="method item private">
    <h3 class="name"><code>intersectShape</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>shape</code>
                </li>
                <li class="arg">
                        <code>quat</code>
                </li>
                <li class="arg">
                        <code>position</code>
                </li>
                <li class="arg">
                        <code>body</code>
                </li>
            </ul><span class="paren">)</span>
        </div>



        <span class="flag private">private</span>





    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_collision_Ray.js.html#l234"><code>src&#x2F;collision&#x2F;Ray.js:234</code></a>
        </p>



    </div>

    <div class="description">
        
    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">shape</code>
                        <span class="type"><a href="../classes/Shape.html" class="crosslink">Shape</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">quat</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">position</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">body</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_intersectSphere" class="method item private">
    <h3 class="name"><code>intersectSphere</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>shape</code>
                </li>
                <li class="arg">
                        <code>quat</code>
                </li>
                <li class="arg">
                        <code>position</code>
                </li>
                <li class="arg">
                        <code>body</code>
                </li>
            </ul><span class="paren">)</span>
        </div>



        <span class="flag private">private</span>





    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_collision_Ray.js.html#l439"><code>src&#x2F;collision&#x2F;Ray.js:439</code></a>
        </p>



    </div>

    <div class="description">
        
    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">shape</code>
                        <span class="type"><a href="../classes/Shape.html" class="crosslink">Shape</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">quat</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">position</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">body</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_intersectTrimesh" class="method item private">
    <h3 class="name"><code>intersectTrimesh</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>shape</code>
                </li>
                <li class="arg">
                        <code>quat</code>
                </li>
                <li class="arg">
                        <code>position</code>
                </li>
                <li class="arg">
                        <code>body</code>
                </li>
                <li class="arg">
                        <code class="optional">[options]</code>
                </li>
            </ul><span class="paren">)</span>
        </div>



        <span class="flag private">private</span>





    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_collision_Ray.js.html#l623"><code>src&#x2F;collision&#x2F;Ray.js:623</code></a>
        </p>



    </div>

    <div class="description">
        
    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">shape</code>
                        <span class="type"><a href="../classes/Shape.html" class="crosslink">Shape</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">quat</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">position</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">body</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name optional">[options]</code>
                        <span class="type">Object</span>
                        <span class="flag optional" title="This parameter is optional.">optional</span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_intersectWorld" class="method item">
    <h3 class="name"><code>intersectWorld</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>world</code>
                </li>
                <li class="arg">
                        <code>options</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type">Boolean</span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_collision_Ray.js.html#l99"><code>src&#x2F;collision&#x2F;Ray.js:99</code></a>
        </p>



    </div>

    <div class="description">
        <p>Do itersection against all bodies in the given World.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">world</code>
                        <span class="type"><a href="../classes/World.html" class="crosslink">World</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">options</code>
                        <span class="type">Object</span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type">Boolean</span>:
                    <p>True if the ray hit anything, otherwise false.</p>

            </div>
        </div>


</div>
<div id="method_reportIntersection" class="method item private">
    <h3 class="name"><code>reportIntersection</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>normal</code>
                </li>
                <li class="arg">
                        <code>hitPointWorld</code>
                </li>
                <li class="arg">
                        <code>shape</code>
                </li>
                <li class="arg">
                        <code>body</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type">Boolean</span>
        </span>


        <span class="flag private">private</span>





    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_collision_Ray.js.html#l738"><code>src&#x2F;collision&#x2F;Ray.js:738</code></a>
        </p>



    </div>

    <div class="description">
        
    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">normal</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">hitPointWorld</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">shape</code>
                        <span class="type"><a href="../classes/Shape.html" class="crosslink">Shape</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">body</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type">Boolean</span>:
                    <p>True if the intersections should continue</p>

            </div>
        </div>


</div>
            </div>

            <div id="properties" class="api-class-tabpanel">
                <h2 class="off-left">Properties</h2>

                    <div id="property__direction" class="property item private">
                        <h3 class="name"><code>_direction</code></h3>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
                    
                    
                            <span class="flag private">private</span>
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_collision_Ray.js.html#l30"><code>src&#x2F;collision&#x2F;Ray.js:30</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_callback" class="property item">
                        <h3 class="name"><code>callback</code></h3>
                        <span class="type">Function</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_collision_Ray.js.html#l84"><code>src&#x2F;collision&#x2F;Ray.js:84</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Current, user-provided result callback. Will be used if mode is Ray.ALL.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_checkCollisionResponse" class="property item">
                        <h3 class="name"><code>checkCollisionResponse</code></h3>
                        <span class="type">Boolean</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_collision_Ray.js.html#l42"><code>src&#x2F;collision&#x2F;Ray.js:42</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Set to true if you want the Ray to take .collisionResponse flags into account on bodies and shapes.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_collisionFilterGroup" class="property item">
                        <h3 class="name"><code>collisionFilterGroup</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_collision_Ray.js.html#l60"><code>src&#x2F;collision&#x2F;Ray.js:60</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                            <p><strong>Default:</strong> -1</p>
                    
                    
                    </div>
                    <div id="property_collisionFilterMask" class="property item">
                        <h3 class="name"><code>collisionFilterMask</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_collision_Ray.js.html#l54"><code>src&#x2F;collision&#x2F;Ray.js:54</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                            <p><strong>Default:</strong> -1</p>
                    
                    
                    </div>
                    <div id="property_from" class="property item">
                        <h3 class="name"><code>from</code></h3>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_collision_Ray.js.html#l20"><code>src&#x2F;collision&#x2F;Ray.js:20</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_hasHit" class="property item">
                        <h3 class="name"><code>hasHit</code></h3>
                        <span class="type">Boolean</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_collision_Ray.js.html#l78"><code>src&#x2F;collision&#x2F;Ray.js:78</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Will be set to true during intersectWorld() if the ray hit anything.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_mode" class="property item">
                        <h3 class="name"><code>mode</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_collision_Ray.js.html#l66"><code>src&#x2F;collision&#x2F;Ray.js:66</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>The intersection mode. Should be Ray.ANY, Ray.ALL or Ray.CLOSEST.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_precision" class="property item">
                        <h3 class="name"><code>precision</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_collision_Ray.js.html#l36"><code>src&#x2F;collision&#x2F;Ray.js:36</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>The precision of the ray. Used when checking parallelity etc.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_result" class="property item">
                        <h3 class="name"><code>result</code></h3>
                        <span class="type"><a href="../classes/RaycastResult.html" class="crosslink">RaycastResult</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_collision_Ray.js.html#l72"><code>src&#x2F;collision&#x2F;Ray.js:72</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Current result object.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_skipBackfaces" class="property item">
                        <h3 class="name"><code>skipBackfaces</code></h3>
                        <span class="type">Boolean</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_collision_Ray.js.html#l48"><code>src&#x2F;collision&#x2F;Ray.js:48</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>If set to true, the ray skips any hits with normal.dot(rayDirection) &lt; 0.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_to" class="property item">
                        <h3 class="name"><code>to</code></h3>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_collision_Ray.js.html#l25"><code>src&#x2F;collision&#x2F;Ray.js:25</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
            </div>


    </div>
</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="../assets/vendor/prettify/prettify-min.js"></script>
<script>prettyPrint();</script>
<script src="../assets/js/yui-prettify.js"></script>
<script src="../assets/../api.js"></script>
<script src="../assets/js/api-filter.js"></script>
<script src="../assets/js/api-list.js"></script>
<script src="../assets/js/api-search.js"></script>
<script src="../assets/js/apidocs.js"></script>
</body>
</html>
