<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title><PERSON><PERSON><PERSON> - cannon</title>
    <link rel="stylesheet" href="http://yui.yahooapis.com/3.9.1/build/cssgrids/cssgrids-min.css">
    <link rel="stylesheet" href="../assets/vendor/prettify/prettify-min.css">
    <link rel="stylesheet" href="../assets/css/main.css" id="site_styles">
    <link rel="icon" href="../assets/favicon.ico">
    <script src="http://yui.yahooapis.com/combo?3.9.1/build/yui/yui-min.js"></script>
</head>
<body class="yui3-skin-sam">

<div id="doc">
    <div id="hd" class="yui3-g header">
        <div class="yui3-u-3-4">
                <h1><img src="../assets/css/logo.png" title="cannon" width="117" height="52"></h1>
        </div>
        <div class="yui3-u-1-4 version">
            <em>API Docs for: 0.6.1</em>
        </div>
    </div>
    <div id="bd" class="yui3-g">

        <div class="yui3-u-1-4">
            <div id="docs-sidebar" class="sidebar apidocs">
                <div id="api-list">
                    <h2 class="off-left">APIs</h2>
                    <div id="api-tabview" class="tabview">
                        <ul class="tabs">
                            <li><a href="#api-classes">Classes</a></li>
                            <li><a href="#api-modules">Modules</a></li>
                        </ul>
                
                        <div id="api-tabview-filter">
                            <input type="search" id="api-filter" placeholder="Type to filter APIs">
                        </div>
                
                        <div id="api-tabview-panel">
                            <ul id="api-classes" class="apis classes">
                                <li><a href="../classes/AABB.html">AABB</a></li>
                                <li><a href="../classes/ArrayCollisionMatrix.html">ArrayCollisionMatrix</a></li>
                                <li><a href="../classes/Body.html">Body</a></li>
                                <li><a href="../classes/Box.html">Box</a></li>
                                <li><a href="../classes/Broadphase.html">Broadphase</a></li>
                                <li><a href="../classes/ConeEquation.html">ConeEquation</a></li>
                                <li><a href="../classes/ConeTwistConstraint.html">ConeTwistConstraint</a></li>
                                <li><a href="../classes/Constraint.html">Constraint</a></li>
                                <li><a href="../classes/ContactEquation.html">ContactEquation</a></li>
                                <li><a href="../classes/ContactMaterial.html">ContactMaterial</a></li>
                                <li><a href="../classes/ConvexPolyhedron.html">ConvexPolyhedron</a></li>
                                <li><a href="../classes/Cylinder.html">Cylinder</a></li>
                                <li><a href="../classes/Demo.html">Demo</a></li>
                                <li><a href="../classes/DistanceConstraint.html">DistanceConstraint</a></li>
                                <li><a href="../classes/Equation.html">Equation</a></li>
                                <li><a href="../classes/EventTarget.html">EventTarget</a></li>
                                <li><a href="../classes/FrictionEquation.html">FrictionEquation</a></li>
                                <li><a href="../classes/GridBroadphase.html">GridBroadphase</a></li>
                                <li><a href="../classes/GSSolver.html">GSSolver</a></li>
                                <li><a href="../classes/Heightfield.html">Heightfield</a></li>
                                <li><a href="../classes/HingeConstraint.html">HingeConstraint</a></li>
                                <li><a href="../classes/JacobianElement.html">JacobianElement</a></li>
                                <li><a href="../classes/LockConstraint.html">LockConstraint</a></li>
                                <li><a href="../classes/Mat3.html">Mat3</a></li>
                                <li><a href="../classes/Material.html">Material</a></li>
                                <li><a href="../classes/NaiveBroadphase.html">NaiveBroadphase</a></li>
                                <li><a href="../classes/Narrowphase.html">Narrowphase</a></li>
                                <li><a href="../classes/ObjectCollisionMatrix.html">ObjectCollisionMatrix</a></li>
                                <li><a href="../classes/Octree.html">Octree</a></li>
                                <li><a href="../classes/OctreeNode.html">OctreeNode</a></li>
                                <li><a href="../classes/Particle.html">Particle</a></li>
                                <li><a href="../classes/Plane.html">Plane</a></li>
                                <li><a href="../classes/PointToPointConstraint.html">PointToPointConstraint</a></li>
                                <li><a href="../classes/Pool.html">Pool</a></li>
                                <li><a href="../classes/Quaternion.html">Quaternion</a></li>
                                <li><a href="../classes/Ray.html">Ray</a></li>
                                <li><a href="../classes/RaycastResult.html">RaycastResult</a></li>
                                <li><a href="../classes/RaycastVehicle.html">RaycastVehicle</a></li>
                                <li><a href="../classes/RigidVehicle.html">RigidVehicle</a></li>
                                <li><a href="../classes/RotationalEquation.html">RotationalEquation</a></li>
                                <li><a href="../classes/RotationalMotorEquation.html">RotationalMotorEquation</a></li>
                                <li><a href="../classes/SAPBroadphase.html">SAPBroadphase</a></li>
                                <li><a href="../classes/Shape.html">Shape</a></li>
                                <li><a href="../classes/Solver.html">Solver</a></li>
                                <li><a href="../classes/Sphere.html">Sphere</a></li>
                                <li><a href="../classes/SPHSystem.html">SPHSystem</a></li>
                                <li><a href="../classes/SplitSolver.html">SplitSolver</a></li>
                                <li><a href="../classes/Spring.html">Spring</a></li>
                                <li><a href="../classes/Transform.html">Transform</a></li>
                                <li><a href="../classes/Trimesh.html">Trimesh</a></li>
                                <li><a href="../classes/TupleDictionary.html">TupleDictionary</a></li>
                                <li><a href="../classes/Vec3.html">Vec3</a></li>
                                <li><a href="../classes/Vec3Pool.html">Vec3Pool</a></li>
                                <li><a href="../classes/WheelInfo.html">WheelInfo</a></li>
                                <li><a href="../classes/World.html">World</a></li>
                            </ul>
                
                            <ul id="api-modules" class="apis modules">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="yui3-u-3-4">
                <div id="api-options">
                    Show:
                    <label for="api-show-inherited">
                        <input type="checkbox" id="api-show-inherited" checked>
                        Inherited
                    </label>
            
                    <label for="api-show-protected">
                        <input type="checkbox" id="api-show-protected">
                        Protected
                    </label>
            
                    <label for="api-show-private">
                        <input type="checkbox" id="api-show-private">
                        Private
                    </label>
                    <label for="api-show-deprecated">
                        <input type="checkbox" id="api-show-deprecated">
                        Deprecated
                    </label>
            
                </div>
            
            <div class="apidocs">
                <div id="docs-main">
                    <div class="content">
<h1>Cylinder Class</h1>
<div class="box meta">

        <div class="extends">
            Extends <a href="../classes/ConvexPolyhedron.html" class="crosslink">ConvexPolyhedron</a>
        </div>

        <div class="foundat">
            Defined in: <a href="../files/src_shapes_Cylinder.js.html#l8"><code>src&#x2F;shapes&#x2F;Cylinder.js:8</code></a>
        </div>


</div>


<div class="box intro">
    
</div>

    <div class="constructor">
        <h2>Constructor</h2>
        <div id="method_Cylinder" class="method item">
            <h3 class="name"><code>Cylinder</code></h3>
        
                <div class="args">
                    <span class="paren">(</span><ul class="args-list inline commas">
                        <li class="arg">
                                <code>radiusTop</code>
                        </li>
                        <li class="arg">
                                <code>radiusBottom</code>
                        </li>
                        <li class="arg">
                                <code>height</code>
                        </li>
                        <li class="arg">
                                <code>numSegments</code>
                        </li>
                    </ul><span class="paren">)</span>
                </div>
        
        
        
        
        
        
        
        
            <div class="meta">
                        <p>
                        Defined in
                <a href="../files/src_shapes_Cylinder.js.html#l8"><code>src&#x2F;shapes&#x2F;Cylinder.js:8</code></a>
                </p>
        
        
        
            </div>
        
            <div class="description">
                
            </div>
        
                <div class="params">
                    <h4>Parameters:</h4>
        
                    <ul class="params-list">
                        <li class="param">
                                <code class="param-name">radiusTop</code>
                                <span class="type">Number</span>
        
        
                            <div class="param-description">
                                 
                            </div>
        
                        </li>
                        <li class="param">
                                <code class="param-name">radiusBottom</code>
                                <span class="type">Number</span>
        
        
                            <div class="param-description">
                                 
                            </div>
        
                        </li>
                        <li class="param">
                                <code class="param-name">height</code>
                                <span class="type">Number</span>
        
        
                            <div class="param-description">
                                 
                            </div>
        
                        </li>
                        <li class="param">
                                <code class="param-name">numSegments</code>
                                <span class="type">Number</span>
        
        
                            <div class="param-description">
                                <p>The number of segments to build the cylinder out of</p>
        
                            </div>
        
                        </li>
                    </ul>
                </div>
        
        
        
        </div>
    </div>

<div id="classdocs" class="tabview">
    <ul class="api-class-tabs">
        <li class="api-class-tab index"><a href="#index">Index</a></li>

            <li class="api-class-tab methods"><a href="#methods">Methods</a></li>
            <li class="api-class-tab properties"><a href="#properties">Properties</a></li>
    </ul>

    <div>
        <div id="index" class="api-class-tabpanel index">
            <h2 class="off-left">Item Index</h2>

                <div class="index-section methods">
                    <h3>Methods</h3>

                    <ul class="index-list methods extends">
                            <li class="index-item method inherited inherited">
                                <a href="#method_calculateLocalInertia">calculateLocalInertia</a>

                            </li>
                            <li class="index-item method inherited">
                                <a href="#method_calculateWorldAABB">calculateWorldAABB</a>

                            </li>
                            <li class="index-item method inherited">
                                <a href="#method_clipAgainstHull">clipAgainstHull</a>

                            </li>
                            <li class="index-item method inherited">
                                <a href="#method_clipFaceAgainstHull">clipFaceAgainstHull</a>

                            </li>
                            <li class="index-item method inherited">
                                <a href="#method_clipFaceAgainstPlane">clipFaceAgainstPlane</a>

                            </li>
                            <li class="index-item method inherited">
                                <a href="#method_computeEdges">computeEdges</a>

                            </li>
                            <li class="index-item method inherited">
                                <a href="#method_computeNormals">computeNormals</a>

                            </li>
                            <li class="index-item method inherited">
                                <a href="#method_computeWorldFaceNormals">computeWorldFaceNormals</a>

                            </li>
                            <li class="index-item method inherited">
                                <a href="#method_findSeparatingAxis">findSeparatingAxis</a>

                            </li>
                            <li class="index-item method inherited">
                                <a href="#method_getAveragePointLocal">getAveragePointLocal</a>

                            </li>
                            <li class="index-item method inherited">
                                <a href="#method_getFaceNormal">getFaceNormal</a>

                            </li>
                            <li class="index-item method inherited">
                                <a href="#method_getPlaneConstantOfFace">getPlaneConstantOfFace</a>

                            </li>
                            <li class="index-item method inherited">
                                <a href="#method_pointIsInside">pointIsInside</a>

                            </li>
                            <li class="index-item method inherited">
                                <a href="#method_testSepAxis">testSepAxis</a>

                            </li>
                            <li class="index-item method inherited">
                                <a href="#method_transformAllPoints">transformAllPoints</a>

                            </li>
                            <li class="index-item method inherited inherited">
                                <a href="#method_updateBoundingSphereRadius">updateBoundingSphereRadius</a>

                            </li>
                            <li class="index-item method inherited inherited">
                                <a href="#method_volume">volume</a>

                            </li>
                    </ul>
                </div>

                <div class="index-section properties">
                    <h3>Properties</h3>

                    <ul class="index-list properties extends">
                            <li class="index-item property inherited">
                                <a href="#property_boundingSphereRadius">boundingSphereRadius</a>

                            </li>
                            <li class="index-item property inherited">
                                <a href="#property_collisionResponse">collisionResponse</a>

                            </li>
                            <li class="index-item property inherited">
                                <a href="#property_faceNormals">faceNormals</a>

                            </li>
                            <li class="index-item property inherited">
                                <a href="#property_faces">faces</a>

                            </li>
                            <li class="index-item property inherited">
                                <a href="#property_id">id</a>

                            </li>
                            <li class="index-item property inherited">
                                <a href="#property_material">material</a>

                            </li>
                            <li class="index-item property inherited">
                                <a href="#property_type">type</a>

                            </li>
                            <li class="index-item property inherited">
                                <a href="#property_uniqueAxes">uniqueAxes</a>

                            </li>
                            <li class="index-item property inherited">
                                <a href="#property_uniqueEdges">uniqueEdges</a>

                            </li>
                            <li class="index-item property inherited">
                                <a href="#property_vertices">vertices</a>

                            </li>
                    </ul>
                </div>


        </div>

            <div id="methods" class="api-class-tabpanel">
                <h2 class="off-left">Methods</h2>

<div id="method_calculateLocalInertia" class="method item inherited">
    <h3 class="name"><code>calculateLocalInertia</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>mass</code>
                </li>
                <li class="arg">
                        <code>target</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
            <p>Inherited from
            <a href="../classes/Shape.html#method_calculateLocalInertia">
                Shape
            </a>
            but overwritten in
        <a href="../files/src_shapes_ConvexPolyhedron.js.html#l413"><code>src&#x2F;shapes&#x2F;ConvexPolyhedron.js:413</code></a>
        </p>



    </div>

    <div class="description">
        
    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">mass</code>
                        <span class="type">Number</span>


                    <div class="param-description">
                        
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">target</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                        
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_calculateWorldAABB" class="method item inherited">
    <h3 class="name"><code>calculateWorldAABB</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>pos</code>
                </li>
                <li class="arg">
                        <code>quat</code>
                </li>
                <li class="arg">
                        <code>min</code>
                </li>
                <li class="arg">
                        <code>max</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>Inherited from
                <a href="../classes/ConvexPolyhedron.html#method_calculateWorldAABB">ConvexPolyhedron</a>:
        <a href="../files/src_shapes_ConvexPolyhedron.js.html#l727"><code>src&#x2F;shapes&#x2F;ConvexPolyhedron.js:727</code></a>
        </p>



    </div>

    <div class="description">
        
    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">pos</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                        
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">quat</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                        
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">min</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                        
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">max</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                        
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_clipAgainstHull" class="method item inherited">
    <h3 class="name"><code>clipAgainstHull</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>posA</code>
                </li>
                <li class="arg">
                        <code>quatA</code>
                </li>
                <li class="arg">
                        <code>hullB</code>
                </li>
                <li class="arg">
                        <code>posB</code>
                </li>
                <li class="arg">
                        <code>quatB</code>
                </li>
                <li class="arg">
                        <code>separatingNormal</code>
                </li>
                <li class="arg">
                        <code>minDist</code>
                </li>
                <li class="arg">
                        <code>maxDist</code>
                </li>
                <li class="arg">
                        <code>result</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>Inherited from
                <a href="../classes/ConvexPolyhedron.html#method_clipAgainstHull">ConvexPolyhedron</a>:
        <a href="../files/src_shapes_ConvexPolyhedron.js.html#l181"><code>src&#x2F;shapes&#x2F;ConvexPolyhedron.js:181</code></a>
        </p>



    </div>

    <div class="description">
        
    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">posA</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                        
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">quatA</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                        
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">hullB</code>
                        <span class="type"><a href="../classes/ConvexPolyhedron.html" class="crosslink">ConvexPolyhedron</a></span>


                    <div class="param-description">
                        
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">posB</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                        
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">quatB</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                        
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">separatingNormal</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                        
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">minDist</code>
                        <span class="type">Number</span>


                    <div class="param-description">
                        <p>Clamp distance</p>

                    </div>

                </li>
                <li class="param">
                        <code class="param-name">maxDist</code>
                        <span class="type">Number</span>


                    <div class="param-description">
                        
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">result</code>
                        <span class="type">Array</span>


                    <div class="param-description">
                        <p>The an array of contact point objects, see clipFaceAgainstHull</p>

                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_clipFaceAgainstHull" class="method item inherited">
    <h3 class="name"><code>clipFaceAgainstHull</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>separatingNormal</code>
                </li>
                <li class="arg">
                        <code>posA</code>
                </li>
                <li class="arg">
                        <code>quatA</code>
                </li>
                <li class="arg">
                        <code>worldVertsB1</code>
                </li>
                <li class="arg">
                        <code>minDist</code>
                </li>
                <li class="arg">
                        <code>maxDist</code>
                </li>
                <li class="arg">
                        <code>Array</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>Inherited from
                <a href="../classes/ConvexPolyhedron.html#method_clipFaceAgainstHull">ConvexPolyhedron</a>:
        <a href="../files/src_shapes_ConvexPolyhedron.js.html#l443"><code>src&#x2F;shapes&#x2F;ConvexPolyhedron.js:443</code></a>
        </p>



    </div>

    <div class="description">
        <p>Clip a face against a hull.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">separatingNormal</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                        
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">posA</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                        
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">quatA</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                        
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">worldVertsB1</code>
                        <span class="type">Array</span>


                    <div class="param-description">
                        <p>An array of Vec3 with vertices in the world frame.</p>

                    </div>

                </li>
                <li class="param">
                        <code class="param-name">minDist</code>
                        <span class="type">Number</span>


                    <div class="param-description">
                        <p>Distance clamping</p>

                    </div>

                </li>
                <li class="param">
                        <code class="param-name">maxDist</code>
                        <span class="type">Number</span>


                    <div class="param-description">
                        
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">Array</code>
                        <span class="type">Object</span>


                    <div class="param-description">
                        <p>result Array to store resulting contact points in. Will be objects with properties: point, depth, normal. These are represented in world coordinates.</p>

                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_clipFaceAgainstPlane" class="method item inherited">
    <h3 class="name"><code>clipFaceAgainstPlane</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>inVertices</code>
                </li>
                <li class="arg">
                        <code>outVertices</code>
                </li>
                <li class="arg">
                        <code>planeNormal</code>
                </li>
                <li class="arg">
                        <code>planeConstant</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>Inherited from
                <a href="../classes/ConvexPolyhedron.html#method_clipFaceAgainstPlane">ConvexPolyhedron</a>:
        <a href="../files/src_shapes_ConvexPolyhedron.js.html#l588"><code>src&#x2F;shapes&#x2F;ConvexPolyhedron.js:588</code></a>
        </p>



    </div>

    <div class="description">
        <p>Clip a face in a hull against the back of a plane.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">inVertices</code>
                        <span class="type">Array</span>


                    <div class="param-description">
                        
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">outVertices</code>
                        <span class="type">Array</span>


                    <div class="param-description">
                        
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">planeNormal</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                        
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">planeConstant</code>
                        <span class="type">Number</span>


                    <div class="param-description">
                        <p>The constant in the mathematical plane equation</p>

                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_computeEdges" class="method item inherited">
    <h3 class="name"><code>computeEdges</code></h3>

        <span class="paren">()</span>








    <div class="meta">
                <p>Inherited from
                <a href="../classes/ConvexPolyhedron.html#method_computeEdges">ConvexPolyhedron</a>:
        <a href="../files/src_shapes_ConvexPolyhedron.js.html#l80"><code>src&#x2F;shapes&#x2F;ConvexPolyhedron.js:80</code></a>
        </p>



    </div>

    <div class="description">
        <p>Computes uniqueEdges</p>

    </div>




</div>
<div id="method_computeNormals" class="method item inherited">
    <h3 class="name"><code>computeNormals</code></h3>

        <span class="paren">()</span>








    <div class="meta">
                <p>Inherited from
                <a href="../classes/ConvexPolyhedron.html#method_computeNormals">ConvexPolyhedron</a>:
        <a href="../files/src_shapes_ConvexPolyhedron.js.html#l116"><code>src&#x2F;shapes&#x2F;ConvexPolyhedron.js:116</code></a>
        </p>



    </div>

    <div class="description">
        <p>Compute the normals of the faces. Will reuse existing Vec3 objects in the .faceNormals array if they exist.</p>

    </div>




</div>
<div id="method_computeWorldFaceNormals" class="method item inherited">
    <h3 class="name"><code>computeWorldFaceNormals</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>quat</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>Inherited from
                <a href="../classes/ConvexPolyhedron.html#method_computeWorldFaceNormals">ConvexPolyhedron</a>:
        <a href="../files/src_shapes_ConvexPolyhedron.js.html#l689"><code>src&#x2F;shapes&#x2F;ConvexPolyhedron.js:689</code></a>
        </p>



    </div>

    <div class="description">
        <p>Updates .worldVertices and sets .worldVerticesNeedsUpdate to false.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">quat</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                        
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_findSeparatingAxis" class="method item inherited">
    <h3 class="name"><code>findSeparatingAxis</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>hullB</code>
                </li>
                <li class="arg">
                        <code>posA</code>
                </li>
                <li class="arg">
                        <code>quatA</code>
                </li>
                <li class="arg">
                        <code>posB</code>
                </li>
                <li class="arg">
                        <code>quatB</code>
                </li>
                <li class="arg">
                        <code>target</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type">Bool</span>
        </span>







    <div class="meta">
                <p>Inherited from
                <a href="../classes/ConvexPolyhedron.html#method_findSeparatingAxis">ConvexPolyhedron</a>:
        <a href="../files/src_shapes_ConvexPolyhedron.js.html#l234"><code>src&#x2F;shapes&#x2F;ConvexPolyhedron.js:234</code></a>
        </p>



    </div>

    <div class="description">
        <p>Find the separating axis between this hull and another</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">hullB</code>
                        <span class="type"><a href="../classes/ConvexPolyhedron.html" class="crosslink">ConvexPolyhedron</a></span>


                    <div class="param-description">
                        
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">posA</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                        
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">quatA</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                        
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">posB</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                        
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">quatB</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                        
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">target</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                        <p>The target vector to save the axis in</p>

                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type">Bool</span>:
                    <p>Returns false if a separation is found, else true</p>

            </div>
        </div>


</div>
<div id="method_getAveragePointLocal" class="method item inherited">
    <h3 class="name"><code>getAveragePointLocal</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>target</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
        </span>







    <div class="meta">
                <p>Inherited from
                <a href="../classes/ConvexPolyhedron.html#method_getAveragePointLocal">ConvexPolyhedron</a>:
        <a href="../files/src_shapes_ConvexPolyhedron.js.html#l773"><code>src&#x2F;shapes&#x2F;ConvexPolyhedron.js:773</code></a>
        </p>



    </div>

    <div class="description">
        <p>Get an average of all the vertices positions</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">target</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                        
                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>:
            </div>
        </div>


</div>
<div id="method_getFaceNormal" class="method item inherited">
    <h3 class="name"><code>getFaceNormal</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>i</code>
                </li>
                <li class="arg">
                        <code>target</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>Inherited from
                <a href="../classes/ConvexPolyhedron.html#method_getFaceNormal">ConvexPolyhedron</a>:
        <a href="../files/src_shapes_ConvexPolyhedron.js.html#l167"><code>src&#x2F;shapes&#x2F;ConvexPolyhedron.js:167</code></a>
        </p>



    </div>

    <div class="description">
        <p>Compute the normal of a face from its vertices</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">i</code>
                        <span class="type">Number</span>


                    <div class="param-description">
                        
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">target</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                        
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_getPlaneConstantOfFace" class="method item inherited">
    <h3 class="name"><code>getPlaneConstantOfFace</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>face_i</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type">Number</span>
        </span>







    <div class="meta">
                <p>Inherited from
                <a href="../classes/ConvexPolyhedron.html#method_getPlaneConstantOfFace">ConvexPolyhedron</a>:
        <a href="../files/src_shapes_ConvexPolyhedron.js.html#l430"><code>src&#x2F;shapes&#x2F;ConvexPolyhedron.js:430</code></a>
        </p>



    </div>

    <div class="description">
        
    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">face_i</code>
                        <span class="type">Number</span>


                    <div class="param-description">
                        <p>Index of the face</p>

                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type">Number</span>:
            </div>
        </div>


</div>
<div id="method_pointIsInside" class="method item inherited">
    <h3 class="name"><code>pointIsInside</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>p</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type">Boolean</span>
        </span>







    <div class="meta">
                <p>Inherited from
                <a href="../classes/ConvexPolyhedron.html#method_pointIsInside">ConvexPolyhedron</a>:
        <a href="../files/src_shapes_ConvexPolyhedron.js.html#l829"><code>src&#x2F;shapes&#x2F;ConvexPolyhedron.js:829</code></a>
        </p>



    </div>

    <div class="description">
        <p>Checks whether p is inside the polyhedra. Must be in local coords. The point lies outside of the convex hull of the other points if and only if the direction of all the vectors from it to those other points are on less than one half of a sphere around it.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">p</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                        <p>A point given in local coordinates</p>

                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type">Boolean</span>:
            </div>
        </div>


</div>
<div id="method_testSepAxis" class="method item inherited">
    <h3 class="name"><code>testSepAxis</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>axis</code>
                </li>
                <li class="arg">
                        <code>hullB</code>
                </li>
                <li class="arg">
                        <code>posA</code>
                </li>
                <li class="arg">
                        <code>quatA</code>
                </li>
                <li class="arg">
                        <code>posB</code>
                </li>
                <li class="arg">
                        <code>quatB</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type">Number</span>
        </span>







    <div class="meta">
                <p>Inherited from
                <a href="../classes/ConvexPolyhedron.html#method_testSepAxis">ConvexPolyhedron</a>:
        <a href="../files/src_shapes_ConvexPolyhedron.js.html#l382"><code>src&#x2F;shapes&#x2F;ConvexPolyhedron.js:382</code></a>
        </p>



    </div>

    <div class="description">
        <p>Test separating axis against two hulls. Both hulls are projected onto the axis and the overlap size is returned if there is one.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">axis</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                        
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">hullB</code>
                        <span class="type"><a href="../classes/ConvexPolyhedron.html" class="crosslink">ConvexPolyhedron</a></span>


                    <div class="param-description">
                        
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">posA</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                        
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">quatA</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                        
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">posB</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                        
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">quatB</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                        
                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type">Number</span>:
                    <p>The overlap depth, or FALSE if no penetration.</p>

            </div>
        </div>


</div>
<div id="method_transformAllPoints" class="method item inherited">
    <h3 class="name"><code>transformAllPoints</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>offset</code>
                </li>
                <li class="arg">
                        <code>quat</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>Inherited from
                <a href="../classes/ConvexPolyhedron.html#method_transformAllPoints">ConvexPolyhedron</a>:
        <a href="../files/src_shapes_ConvexPolyhedron.js.html#l790"><code>src&#x2F;shapes&#x2F;ConvexPolyhedron.js:790</code></a>
        </p>



    </div>

    <div class="description">
        <p>Transform all local points. Will change the .vertices</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">offset</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                        
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">quat</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                        
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_updateBoundingSphereRadius" class="method item inherited">
    <h3 class="name"><code>updateBoundingSphereRadius</code></h3>

        <span class="paren">()</span>








    <div class="meta">
            <p>Inherited from
            <a href="../classes/Shape.html#method_updateBoundingSphereRadius">
                Shape
            </a>
            but overwritten in
        <a href="../files/src_shapes_ConvexPolyhedron.js.html#l709"><code>src&#x2F;shapes&#x2F;ConvexPolyhedron.js:709</code></a>
        </p>



    </div>

    <div class="description">
        
    </div>




</div>
<div id="method_volume" class="method item inherited">
    <h3 class="name"><code>volume</code></h3>

        <span class="paren">()</span>

        <span class="returns-inline">
            <span class="type">Number</span>
        </span>







    <div class="meta">
            <p>Inherited from
            <a href="../classes/Shape.html#method_volume">
                Shape
            </a>
            but overwritten in
        <a href="../files/src_shapes_ConvexPolyhedron.js.html#l764"><code>src&#x2F;shapes&#x2F;ConvexPolyhedron.js:764</code></a>
        </p>



    </div>

    <div class="description">
        <p>Get approximate convex volume</p>

    </div>


        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type">Number</span>:
            </div>
        </div>


</div>
            </div>

            <div id="properties" class="api-class-tabpanel">
                <h2 class="off-left">Properties</h2>

                    <div id="property_boundingSphereRadius" class="property item inherited">
                        <h3 class="name"><code>boundingSphereRadius</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>Inherited from
                                    <a href="../classes/Shape.html#property_boundingSphereRadius">Shape</a>:
                            <a href="../files/src_shapes_Shape.js.html#l31"><code>src&#x2F;shapes&#x2F;Shape.js:31</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>The local bounding sphere radius of this shape.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_collisionResponse" class="property item inherited">
                        <h3 class="name"><code>collisionResponse</code></h3>
                        <span class="type">Boolean</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>Inherited from
                                    <a href="../classes/Shape.html#property_collisionResponse">Shape</a>:
                            <a href="../files/src_shapes_Shape.js.html#l37"><code>src&#x2F;shapes&#x2F;Shape.js:37</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Whether to produce contact forces when in contact with other bodies. Note that contacts will be generated, but they will be disabled.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_faceNormals" class="property item inherited">
                        <h3 class="name"><code>faceNormals</code></h3>
                        <span class="type">Array</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>Inherited from
                                    <a href="../classes/ConvexPolyhedron.html#property_faceNormals">ConvexPolyhedron</a>:
                            <a href="../files/src_shapes_ConvexPolyhedron.js.html#l49"><code>src&#x2F;shapes&#x2F;ConvexPolyhedron.js:49</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Array of Vec3</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_faces" class="property item inherited">
                        <h3 class="name"><code>faces</code></h3>
                        <span class="type">Array</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>Inherited from
                                    <a href="../classes/ConvexPolyhedron.html#property_faces">ConvexPolyhedron</a>:
                            <a href="../files/src_shapes_ConvexPolyhedron.js.html#l42"><code>src&#x2F;shapes&#x2F;ConvexPolyhedron.js:42</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Array of integer arrays, indicating which vertices each face consists of</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_id" class="property item inherited">
                        <h3 class="name"><code>id</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>Inherited from
                                    <a href="../classes/Shape.html#property_id">Shape</a>:
                            <a href="../files/src_shapes_Shape.js.html#l17"><code>src&#x2F;shapes&#x2F;Shape.js:17</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Identifyer of the Shape.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_material" class="property item inherited">
                        <h3 class="name"><code>material</code></h3>
                        <span class="type"><a href="../classes/Material.html" class="crosslink">Material</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>Inherited from
                                    <a href="../classes/Shape.html#property_material">Shape</a>:
                            <a href="../files/src_shapes_Shape.js.html#l43"><code>src&#x2F;shapes&#x2F;Shape.js:43</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_type" class="property item inherited">
                        <h3 class="name"><code>type</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>Inherited from
                                    <a href="../classes/Shape.html#property_type">Shape</a>:
                            <a href="../files/src_shapes_Shape.js.html#l23"><code>src&#x2F;shapes&#x2F;Shape.js:23</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>The type of this shape. Must be set to an int &gt; 0 by subclasses.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_uniqueAxes" class="property item inherited">
                        <h3 class="name"><code>uniqueAxes</code></h3>
                        <span class="type">Array</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>Inherited from
                                    <a href="../classes/ConvexPolyhedron.html#property_uniqueAxes">ConvexPolyhedron</a>:
                            <a href="../files/src_shapes_ConvexPolyhedron.js.html#l67"><code>src&#x2F;shapes&#x2F;ConvexPolyhedron.js:67</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>If given, these locally defined, normalized axes are the only ones being checked when doing separating axis check.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_uniqueEdges" class="property item inherited">
                        <h3 class="name"><code>uniqueEdges</code></h3>
                        <span class="type">Array</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>Inherited from
                                    <a href="../classes/ConvexPolyhedron.html#property_uniqueEdges">ConvexPolyhedron</a>:
                            <a href="../files/src_shapes_ConvexPolyhedron.js.html#l60"><code>src&#x2F;shapes&#x2F;ConvexPolyhedron.js:60</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Array of Vec3</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_vertices" class="property item inherited">
                        <h3 class="name"><code>vertices</code></h3>
                        <span class="type">Array</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>Inherited from
                                    <a href="../classes/ConvexPolyhedron.html#property_vertices">ConvexPolyhedron</a>:
                            <a href="../files/src_shapes_ConvexPolyhedron.js.html#l32"><code>src&#x2F;shapes&#x2F;ConvexPolyhedron.js:32</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Array of Vec3</p>
                    
                        </div>
                    
                    
                    
                    </div>
            </div>


    </div>
</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="../assets/vendor/prettify/prettify-min.js"></script>
<script>prettyPrint();</script>
<script src="../assets/js/yui-prettify.js"></script>
<script src="../assets/../api.js"></script>
<script src="../assets/js/api-filter.js"></script>
<script src="../assets/js/api-list.js"></script>
<script src="../assets/js/api-search.js"></script>
<script src="../assets/js/apidocs.js"></script>
</body>
</html>
