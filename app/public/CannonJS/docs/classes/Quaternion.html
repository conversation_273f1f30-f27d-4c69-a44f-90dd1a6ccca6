<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Quaternion - cannon</title>
    <link rel="stylesheet" href="http://yui.yahooapis.com/3.9.1/build/cssgrids/cssgrids-min.css">
    <link rel="stylesheet" href="../assets/vendor/prettify/prettify-min.css">
    <link rel="stylesheet" href="../assets/css/main.css" id="site_styles">
    <link rel="icon" href="../assets/favicon.ico">
    <script src="http://yui.yahooapis.com/combo?3.9.1/build/yui/yui-min.js"></script>
</head>
<body class="yui3-skin-sam">

<div id="doc">
    <div id="hd" class="yui3-g header">
        <div class="yui3-u-3-4">
                <h1><img src="../assets/css/logo.png" title="cannon" width="117" height="52"></h1>
        </div>
        <div class="yui3-u-1-4 version">
            <em>API Docs for: 0.6.1</em>
        </div>
    </div>
    <div id="bd" class="yui3-g">

        <div class="yui3-u-1-4">
            <div id="docs-sidebar" class="sidebar apidocs">
                <div id="api-list">
                    <h2 class="off-left">APIs</h2>
                    <div id="api-tabview" class="tabview">
                        <ul class="tabs">
                            <li><a href="#api-classes">Classes</a></li>
                            <li><a href="#api-modules">Modules</a></li>
                        </ul>
                
                        <div id="api-tabview-filter">
                            <input type="search" id="api-filter" placeholder="Type to filter APIs">
                        </div>
                
                        <div id="api-tabview-panel">
                            <ul id="api-classes" class="apis classes">
                                <li><a href="../classes/AABB.html">AABB</a></li>
                                <li><a href="../classes/ArrayCollisionMatrix.html">ArrayCollisionMatrix</a></li>
                                <li><a href="../classes/Body.html">Body</a></li>
                                <li><a href="../classes/Box.html">Box</a></li>
                                <li><a href="../classes/Broadphase.html">Broadphase</a></li>
                                <li><a href="../classes/ConeEquation.html">ConeEquation</a></li>
                                <li><a href="../classes/ConeTwistConstraint.html">ConeTwistConstraint</a></li>
                                <li><a href="../classes/Constraint.html">Constraint</a></li>
                                <li><a href="../classes/ContactEquation.html">ContactEquation</a></li>
                                <li><a href="../classes/ContactMaterial.html">ContactMaterial</a></li>
                                <li><a href="../classes/ConvexPolyhedron.html">ConvexPolyhedron</a></li>
                                <li><a href="../classes/Cylinder.html">Cylinder</a></li>
                                <li><a href="../classes/Demo.html">Demo</a></li>
                                <li><a href="../classes/DistanceConstraint.html">DistanceConstraint</a></li>
                                <li><a href="../classes/Equation.html">Equation</a></li>
                                <li><a href="../classes/EventTarget.html">EventTarget</a></li>
                                <li><a href="../classes/FrictionEquation.html">FrictionEquation</a></li>
                                <li><a href="../classes/GridBroadphase.html">GridBroadphase</a></li>
                                <li><a href="../classes/GSSolver.html">GSSolver</a></li>
                                <li><a href="../classes/Heightfield.html">Heightfield</a></li>
                                <li><a href="../classes/HingeConstraint.html">HingeConstraint</a></li>
                                <li><a href="../classes/JacobianElement.html">JacobianElement</a></li>
                                <li><a href="../classes/LockConstraint.html">LockConstraint</a></li>
                                <li><a href="../classes/Mat3.html">Mat3</a></li>
                                <li><a href="../classes/Material.html">Material</a></li>
                                <li><a href="../classes/NaiveBroadphase.html">NaiveBroadphase</a></li>
                                <li><a href="../classes/Narrowphase.html">Narrowphase</a></li>
                                <li><a href="../classes/ObjectCollisionMatrix.html">ObjectCollisionMatrix</a></li>
                                <li><a href="../classes/Octree.html">Octree</a></li>
                                <li><a href="../classes/OctreeNode.html">OctreeNode</a></li>
                                <li><a href="../classes/Particle.html">Particle</a></li>
                                <li><a href="../classes/Plane.html">Plane</a></li>
                                <li><a href="../classes/PointToPointConstraint.html">PointToPointConstraint</a></li>
                                <li><a href="../classes/Pool.html">Pool</a></li>
                                <li><a href="../classes/Quaternion.html">Quaternion</a></li>
                                <li><a href="../classes/Ray.html">Ray</a></li>
                                <li><a href="../classes/RaycastResult.html">RaycastResult</a></li>
                                <li><a href="../classes/RaycastVehicle.html">RaycastVehicle</a></li>
                                <li><a href="../classes/RigidVehicle.html">RigidVehicle</a></li>
                                <li><a href="../classes/RotationalEquation.html">RotationalEquation</a></li>
                                <li><a href="../classes/RotationalMotorEquation.html">RotationalMotorEquation</a></li>
                                <li><a href="../classes/SAPBroadphase.html">SAPBroadphase</a></li>
                                <li><a href="../classes/Shape.html">Shape</a></li>
                                <li><a href="../classes/Solver.html">Solver</a></li>
                                <li><a href="../classes/Sphere.html">Sphere</a></li>
                                <li><a href="../classes/SPHSystem.html">SPHSystem</a></li>
                                <li><a href="../classes/SplitSolver.html">SplitSolver</a></li>
                                <li><a href="../classes/Spring.html">Spring</a></li>
                                <li><a href="../classes/Transform.html">Transform</a></li>
                                <li><a href="../classes/Trimesh.html">Trimesh</a></li>
                                <li><a href="../classes/TupleDictionary.html">TupleDictionary</a></li>
                                <li><a href="../classes/Vec3.html">Vec3</a></li>
                                <li><a href="../classes/Vec3Pool.html">Vec3Pool</a></li>
                                <li><a href="../classes/WheelInfo.html">WheelInfo</a></li>
                                <li><a href="../classes/World.html">World</a></li>
                            </ul>
                
                            <ul id="api-modules" class="apis modules">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="yui3-u-3-4">
                <div id="api-options">
                    Show:
                    <label for="api-show-inherited">
                        <input type="checkbox" id="api-show-inherited" checked>
                        Inherited
                    </label>
            
                    <label for="api-show-protected">
                        <input type="checkbox" id="api-show-protected">
                        Protected
                    </label>
            
                    <label for="api-show-private">
                        <input type="checkbox" id="api-show-private">
                        Private
                    </label>
                    <label for="api-show-deprecated">
                        <input type="checkbox" id="api-show-deprecated">
                        Deprecated
                    </label>
            
                </div>
            
            <div class="apidocs">
                <div id="docs-main">
                    <div class="content">
<h1>Quaternion Class</h1>
<div class="box meta">


        <div class="foundat">
            Defined in: <a href="../files/src_math_Quaternion.js.html#l5"><code>src&#x2F;math&#x2F;Quaternion.js:5</code></a>
        </div>


</div>


<div class="box intro">
    <p>A Quaternion describes a rotation in 3D space. The Quaternion is mathematically defined as Q = x<em>i + y</em>j + z*k + w, where (i,j,k) are imaginary basis vectors. (x,y,z) can be seen as a vector related to the axis of rotation, while the real multiplier, w, is related to the amount of rotation.</p>

</div>

    <div class="constructor">
        <h2>Constructor</h2>
        <div id="method_Quaternion" class="method item">
            <h3 class="name"><code>Quaternion</code></h3>
        
                <div class="args">
                    <span class="paren">(</span><ul class="args-list inline commas">
                        <li class="arg">
                                <code>x</code>
                        </li>
                        <li class="arg">
                                <code>y</code>
                        </li>
                        <li class="arg">
                                <code>z</code>
                        </li>
                        <li class="arg">
                                <code>w</code>
                        </li>
                    </ul><span class="paren">)</span>
                </div>
        
        
        
        
        
        
        
        
            <div class="meta">
                        <p>
                        Defined in
                <a href="../files/src_math_Quaternion.js.html#l5"><code>src&#x2F;math&#x2F;Quaternion.js:5</code></a>
                </p>
        
        
        
            </div>
        
            <div class="description">
                
            </div>
        
                <div class="params">
                    <h4>Parameters:</h4>
        
                    <ul class="params-list">
                        <li class="param">
                                <code class="param-name">x</code>
                                <span class="type">Number</span>
        
        
                            <div class="param-description">
                                <p>Multiplier of the imaginary basis vector i.</p>
        
                            </div>
        
                        </li>
                        <li class="param">
                                <code class="param-name">y</code>
                                <span class="type">Number</span>
        
        
                            <div class="param-description">
                                <p>Multiplier of the imaginary basis vector j.</p>
        
                            </div>
        
                        </li>
                        <li class="param">
                                <code class="param-name">z</code>
                                <span class="type">Number</span>
        
        
                            <div class="param-description">
                                <p>Multiplier of the imaginary basis vector k.</p>
        
                            </div>
        
                        </li>
                        <li class="param">
                                <code class="param-name">w</code>
                                <span class="type">Number</span>
        
        
                            <div class="param-description">
                                <p>Multiplier of the real part.</p>
        
                            </div>
        
                        </li>
                    </ul>
                </div>
        
        
        
        </div>
    </div>

<div id="classdocs" class="tabview">
    <ul class="api-class-tabs">
        <li class="api-class-tab index"><a href="#index">Index</a></li>

            <li class="api-class-tab methods"><a href="#methods">Methods</a></li>
            <li class="api-class-tab properties"><a href="#properties">Properties</a></li>
    </ul>

    <div>
        <div id="index" class="api-class-tabpanel index">
            <h2 class="off-left">Item Index</h2>

                <div class="index-section methods">
                    <h3>Methods</h3>

                    <ul class="index-list methods">
                            <li class="index-item method">
                                <a href="#method_conjugate">conjugate</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_copy">copy</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_inverse">inverse</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_mult">mult</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_normalize">normalize</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_normalizeFast">normalizeFast</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_set">set</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_setFromAxisAngle">setFromAxisAngle</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_setFromEuler">setFromEuler</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_setFromVectors">setFromVectors</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_toArray">toArray</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_toAxisAngle">toAxisAngle</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_toEuler">toEuler</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_toString">toString</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_vmult">vmult</a>

                            </li>
                    </ul>
                </div>

                <div class="index-section properties">
                    <h3>Properties</h3>

                    <ul class="index-list properties">
                            <li class="index-item property">
                                <a href="#property_w">w</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_x">x</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_y">y</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_z">z</a>

                            </li>
                    </ul>
                </div>


        </div>

            <div id="methods" class="api-class-tabpanel">
                <h2 class="off-left">Methods</h2>

<div id="method_conjugate" class="method item">
    <h3 class="name"><code>conjugate</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>target</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Quaternion.js.html#l184"><code>src&#x2F;math&#x2F;Quaternion.js:184</code></a>
        </p>



    </div>

    <div class="description">
        <p>Get the quaternion conjugate</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">target</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>:
            </div>
        </div>


</div>
<div id="method_copy" class="method item">
    <h3 class="name"><code>copy</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>source</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Quaternion.js.html#l274"><code>src&#x2F;math&#x2F;Quaternion.js:274</code></a>
        </p>



    </div>

    <div class="description">
        <p>Copies value of source to this quaternion.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">source</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>:
                    <p>this</p>

            </div>
        </div>


</div>
<div id="method_inverse" class="method item">
    <h3 class="name"><code>inverse</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>target</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Quaternion.js.html#l164"><code>src&#x2F;math&#x2F;Quaternion.js:164</code></a>
        </p>



    </div>

    <div class="description">
        <p>Get the inverse quaternion rotation.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">target</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>:
            </div>
        </div>


</div>
<div id="method_mult" class="method item">
    <h3 class="name"><code>mult</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>q</code>
                </li>
                <li class="arg">
                        <code>target</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Quaternion.js.html#l135"><code>src&#x2F;math&#x2F;Quaternion.js:135</code></a>
        </p>



    </div>

    <div class="description">
        <p>Quaternion multiplication</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">q</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">target</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                        <p>Optional.</p>

                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>:
            </div>
        </div>


</div>
<div id="method_normalize" class="method item">
    <h3 class="name"><code>normalize</code></h3>

        <span class="paren">()</span>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Quaternion.js.html#l201"><code>src&#x2F;math&#x2F;Quaternion.js:201</code></a>
        </p>



    </div>

    <div class="description">
        <p>Normalize the quaternion. Note that this changes the values of the quaternion.</p>

    </div>




</div>
<div id="method_normalizeFast" class="method item">
    <h3 class="name"><code>normalizeFast</code></h3>

        <span class="paren">()</span>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Quaternion.js.html#l221"><code>src&#x2F;math&#x2F;Quaternion.js:221</code></a>
        </p>



    </div>

    <div class="description">
        <p>Approximation of quaternion normalization. Works best when quat is already almost-normalized.</p>

    </div>




</div>
<div id="method_set" class="method item">
    <h3 class="name"><code>set</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>x</code>
                </li>
                <li class="arg">
                        <code>y</code>
                </li>
                <li class="arg">
                        <code>z</code>
                </li>
                <li class="arg">
                        <code>w</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Quaternion.js.html#l38"><code>src&#x2F;math&#x2F;Quaternion.js:38</code></a>
        </p>



    </div>

    <div class="description">
        <p>Set the value of the quaternion.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">x</code>
                        <span class="type">Number</span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">y</code>
                        <span class="type">Number</span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">z</code>
                        <span class="type">Number</span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">w</code>
                        <span class="type">Number</span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_setFromAxisAngle" class="method item">
    <h3 class="name"><code>setFromAxisAngle</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>axis</code>
                </li>
                <li class="arg">
                        <code>angle</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Quaternion.js.html#l71"><code>src&#x2F;math&#x2F;Quaternion.js:71</code></a>
        </p>



    </div>

    <div class="description">
        <p>Set the quaternion components given an axis and an angle.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">axis</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">angle</code>
                        <span class="type">Number</span>


                    <div class="param-description">
                        <p>in radians</p>

                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_setFromEuler" class="method item">
    <h3 class="name"><code>setFromEuler</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>x</code>
                </li>
                <li class="arg">
                        <code>y</code>
                </li>
                <li class="arg">
                        <code>z</code>
                </li>
                <li class="arg">
                        <code>order</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Quaternion.js.html#l331"><code>src&#x2F;math&#x2F;Quaternion.js:331</code></a>
        </p>



    </div>

    <div class="description">
        <p>See <a href="http://www.mathworks.com/matlabcentral/fileexchange/20696-function-to-convert-between-dcm-euler-angles-quaternions-and-euler-vectors/content/SpinCalc.m">http://www.mathworks.com/matlabcentral/fileexchange/20696-function-to-convert-between-dcm-euler-angles-quaternions-and-euler-vectors/content/SpinCalc.m</a></p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">x</code>
                        <span class="type">Number</span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">y</code>
                        <span class="type">Number</span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">z</code>
                        <span class="type">Number</span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">order</code>
                        <span class="type">String</span>


                    <div class="param-description">
                        <p>The order to apply angles: &#39;XYZ&#39; or &#39;YXZ&#39; or any other combination</p>

                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_setFromVectors" class="method item">
    <h3 class="name"><code>setFromVectors</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>u</code>
                </li>
                <li class="arg">
                        <code>v</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Quaternion.js.html#l112"><code>src&#x2F;math&#x2F;Quaternion.js:112</code></a>
        </p>



    </div>

    <div class="description">
        <p>Set the quaternion value given two vectors. The resulting rotation will be the needed rotation to rotate u to v.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">u</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">v</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_toArray" class="method item">
    <h3 class="name"><code>toArray</code></h3>

        <span class="paren">()</span>

        <span class="returns-inline">
            <span class="type"></span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Quaternion.js.html#l62"><code>src&#x2F;math&#x2F;Quaternion.js:62</code></a>
        </p>



    </div>

    <div class="description">
        <p>Convert to an Array</p>

    </div>


        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                    <p>Array</p>

            </div>
        </div>


</div>
<div id="method_toAxisAngle" class="method item">
    <h3 class="name"><code>toAxisAngle</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>targetAxis</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type"></span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Quaternion.js.html#l85"><code>src&#x2F;math&#x2F;Quaternion.js:85</code></a>
        </p>



    </div>

    <div class="description">
        <p>Converts the quaternion to axis/angle representation.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">targetAxis</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                        <p>Optional. A vector object to reuse for storing the axis.</p>

                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                    <p>Array An array, first elemnt is the axis and the second is the angle in radians.</p>

            </div>
        </div>


</div>
<div id="method_toEuler" class="method item">
    <h3 class="name"><code>toEuler</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>target</code>
                </li>
                <li class="arg">
                        <code>string</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Quaternion.js.html#l288"><code>src&#x2F;math&#x2F;Quaternion.js:288</code></a>
        </p>



    </div>

    <div class="description">
        <p>Convert the quaternion to euler angle representation. Order: YZX, as this page describes: <a href="http://www.euclideanspace.com/maths/standards/index.htm">http://www.euclideanspace.com/maths/standards/index.htm</a></p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">target</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">string</code>
                        <span class="type">Object</span>


                    <div class="param-description">
                        <p>order Three-character string e.g. &quot;YZX&quot;, which also is default.</p>

                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_toString" class="method item">
    <h3 class="name"><code>toString</code></h3>

        <span class="paren">()</span>

        <span class="returns-inline">
            <span class="type"></span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Quaternion.js.html#l53"><code>src&#x2F;math&#x2F;Quaternion.js:53</code></a>
        </p>



    </div>

    <div class="description">
        <p>Convert to a readable format</p>

    </div>


        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                    <p>string</p>

            </div>
        </div>


</div>
<div id="method_vmult" class="method item">
    <h3 class="name"><code>vmult</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>v</code>
                </li>
                <li class="arg">
                        <code>target</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Quaternion.js.html#l242"><code>src&#x2F;math&#x2F;Quaternion.js:242</code></a>
        </p>



    </div>

    <div class="description">
        <p>Multiply the quaternion by a vector</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">v</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">target</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                        <p>Optional</p>

                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>:
            </div>
        </div>


</div>
            </div>

            <div id="properties" class="api-class-tabpanel">
                <h2 class="off-left">Properties</h2>

                    <div id="property_w" class="property item">
                        <h3 class="name"><code>w</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_math_Quaternion.js.html#l31"><code>src&#x2F;math&#x2F;Quaternion.js:31</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>The multiplier of the real quaternion basis vector.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_x" class="property item">
                        <h3 class="name"><code>x</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_math_Quaternion.js.html#l16"><code>src&#x2F;math&#x2F;Quaternion.js:16</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_y" class="property item">
                        <h3 class="name"><code>y</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_math_Quaternion.js.html#l21"><code>src&#x2F;math&#x2F;Quaternion.js:21</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_z" class="property item">
                        <h3 class="name"><code>z</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_math_Quaternion.js.html#l26"><code>src&#x2F;math&#x2F;Quaternion.js:26</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
            </div>


    </div>
</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="../assets/vendor/prettify/prettify-min.js"></script>
<script>prettyPrint();</script>
<script src="../assets/js/yui-prettify.js"></script>
<script src="../assets/../api.js"></script>
<script src="../assets/js/api-filter.js"></script>
<script src="../assets/js/api-list.js"></script>
<script src="../assets/js/api-search.js"></script>
<script src="../assets/js/apidocs.js"></script>
</body>
</html>
