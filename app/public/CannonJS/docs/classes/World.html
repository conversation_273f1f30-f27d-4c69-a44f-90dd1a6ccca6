<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>World - cannon</title>
    <link rel="stylesheet" href="http://yui.yahooapis.com/3.9.1/build/cssgrids/cssgrids-min.css">
    <link rel="stylesheet" href="../assets/vendor/prettify/prettify-min.css">
    <link rel="stylesheet" href="../assets/css/main.css" id="site_styles">
    <link rel="icon" href="../assets/favicon.ico">
    <script src="http://yui.yahooapis.com/combo?3.9.1/build/yui/yui-min.js"></script>
</head>
<body class="yui3-skin-sam">

<div id="doc">
    <div id="hd" class="yui3-g header">
        <div class="yui3-u-3-4">
                <h1><img src="../assets/css/logo.png" title="cannon" width="117" height="52"></h1>
        </div>
        <div class="yui3-u-1-4 version">
            <em>API Docs for: 0.6.1</em>
        </div>
    </div>
    <div id="bd" class="yui3-g">

        <div class="yui3-u-1-4">
            <div id="docs-sidebar" class="sidebar apidocs">
                <div id="api-list">
                    <h2 class="off-left">APIs</h2>
                    <div id="api-tabview" class="tabview">
                        <ul class="tabs">
                            <li><a href="#api-classes">Classes</a></li>
                            <li><a href="#api-modules">Modules</a></li>
                        </ul>
                
                        <div id="api-tabview-filter">
                            <input type="search" id="api-filter" placeholder="Type to filter APIs">
                        </div>
                
                        <div id="api-tabview-panel">
                            <ul id="api-classes" class="apis classes">
                                <li><a href="../classes/AABB.html">AABB</a></li>
                                <li><a href="../classes/ArrayCollisionMatrix.html">ArrayCollisionMatrix</a></li>
                                <li><a href="../classes/Body.html">Body</a></li>
                                <li><a href="../classes/Box.html">Box</a></li>
                                <li><a href="../classes/Broadphase.html">Broadphase</a></li>
                                <li><a href="../classes/ConeEquation.html">ConeEquation</a></li>
                                <li><a href="../classes/ConeTwistConstraint.html">ConeTwistConstraint</a></li>
                                <li><a href="../classes/Constraint.html">Constraint</a></li>
                                <li><a href="../classes/ContactEquation.html">ContactEquation</a></li>
                                <li><a href="../classes/ContactMaterial.html">ContactMaterial</a></li>
                                <li><a href="../classes/ConvexPolyhedron.html">ConvexPolyhedron</a></li>
                                <li><a href="../classes/Cylinder.html">Cylinder</a></li>
                                <li><a href="../classes/Demo.html">Demo</a></li>
                                <li><a href="../classes/DistanceConstraint.html">DistanceConstraint</a></li>
                                <li><a href="../classes/Equation.html">Equation</a></li>
                                <li><a href="../classes/EventTarget.html">EventTarget</a></li>
                                <li><a href="../classes/FrictionEquation.html">FrictionEquation</a></li>
                                <li><a href="../classes/GridBroadphase.html">GridBroadphase</a></li>
                                <li><a href="../classes/GSSolver.html">GSSolver</a></li>
                                <li><a href="../classes/Heightfield.html">Heightfield</a></li>
                                <li><a href="../classes/HingeConstraint.html">HingeConstraint</a></li>
                                <li><a href="../classes/JacobianElement.html">JacobianElement</a></li>
                                <li><a href="../classes/LockConstraint.html">LockConstraint</a></li>
                                <li><a href="../classes/Mat3.html">Mat3</a></li>
                                <li><a href="../classes/Material.html">Material</a></li>
                                <li><a href="../classes/NaiveBroadphase.html">NaiveBroadphase</a></li>
                                <li><a href="../classes/Narrowphase.html">Narrowphase</a></li>
                                <li><a href="../classes/ObjectCollisionMatrix.html">ObjectCollisionMatrix</a></li>
                                <li><a href="../classes/Octree.html">Octree</a></li>
                                <li><a href="../classes/OctreeNode.html">OctreeNode</a></li>
                                <li><a href="../classes/Particle.html">Particle</a></li>
                                <li><a href="../classes/Plane.html">Plane</a></li>
                                <li><a href="../classes/PointToPointConstraint.html">PointToPointConstraint</a></li>
                                <li><a href="../classes/Pool.html">Pool</a></li>
                                <li><a href="../classes/Quaternion.html">Quaternion</a></li>
                                <li><a href="../classes/Ray.html">Ray</a></li>
                                <li><a href="../classes/RaycastResult.html">RaycastResult</a></li>
                                <li><a href="../classes/RaycastVehicle.html">RaycastVehicle</a></li>
                                <li><a href="../classes/RigidVehicle.html">RigidVehicle</a></li>
                                <li><a href="../classes/RotationalEquation.html">RotationalEquation</a></li>
                                <li><a href="../classes/RotationalMotorEquation.html">RotationalMotorEquation</a></li>
                                <li><a href="../classes/SAPBroadphase.html">SAPBroadphase</a></li>
                                <li><a href="../classes/Shape.html">Shape</a></li>
                                <li><a href="../classes/Solver.html">Solver</a></li>
                                <li><a href="../classes/Sphere.html">Sphere</a></li>
                                <li><a href="../classes/SPHSystem.html">SPHSystem</a></li>
                                <li><a href="../classes/SplitSolver.html">SplitSolver</a></li>
                                <li><a href="../classes/Spring.html">Spring</a></li>
                                <li><a href="../classes/Transform.html">Transform</a></li>
                                <li><a href="../classes/Trimesh.html">Trimesh</a></li>
                                <li><a href="../classes/TupleDictionary.html">TupleDictionary</a></li>
                                <li><a href="../classes/Vec3.html">Vec3</a></li>
                                <li><a href="../classes/Vec3Pool.html">Vec3Pool</a></li>
                                <li><a href="../classes/WheelInfo.html">WheelInfo</a></li>
                                <li><a href="../classes/World.html">World</a></li>
                            </ul>
                
                            <ul id="api-modules" class="apis modules">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="yui3-u-3-4">
                <div id="api-options">
                    Show:
                    <label for="api-show-inherited">
                        <input type="checkbox" id="api-show-inherited" checked>
                        Inherited
                    </label>
            
                    <label for="api-show-protected">
                        <input type="checkbox" id="api-show-protected">
                        Protected
                    </label>
            
                    <label for="api-show-private">
                        <input type="checkbox" id="api-show-private">
                        Private
                    </label>
                    <label for="api-show-deprecated">
                        <input type="checkbox" id="api-show-deprecated">
                        Deprecated
                    </label>
            
                </div>
            
            <div class="apidocs">
                <div id="docs-main">
                    <div class="content">
<h1>World Class</h1>
<div class="box meta">

        <div class="extends">
            Extends <a href="../classes/EventTarget.html" class="crosslink">EventTarget</a>
        </div>

        <div class="foundat">
            Defined in: <a href="../files/src_world_World.js.html#l24"><code>src&#x2F;world&#x2F;World.js:24</code></a>
        </div>


</div>


<div class="box intro">
    <p>The physics world</p>

</div>

    <div class="constructor">
        <h2>Constructor</h2>
        <div id="method_World" class="method item">
            <h3 class="name"><code>World</code></h3>
        
                <span class="paren">()</span>
        
        
        
        
        
        
        
        
            <div class="meta">
                        <p>
                        Defined in
                <a href="../files/src_world_World.js.html#l24"><code>src&#x2F;world&#x2F;World.js:24</code></a>
                </p>
        
        
        
            </div>
        
            <div class="description">
                
            </div>
        
        
        
        
        </div>
    </div>

<div id="classdocs" class="tabview">
    <ul class="api-class-tabs">
        <li class="api-class-tab index"><a href="#index">Index</a></li>

            <li class="api-class-tab methods"><a href="#methods">Methods</a></li>
            <li class="api-class-tab properties"><a href="#properties">Properties</a></li>
    </ul>

    <div>
        <div id="index" class="api-class-tabpanel index">
            <h2 class="off-left">Item Index</h2>

                <div class="index-section methods">
                    <h3>Methods</h3>

                    <ul class="index-list methods extends">
                            <li class="index-item method deprecated">
                                <a href="#method_add">add</a>

                                    <span class="flag deprecated">deprecated</span>
                            </li>
                            <li class="index-item method">
                                <a href="#method_addConstraint">addConstraint</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_addContactMaterial">addContactMaterial</a>

                            </li>
                            <li class="index-item method inherited">
                                <a href="#method_addEventListener">addEventListener</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_addMaterial">addMaterial</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_clearForces">clearForces</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_collisionMatrixTick">collisionMatrixTick</a>

                            </li>
                            <li class="index-item method inherited">
                                <a href="#method_dispatchEvent">dispatchEvent</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_getContactMaterial">getContactMaterial</a>

                            </li>
                            <li class="index-item method inherited">
                                <a href="#method_hasEventListener">hasEventListener</a>

                            </li>
                            <li class="index-item method deprecated">
                                <a href="#method_numObjects">numObjects</a>

                                    <span class="flag deprecated">deprecated</span>
                            </li>
                            <li class="index-item method">
                                <a href="#method_raycastAll">raycastAll</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_raycastAny">raycastAny</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_raycastClosest">raycastClosest</a>

                            </li>
                            <li class="index-item method deprecated">
                                <a href="#method_rayTest">rayTest</a>

                                    <span class="flag deprecated">deprecated</span>
                            </li>
                            <li class="index-item method deprecated">
                                <a href="#method_remove">remove</a>

                                    <span class="flag deprecated">deprecated</span>
                            </li>
                            <li class="index-item method">
                                <a href="#method_removeBody">removeBody</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_removeConstraint">removeConstraint</a>

                            </li>
                            <li class="index-item method inherited">
                                <a href="#method_removeEventListener">removeEventListener</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_step">step</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_step">step</a>

                            </li>
                    </ul>
                </div>

                <div class="index-section properties">
                    <h3>Properties</h3>

                    <ul class="index-list properties extends">
                            <li class="index-item property">
                                <a href="#property_allowSleep">allowSleep</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_bodies">bodies</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_broadphase">broadphase</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_collisionMatrix">collisionMatrix</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_collisionMatrixPrevious">collisionMatrixPrevious</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_constraints">constraints</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_contactmaterials">contactmaterials</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_contactMaterialTable">contactMaterialTable</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_contacts">contacts</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_defaultContactMaterial">defaultContactMaterial</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_doProfiling">doProfiling</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_dt">dt</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_gravity">gravity</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_materials">materials</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_narrowphase">narrowphase</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_profile">profile</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_quatNormalizeFast">quatNormalizeFast</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_quatNormalizeSkip">quatNormalizeSkip</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_solver">solver</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_stepnumber">stepnumber</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_subsystems">subsystems</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_time">time</a>

                            </li>
                    </ul>
                </div>


        </div>

            <div id="methods" class="api-class-tabpanel">
                <h2 class="off-left">Methods</h2>

<div id="method_add" class="method item deprecated">
    <h3 class="name"><code>add</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>body</code>
                </li>
            </ul><span class="paren">)</span>
        </div>


        <span class="flag deprecated" title="Use .addBody instead">deprecated</span>






    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_world_World.js.html#l238"><code>src&#x2F;world&#x2F;World.js:238</code></a>
        </p>


            <p>Deprecated: Use .addBody instead</p>

    </div>

    <div class="description">
        <p>Add a rigid body to the simulation.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">body</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_addConstraint" class="method item">
    <h3 class="name"><code>addConstraint</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>c</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_world_World.js.html#l265"><code>src&#x2F;world&#x2F;World.js:265</code></a>
        </p>



    </div>

    <div class="description">
        <p>Add a constraint to the simulation.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">c</code>
                        <span class="type"><a href="../classes/Constraint.html" class="crosslink">Constraint</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_addContactMaterial" class="method item">
    <h3 class="name"><code>addContactMaterial</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>cmat</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_world_World.js.html#l413"><code>src&#x2F;world&#x2F;World.js:413</code></a>
        </p>



    </div>

    <div class="description">
        <p>Adds a contact material to the World</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">cmat</code>
                        <span class="type"><a href="../classes/ContactMaterial.html" class="crosslink">ContactMaterial</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_addEventListener" class="method item inherited">
    <h3 class="name"><code>addEventListener</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>type</code>
                </li>
                <li class="arg">
                        <code>listener</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type"><a href="../classes/EventTarget.html" class="crosslink">EventTarget</a></span>
        </span>







    <div class="meta">
                <p>Inherited from
                <a href="../classes/EventTarget.html#method_addEventListener">EventTarget</a>:
        <a href="../files/src_utils_EventTarget.js.html#l15"><code>src&#x2F;utils&#x2F;EventTarget.js:15</code></a>
        </p>



    </div>

    <div class="description">
        <p>Add an event listener</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">type</code>
                        <span class="type">String</span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">listener</code>
                        <span class="type">Function</span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type"><a href="../classes/EventTarget.html" class="crosslink">EventTarget</a></span>:
                    <p>The self object, for chainability.</p>

            </div>
        </div>


</div>
<div id="method_addMaterial" class="method item">
    <h3 class="name"><code>addMaterial</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>m</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_world_World.js.html#l403"><code>src&#x2F;world&#x2F;World.js:403</code></a>
        </p>



    </div>

    <div class="description">
        <p>Adds a material to the World.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">m</code>
                        <span class="type"><a href="../classes/Material.html" class="crosslink">Material</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_clearForces" class="method item">
    <h3 class="name"><code>clearForces</code></h3>

        <span class="paren">()</span>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_world_World.js.html#l936"><code>src&#x2F;world&#x2F;World.js:936</code></a>
        </p>



    </div>

    <div class="description">
        <p>Sets all body forces in the world to zero.</p>

    </div>




</div>
<div id="method_collisionMatrixTick" class="method item">
    <h3 class="name"><code>collisionMatrixTick</code></h3>

        <span class="paren">()</span>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_world_World.js.html#l227"><code>src&#x2F;world&#x2F;World.js:227</code></a>
        </p>



    </div>

    <div class="description">
        <p>Store old collision state info</p>

    </div>




</div>
<div id="method_dispatchEvent" class="method item inherited">
    <h3 class="name"><code>dispatchEvent</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>event</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type"><a href="../classes/EventTarget.html" class="crosslink">EventTarget</a></span>
        </span>







    <div class="meta">
                <p>Inherited from
                <a href="../classes/EventTarget.html#method_dispatchEvent">EventTarget</a>:
        <a href="../files/src_utils_EventTarget.js.html#l68"><code>src&#x2F;utils&#x2F;EventTarget.js:68</code></a>
        </p>



    </div>

    <div class="description">
        <p>Emit an event.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">event</code>
                        <span class="type">Object</span>


                    <div class="param-description">
                         
                    </div>

                        <ul class="params-list">
                            <li class="param">
                                    <code class="param-name">type</code>
                                    <span class="type">String</span>

                                <div class="param-description">
                                    
                                </div>

                            </li>
                        </ul>
                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type"><a href="../classes/EventTarget.html" class="crosslink">EventTarget</a></span>:
                    <p>The self object, for chainability.</p>

            </div>
        </div>


</div>
<div id="method_getContactMaterial" class="method item">
    <h3 class="name"><code>getContactMaterial</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>m1</code>
                </li>
                <li class="arg">
                        <code>m2</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type"><a href="../classes/ContactMaterial.html" class="crosslink">ContactMaterial</a></span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_world_World.js.html#l206"><code>src&#x2F;world&#x2F;World.js:206</code></a>
        </p>



    </div>

    <div class="description">
        <p>Get the contact material between materials m1 and m2</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">m1</code>
                        <span class="type"><a href="../classes/Material.html" class="crosslink">Material</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">m2</code>
                        <span class="type"><a href="../classes/Material.html" class="crosslink">Material</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type"><a href="../classes/ContactMaterial.html" class="crosslink">ContactMaterial</a></span>:
                    <p>The contact material if it was found.</p>

            </div>
        </div>


</div>
<div id="method_hasEventListener" class="method item inherited">
    <h3 class="name"><code>hasEventListener</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>type</code>
                </li>
                <li class="arg">
                        <code>listener</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type">Boolean</span>
        </span>







    <div class="meta">
                <p>Inherited from
                <a href="../classes/EventTarget.html#method_hasEventListener">EventTarget</a>:
        <a href="../files/src_utils_EventTarget.js.html#l34"><code>src&#x2F;utils&#x2F;EventTarget.js:34</code></a>
        </p>



    </div>

    <div class="description">
        <p>Check if an event listener is added</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">type</code>
                        <span class="type">String</span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">listener</code>
                        <span class="type">Function</span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type">Boolean</span>:
            </div>
        </div>


</div>
<div id="method_numObjects" class="method item deprecated">
    <h3 class="name"><code>numObjects</code></h3>

        <span class="paren">()</span>

        <span class="returns-inline">
            <span class="type">Number</span>
        </span>

        <span class="flag deprecated">deprecated</span>






    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_world_World.js.html#l217"><code>src&#x2F;world&#x2F;World.js:217</code></a>
        </p>



    </div>

    <div class="description">
        <p>Get number of objects in the world.</p>

    </div>


        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type">Number</span>:
            </div>
        </div>


</div>
<div id="method_raycastAll" class="method item">
    <h3 class="name"><code>raycastAll</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>from</code>
                </li>
                <li class="arg">
                        <code>to</code>
                </li>
                <li class="arg">
                        <code>options</code>
                </li>
                <li class="arg">
                        <code>callback</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type">Boolean</span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_world_World.js.html#l308"><code>src&#x2F;world&#x2F;World.js:308</code></a>
        </p>



    </div>

    <div class="description">
        <p>Ray cast against all bodies. The provided callback will be executed for each hit with a RaycastResult as single argument.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">from</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">to</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">options</code>
                        <span class="type">Object</span>


                    <div class="param-description">
                         
                    </div>

                        <ul class="params-list">
                            <li class="param">
                                    <code class="param-name optional">[collisionFilterMask=-1]</code>
                                    <span class="type">Number</span>
                                    <span class="flag optional" title="This parameter is optional.">optional</span>

                                <div class="param-description">
                                    
                                </div>

                            </li>
                            <li class="param">
                                    <code class="param-name optional">[collisionFilterGroup=-1]</code>
                                    <span class="type">Number</span>
                                    <span class="flag optional" title="This parameter is optional.">optional</span>

                                <div class="param-description">
                                    
                                </div>

                            </li>
                            <li class="param">
                                    <code class="param-name optional">[skipBackfaces=false]</code>
                                    <span class="type">Boolean</span>
                                    <span class="flag optional" title="This parameter is optional.">optional</span>

                                <div class="param-description">
                                    
                                </div>

                            </li>
                            <li class="param">
                                    <code class="param-name optional">[checkCollisionResponse=true]</code>
                                    <span class="type">Boolean</span>
                                    <span class="flag optional" title="This parameter is optional.">optional</span>

                                <div class="param-description">
                                    
                                </div>

                            </li>
                        </ul>
                </li>
                <li class="param">
                        <code class="param-name">callback</code>
                        <span class="type">Function</span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type">Boolean</span>:
                    <p>True if any body was hit.</p>

            </div>
        </div>


</div>
<div id="method_raycastAny" class="method item">
    <h3 class="name"><code>raycastAny</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>from</code>
                </li>
                <li class="arg">
                        <code>to</code>
                </li>
                <li class="arg">
                        <code>options</code>
                </li>
                <li class="arg">
                        <code>result</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type">Boolean</span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_world_World.js.html#l329"><code>src&#x2F;world&#x2F;World.js:329</code></a>
        </p>



    </div>

    <div class="description">
        <p>Ray cast, and stop at the first result. Note that the order is random - but the method is fast.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">from</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">to</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">options</code>
                        <span class="type">Object</span>


                    <div class="param-description">
                         
                    </div>

                        <ul class="params-list">
                            <li class="param">
                                    <code class="param-name optional">[collisionFilterMask=-1]</code>
                                    <span class="type">Number</span>
                                    <span class="flag optional" title="This parameter is optional.">optional</span>

                                <div class="param-description">
                                    
                                </div>

                            </li>
                            <li class="param">
                                    <code class="param-name optional">[collisionFilterGroup=-1]</code>
                                    <span class="type">Number</span>
                                    <span class="flag optional" title="This parameter is optional.">optional</span>

                                <div class="param-description">
                                    
                                </div>

                            </li>
                            <li class="param">
                                    <code class="param-name optional">[skipBackfaces=false]</code>
                                    <span class="type">Boolean</span>
                                    <span class="flag optional" title="This parameter is optional.">optional</span>

                                <div class="param-description">
                                    
                                </div>

                            </li>
                            <li class="param">
                                    <code class="param-name optional">[checkCollisionResponse=true]</code>
                                    <span class="type">Boolean</span>
                                    <span class="flag optional" title="This parameter is optional.">optional</span>

                                <div class="param-description">
                                    
                                </div>

                            </li>
                        </ul>
                </li>
                <li class="param">
                        <code class="param-name">result</code>
                        <span class="type"><a href="../classes/RaycastResult.html" class="crosslink">RaycastResult</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type">Boolean</span>:
                    <p>True if any body was hit.</p>

            </div>
        </div>


</div>
<div id="method_raycastClosest" class="method item">
    <h3 class="name"><code>raycastClosest</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>from</code>
                </li>
                <li class="arg">
                        <code>to</code>
                </li>
                <li class="arg">
                        <code>options</code>
                </li>
                <li class="arg">
                        <code>result</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type">Boolean</span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_world_World.js.html#l350"><code>src&#x2F;world&#x2F;World.js:350</code></a>
        </p>



    </div>

    <div class="description">
        <p>Ray cast, and return information of the closest hit.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">from</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">to</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">options</code>
                        <span class="type">Object</span>


                    <div class="param-description">
                         
                    </div>

                        <ul class="params-list">
                            <li class="param">
                                    <code class="param-name optional">[collisionFilterMask=-1]</code>
                                    <span class="type">Number</span>
                                    <span class="flag optional" title="This parameter is optional.">optional</span>

                                <div class="param-description">
                                    
                                </div>

                            </li>
                            <li class="param">
                                    <code class="param-name optional">[collisionFilterGroup=-1]</code>
                                    <span class="type">Number</span>
                                    <span class="flag optional" title="This parameter is optional.">optional</span>

                                <div class="param-description">
                                    
                                </div>

                            </li>
                            <li class="param">
                                    <code class="param-name optional">[skipBackfaces=false]</code>
                                    <span class="type">Boolean</span>
                                    <span class="flag optional" title="This parameter is optional.">optional</span>

                                <div class="param-description">
                                    
                                </div>

                            </li>
                            <li class="param">
                                    <code class="param-name optional">[checkCollisionResponse=true]</code>
                                    <span class="type">Boolean</span>
                                    <span class="flag optional" title="This parameter is optional.">optional</span>

                                <div class="param-description">
                                    
                                </div>

                            </li>
                        </ul>
                </li>
                <li class="param">
                        <code class="param-name">result</code>
                        <span class="type"><a href="../classes/RaycastResult.html" class="crosslink">RaycastResult</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type">Boolean</span>:
                    <p>True if any body was hit.</p>

            </div>
        </div>


</div>
<div id="method_rayTest" class="method item deprecated">
    <h3 class="name"><code>rayTest</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>from</code>
                </li>
                <li class="arg">
                        <code>to</code>
                </li>
                <li class="arg">
                        <code>result</code>
                </li>
            </ul><span class="paren">)</span>
        </div>


        <span class="flag deprecated" title="Use .raycastAll, .raycastClosest or .raycastAny instead.">deprecated</span>






    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_world_World.js.html#l286"><code>src&#x2F;world&#x2F;World.js:286</code></a>
        </p>


            <p>Deprecated: Use .raycastAll, .raycastClosest or .raycastAny instead.</p>

    </div>

    <div class="description">
        <p>Raycast test</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">from</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">to</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">result</code>
                        <span class="type">Function | <a href="../classes/RaycastResult.html" class="crosslink">RaycastResult</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_remove" class="method item deprecated">
    <h3 class="name"><code>remove</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>body</code>
                </li>
            </ul><span class="paren">)</span>
        </div>


        <span class="flag deprecated" title="Use .removeBody instead">deprecated</span>






    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_world_World.js.html#l371"><code>src&#x2F;world&#x2F;World.js:371</code></a>
        </p>


            <p>Deprecated: Use .removeBody instead</p>

    </div>

    <div class="description">
        <p>Remove a rigid body from the simulation.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">body</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_removeBody" class="method item">
    <h3 class="name"><code>removeBody</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>body</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_world_World.js.html#l396"><code>src&#x2F;world&#x2F;World.js:396</code></a>
        </p>



    </div>

    <div class="description">
        <p>Remove a rigid body from the simulation.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">body</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_removeConstraint" class="method item">
    <h3 class="name"><code>removeConstraint</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>c</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_world_World.js.html#l274"><code>src&#x2F;world&#x2F;World.js:274</code></a>
        </p>



    </div>

    <div class="description">
        <p>Removes a constraint</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">c</code>
                        <span class="type"><a href="../classes/Constraint.html" class="crosslink">Constraint</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_removeEventListener" class="method item inherited">
    <h3 class="name"><code>removeEventListener</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>type</code>
                </li>
                <li class="arg">
                        <code>listener</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type"><a href="../classes/EventTarget.html" class="crosslink">EventTarget</a></span>
        </span>







    <div class="meta">
                <p>Inherited from
                <a href="../classes/EventTarget.html#method_removeEventListener">EventTarget</a>:
        <a href="../files/src_utils_EventTarget.js.html#l50"><code>src&#x2F;utils&#x2F;EventTarget.js:50</code></a>
        </p>



    </div>

    <div class="description">
        <p>Remove an event listener</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">type</code>
                        <span class="type">String</span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">listener</code>
                        <span class="type">Function</span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type"><a href="../classes/EventTarget.html" class="crosslink">EventTarget</a></span>:
                    <p>The self object, for chainability.</p>

            </div>
        </div>


</div>
<div id="method_step" class="method item">
    <h3 class="name"><code>step</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>dt</code>
                </li>
                <li class="arg">
                        <code class="optional">[timeSinceLastCalled]</code>
                </li>
                <li class="arg">
                        <code class="optional">[maxSubSteps=10]</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_world_World.js.html#l443"><code>src&#x2F;world&#x2F;World.js:443</code></a>
        </p>



    </div>

    <div class="description">
        <p>Step the physics world forward in time.</p>
<p>There are two modes. The simple mode is fixed timestepping without interpolation. In this case you only use the first argument. The second case uses interpolation. In that you also provide the time since the function was last used, as well as the maximum fixed timesteps to take.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">dt</code>
                        <span class="type">Number</span>


                    <div class="param-description">
                        <p>The fixed time step size to use.</p>

                    </div>

                </li>
                <li class="param">
                        <code class="param-name optional">[timeSinceLastCalled]</code>
                        <span class="type">Number</span>
                        <span class="flag optional" title="This parameter is optional.">optional</span>


                    <div class="param-description">
                        <p>The time elapsed since the function was last called.</p>

                    </div>

                </li>
                <li class="param">
                        <code class="param-name optional">[maxSubSteps=10]</code>
                        <span class="type">Number</span>
                        <span class="flag optional" title="This parameter is optional.">optional</span>


                    <div class="param-description">
                        <p>Maximum number of fixed steps to take per function call.</p>

                    </div>

                </li>
            </ul>
        </div>



        <div class="example">
            <h4>Example:</h4>

            <div class="example-content">
                <pre class="code prettyprint"><code>// fixed timestepping without interpolation
world.step(1/60);
</code></pre>
            </div>
        </div>
</div>
<div id="method_step" class="method item">
    <h3 class="name"><code>step</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>dt</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_world_World.js.html#l517"><code>src&#x2F;world&#x2F;World.js:517</code></a>
        </p>



    </div>

    <div class="description">
        <p>Step the simulation</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">dt</code>
                        <span class="type">Number</span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
            </div>

            <div id="properties" class="api-class-tabpanel">
                <h2 class="off-left">Properties</h2>

                    <div id="property_allowSleep" class="property item">
                        <h3 class="name"><code>allowSleep</code></h3>
                        <span class="type">Boolean</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_world_World.js.html#l39"><code>src&#x2F;world&#x2F;World.js:39</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Makes bodies go to sleep when they&#39;ve been inactive</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_bodies" class="property item">
                        <h3 class="name"><code>bodies</code></h3>
                        <span class="type">Array</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_world_World.js.html#l100"><code>src&#x2F;world&#x2F;World.js:100</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_broadphase" class="property item">
                        <h3 class="name"><code>broadphase</code></h3>
                        <span class="type"><a href="../classes/Broadphase.html" class="crosslink">Broadphase</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_world_World.js.html#l94"><code>src&#x2F;world&#x2F;World.js:94</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_collisionMatrix" class="property item">
                        <h3 class="name"><code>collisionMatrix</code></h3>
                        <span class="type"><a href="../classes/ArrayCollisionMatrix.html" class="crosslink">ArrayCollisionMatrix</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_world_World.js.html#l124"><code>src&#x2F;world&#x2F;World.js:124</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_collisionMatrixPrevious" class="property item">
                        <h3 class="name"><code>collisionMatrixPrevious</code></h3>
                        <span class="type"><a href="../classes/ArrayCollisionMatrix.html" class="crosslink">ArrayCollisionMatrix</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_world_World.js.html#l130"><code>src&#x2F;world&#x2F;World.js:130</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>CollisionMatrix from the previous step.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_constraints" class="property item">
                        <h3 class="name"><code>constraints</code></h3>
                        <span class="type">Array</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_world_World.js.html#l112"><code>src&#x2F;world&#x2F;World.js:112</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_contactmaterials" class="property item">
                        <h3 class="name"><code>contactmaterials</code></h3>
                        <span class="type">Array</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_world_World.js.html#l144"><code>src&#x2F;world&#x2F;World.js:144</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_contactMaterialTable" class="property item">
                        <h3 class="name"><code>contactMaterialTable</code></h3>
                        <span class="type"><a href="../classes/TupleDictionary.html" class="crosslink">TupleDictionary</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_world_World.js.html#l150"><code>src&#x2F;world&#x2F;World.js:150</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Used to look up a ContactMaterial given two instances of Material.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_contacts" class="property item">
                        <h3 class="name"><code>contacts</code></h3>
                        <span class="type">Array</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_world_World.js.html#l46"><code>src&#x2F;world&#x2F;World.js:46</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>All the current contacts (instances of ContactEquation) in the world.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_defaultContactMaterial" class="property item">
                        <h3 class="name"><code>defaultContactMaterial</code></h3>
                        <span class="type"><a href="../classes/ContactMaterial.html" class="crosslink">ContactMaterial</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_world_World.js.html#l158"><code>src&#x2F;world&#x2F;World.js:158</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>This contact material is used if no suitable contactmaterial is found for a contact.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_doProfiling" class="property item">
                        <h3 class="name"><code>doProfiling</code></h3>
                        <span class="type">Boolean</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_world_World.js.html#l165"><code>src&#x2F;world&#x2F;World.js:165</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_dt" class="property item">
                        <h3 class="name"><code>dt</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_world_World.js.html#l33"><code>src&#x2F;world&#x2F;World.js:33</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Currently / last used timestep. Is set to -1 if not available. This value is updated before each internal step, which means that it is &quot;fresh&quot; inside event callbacks.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_gravity" class="property item">
                        <h3 class="name"><code>gravity</code></h3>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_world_World.js.html#l88"><code>src&#x2F;world&#x2F;World.js:88</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_materials" class="property item">
                        <h3 class="name"><code>materials</code></h3>
                        <span class="type">Array</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_world_World.js.html#l137"><code>src&#x2F;world&#x2F;World.js:137</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>All added materials</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_narrowphase" class="property item">
                        <h3 class="name"><code>narrowphase</code></h3>
                        <span class="type"><a href="../classes/Narrowphase.html" class="crosslink">Narrowphase</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_world_World.js.html#l118"><code>src&#x2F;world&#x2F;World.js:118</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_profile" class="property item">
                        <h3 class="name"><code>profile</code></h3>
                        <span class="type">Object</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_world_World.js.html#l171"><code>src&#x2F;world&#x2F;World.js:171</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_quatNormalizeFast" class="property item">
                        <h3 class="name"><code>quatNormalizeFast</code></h3>
                        <span class="type">Boolean</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_world_World.js.html#l61"><code>src&#x2F;world&#x2F;World.js:61</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Set to true to use fast quaternion normalization. It is often enough accurate to use. If bodies tend to explode, set to false.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_quatNormalizeSkip" class="property item">
                        <h3 class="name"><code>quatNormalizeSkip</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_world_World.js.html#l54"><code>src&#x2F;world&#x2F;World.js:54</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>How often to normalize quaternions. Set to 0 for every step, 1 for every second etc.. A larger value increases performance. If bodies tend to explode, set to a smaller value (zero to be sure nothing can go wrong).</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_solver" class="property item">
                        <h3 class="name"><code>solver</code></h3>
                        <span class="type"><a href="../classes/Solver.html" class="crosslink">Solver</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_world_World.js.html#l106"><code>src&#x2F;world&#x2F;World.js:106</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_stepnumber" class="property item">
                        <h3 class="name"><code>stepnumber</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_world_World.js.html#l77"><code>src&#x2F;world&#x2F;World.js:77</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Number of timesteps taken since start</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_subsystems" class="property item">
                        <h3 class="name"><code>subsystems</code></h3>
                        <span class="type">Array</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_world_World.js.html#l183"><code>src&#x2F;world&#x2F;World.js:183</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_time" class="property item">
                        <h3 class="name"><code>time</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_world_World.js.html#l70"><code>src&#x2F;world&#x2F;World.js:70</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>The wall-clock time since simulation start</p>
                    
                        </div>
                    
                    
                    
                    </div>
            </div>


    </div>
</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="../assets/vendor/prettify/prettify-min.js"></script>
<script>prettyPrint();</script>
<script src="../assets/js/yui-prettify.js"></script>
<script src="../assets/../api.js"></script>
<script src="../assets/js/api-filter.js"></script>
<script src="../assets/js/api-list.js"></script>
<script src="../assets/js/api-search.js"></script>
<script src="../assets/js/apidocs.js"></script>
</body>
</html>
