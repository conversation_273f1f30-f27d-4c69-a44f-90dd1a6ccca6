<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>RaycastVehicle - cannon</title>
    <link rel="stylesheet" href="http://yui.yahooapis.com/3.9.1/build/cssgrids/cssgrids-min.css">
    <link rel="stylesheet" href="../assets/vendor/prettify/prettify-min.css">
    <link rel="stylesheet" href="../assets/css/main.css" id="site_styles">
    <link rel="icon" href="../assets/favicon.ico">
    <script src="http://yui.yahooapis.com/combo?3.9.1/build/yui/yui-min.js"></script>
</head>
<body class="yui3-skin-sam">

<div id="doc">
    <div id="hd" class="yui3-g header">
        <div class="yui3-u-3-4">
                <h1><img src="../assets/css/logo.png" title="cannon" width="117" height="52"></h1>
        </div>
        <div class="yui3-u-1-4 version">
            <em>API Docs for: 0.6.1</em>
        </div>
    </div>
    <div id="bd" class="yui3-g">

        <div class="yui3-u-1-4">
            <div id="docs-sidebar" class="sidebar apidocs">
                <div id="api-list">
                    <h2 class="off-left">APIs</h2>
                    <div id="api-tabview" class="tabview">
                        <ul class="tabs">
                            <li><a href="#api-classes">Classes</a></li>
                            <li><a href="#api-modules">Modules</a></li>
                        </ul>
                
                        <div id="api-tabview-filter">
                            <input type="search" id="api-filter" placeholder="Type to filter APIs">
                        </div>
                
                        <div id="api-tabview-panel">
                            <ul id="api-classes" class="apis classes">
                                <li><a href="../classes/AABB.html">AABB</a></li>
                                <li><a href="../classes/ArrayCollisionMatrix.html">ArrayCollisionMatrix</a></li>
                                <li><a href="../classes/Body.html">Body</a></li>
                                <li><a href="../classes/Box.html">Box</a></li>
                                <li><a href="../classes/Broadphase.html">Broadphase</a></li>
                                <li><a href="../classes/ConeEquation.html">ConeEquation</a></li>
                                <li><a href="../classes/ConeTwistConstraint.html">ConeTwistConstraint</a></li>
                                <li><a href="../classes/Constraint.html">Constraint</a></li>
                                <li><a href="../classes/ContactEquation.html">ContactEquation</a></li>
                                <li><a href="../classes/ContactMaterial.html">ContactMaterial</a></li>
                                <li><a href="../classes/ConvexPolyhedron.html">ConvexPolyhedron</a></li>
                                <li><a href="../classes/Cylinder.html">Cylinder</a></li>
                                <li><a href="../classes/Demo.html">Demo</a></li>
                                <li><a href="../classes/DistanceConstraint.html">DistanceConstraint</a></li>
                                <li><a href="../classes/Equation.html">Equation</a></li>
                                <li><a href="../classes/EventTarget.html">EventTarget</a></li>
                                <li><a href="../classes/FrictionEquation.html">FrictionEquation</a></li>
                                <li><a href="../classes/GridBroadphase.html">GridBroadphase</a></li>
                                <li><a href="../classes/GSSolver.html">GSSolver</a></li>
                                <li><a href="../classes/Heightfield.html">Heightfield</a></li>
                                <li><a href="../classes/HingeConstraint.html">HingeConstraint</a></li>
                                <li><a href="../classes/JacobianElement.html">JacobianElement</a></li>
                                <li><a href="../classes/LockConstraint.html">LockConstraint</a></li>
                                <li><a href="../classes/Mat3.html">Mat3</a></li>
                                <li><a href="../classes/Material.html">Material</a></li>
                                <li><a href="../classes/NaiveBroadphase.html">NaiveBroadphase</a></li>
                                <li><a href="../classes/Narrowphase.html">Narrowphase</a></li>
                                <li><a href="../classes/ObjectCollisionMatrix.html">ObjectCollisionMatrix</a></li>
                                <li><a href="../classes/Octree.html">Octree</a></li>
                                <li><a href="../classes/OctreeNode.html">OctreeNode</a></li>
                                <li><a href="../classes/Particle.html">Particle</a></li>
                                <li><a href="../classes/Plane.html">Plane</a></li>
                                <li><a href="../classes/PointToPointConstraint.html">PointToPointConstraint</a></li>
                                <li><a href="../classes/Pool.html">Pool</a></li>
                                <li><a href="../classes/Quaternion.html">Quaternion</a></li>
                                <li><a href="../classes/Ray.html">Ray</a></li>
                                <li><a href="../classes/RaycastResult.html">RaycastResult</a></li>
                                <li><a href="../classes/RaycastVehicle.html">RaycastVehicle</a></li>
                                <li><a href="../classes/RigidVehicle.html">RigidVehicle</a></li>
                                <li><a href="../classes/RotationalEquation.html">RotationalEquation</a></li>
                                <li><a href="../classes/RotationalMotorEquation.html">RotationalMotorEquation</a></li>
                                <li><a href="../classes/SAPBroadphase.html">SAPBroadphase</a></li>
                                <li><a href="../classes/Shape.html">Shape</a></li>
                                <li><a href="../classes/Solver.html">Solver</a></li>
                                <li><a href="../classes/Sphere.html">Sphere</a></li>
                                <li><a href="../classes/SPHSystem.html">SPHSystem</a></li>
                                <li><a href="../classes/SplitSolver.html">SplitSolver</a></li>
                                <li><a href="../classes/Spring.html">Spring</a></li>
                                <li><a href="../classes/Transform.html">Transform</a></li>
                                <li><a href="../classes/Trimesh.html">Trimesh</a></li>
                                <li><a href="../classes/TupleDictionary.html">TupleDictionary</a></li>
                                <li><a href="../classes/Vec3.html">Vec3</a></li>
                                <li><a href="../classes/Vec3Pool.html">Vec3Pool</a></li>
                                <li><a href="../classes/WheelInfo.html">WheelInfo</a></li>
                                <li><a href="../classes/World.html">World</a></li>
                            </ul>
                
                            <ul id="api-modules" class="apis modules">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="yui3-u-3-4">
                <div id="api-options">
                    Show:
                    <label for="api-show-inherited">
                        <input type="checkbox" id="api-show-inherited" checked>
                        Inherited
                    </label>
            
                    <label for="api-show-protected">
                        <input type="checkbox" id="api-show-protected">
                        Protected
                    </label>
            
                    <label for="api-show-private">
                        <input type="checkbox" id="api-show-private">
                        Private
                    </label>
                    <label for="api-show-deprecated">
                        <input type="checkbox" id="api-show-deprecated">
                        Deprecated
                    </label>
            
                </div>
            
            <div class="apidocs">
                <div id="docs-main">
                    <div class="content">
<h1>RaycastVehicle Class</h1>
<div class="box meta">


        <div class="foundat">
            Defined in: <a href="../files/src_objects_RaycastVehicle.js.html#l10"><code>src&#x2F;objects&#x2F;RaycastVehicle.js:10</code></a>
        </div>


</div>


<div class="box intro">
    <p>Vehicle helper class that casts rays from the wheel positions towards the ground and applies forces.</p>

</div>

    <div class="constructor">
        <h2>Constructor</h2>
        <div id="method_RaycastVehicle" class="method item">
            <h3 class="name"><code>RaycastVehicle</code></h3>
        
                <div class="args">
                    <span class="paren">(</span><ul class="args-list inline commas">
                        <li class="arg">
                                <code class="optional">[options]</code>
                        </li>
                    </ul><span class="paren">)</span>
                </div>
        
        
        
        
        
        
        
        
            <div class="meta">
                        <p>
                        Defined in
                <a href="../files/src_objects_RaycastVehicle.js.html#l10"><code>src&#x2F;objects&#x2F;RaycastVehicle.js:10</code></a>
                </p>
        
        
        
            </div>
        
            <div class="description">
                
            </div>
        
                <div class="params">
                    <h4>Parameters:</h4>
        
                    <ul class="params-list">
                        <li class="param">
                                <code class="param-name optional">[options]</code>
                                <span class="type">Object</span>
                                <span class="flag optional" title="This parameter is optional.">optional</span>
        
        
                            <div class="param-description">
                                 
                            </div>
        
                                <ul class="params-list">
                                    <li class="param">
                                            <code class="param-name optional">[chassisBody]</code>
                                            <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>
                                            <span class="flag optional" title="This parameter is optional.">optional</span>
        
                                        <div class="param-description">
                                            <p>The car chassis body.</p>
        
                                        </div>
        
                                    </li>
                                    <li class="param">
                                            <code class="param-name optional">[indexRightAxis]</code>
                                            <span class="type">Integer</span>
                                            <span class="flag optional" title="This parameter is optional.">optional</span>
        
                                        <div class="param-description">
                                            <p>Axis to use for right. x=0, y=1, z=2</p>
        
                                        </div>
        
                                    </li>
                                    <li class="param">
                                            <code class="param-name optional">[indexLeftAxis]</code>
                                            <span class="type">Integer</span>
                                            <span class="flag optional" title="This parameter is optional.">optional</span>
        
                                        <div class="param-description">
                                            
                                        </div>
        
                                    </li>
                                    <li class="param">
                                            <code class="param-name optional">[indexUpAxis]</code>
                                            <span class="type">Integer</span>
                                            <span class="flag optional" title="This parameter is optional.">optional</span>
        
                                        <div class="param-description">
                                            
                                        </div>
        
                                    </li>
                                </ul>
                        </li>
                    </ul>
                </div>
        
        
        
        </div>
    </div>

<div id="classdocs" class="tabview">
    <ul class="api-class-tabs">
        <li class="api-class-tab index"><a href="#index">Index</a></li>

            <li class="api-class-tab methods"><a href="#methods">Methods</a></li>
            <li class="api-class-tab properties"><a href="#properties">Properties</a></li>
    </ul>

    <div>
        <div id="index" class="api-class-tabpanel index">
            <h2 class="off-left">Item Index</h2>

                <div class="index-section methods">
                    <h3>Methods</h3>

                    <ul class="index-list methods">
                            <li class="index-item method">
                                <a href="#method_addToWorld">addToWorld</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_addWheel">addWheel</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_applyEngineForce">applyEngineForce</a>

                            </li>
                            <li class="index-item method private">
                                <a href="#method_getVehicleAxisWorld">getVehicleAxisWorld</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_getWheelTransformWorld">getWheelTransformWorld</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_removeFromWorld">removeFromWorld</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_setBrake">setBrake</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_setSteeringValue">setSteeringValue</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_updateWheelTransform">updateWheelTransform</a>

                            </li>
                    </ul>
                </div>

                <div class="index-section properties">
                    <h3>Properties</h3>

                    <ul class="index-list properties">
                            <li class="index-item property">
                                <a href="#property_chassisBody">chassisBody</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_indexForwardAxis">indexForwardAxis</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_indexRightAxis">indexRightAxis</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_indexUpAxis">indexUpAxis</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_sliding">sliding</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_wheelInfos">wheelInfos</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_world">world</a>

                            </li>
                    </ul>
                </div>


        </div>

            <div id="methods" class="api-class-tabpanel">
                <h2 class="off-left">Methods</h2>

<div id="method_addToWorld" class="method item">
    <h3 class="name"><code>addToWorld</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>world</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_objects_RaycastVehicle.js.html#l122"><code>src&#x2F;objects&#x2F;RaycastVehicle.js:122</code></a>
        </p>



    </div>

    <div class="description">
        <p>Add the vehicle including its constraints to the world.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">world</code>
                        <span class="type"><a href="../classes/World.html" class="crosslink">World</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_addWheel" class="method item">
    <h3 class="name"><code>addWheel</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code class="optional">[options]</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_objects_RaycastVehicle.js.html#l74"><code>src&#x2F;objects&#x2F;RaycastVehicle.js:74</code></a>
        </p>



    </div>

    <div class="description">
        <p>Add a wheel. For information about the options, see WheelInfo.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name optional">[options]</code>
                        <span class="type">Object</span>
                        <span class="flag optional" title="This parameter is optional.">optional</span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_applyEngineForce" class="method item">
    <h3 class="name"><code>applyEngineForce</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>value</code>
                </li>
                <li class="arg">
                        <code>wheelIndex</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_objects_RaycastVehicle.js.html#l102"><code>src&#x2F;objects&#x2F;RaycastVehicle.js:102</code></a>
        </p>



    </div>

    <div class="description">
        <p>Set the wheel force to apply on one of the wheels each time step</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">value</code>
                        <span class="type">Number</span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">wheelIndex</code>
                        <span class="type">Integer</span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_getVehicleAxisWorld" class="method item private">
    <h3 class="name"><code>getVehicleAxisWorld</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>axisIndex</code>
                </li>
                <li class="arg">
                        <code>result</code>
                </li>
            </ul><span class="paren">)</span>
        </div>



        <span class="flag private">private</span>





    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_objects_RaycastVehicle.js.html#l138"><code>src&#x2F;objects&#x2F;RaycastVehicle.js:138</code></a>
        </p>



    </div>

    <div class="description">
        <p>Get one of the wheel axles, world-oriented.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">axisIndex</code>
                        <span class="type">Integer</span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">result</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_getWheelTransformWorld" class="method item">
    <h3 class="name"><code>getWheelTransformWorld</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>wheelIndex</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type"><a href="../classes/Transform.html" class="crosslink">Transform</a></span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_objects_RaycastVehicle.js.html#l428"><code>src&#x2F;objects&#x2F;RaycastVehicle.js:428</code></a>
        </p>



    </div>

    <div class="description">
        <p>Get the world transform of one of the wheels</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">wheelIndex</code>
                        <span class="type">Integer</span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type"><a href="../classes/Transform.html" class="crosslink">Transform</a></span>:
            </div>
        </div>


</div>
<div id="method_removeFromWorld" class="method item">
    <h3 class="name"><code>removeFromWorld</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>world</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_objects_RaycastVehicle.js.html#l279"><code>src&#x2F;objects&#x2F;RaycastVehicle.js:279</code></a>
        </p>



    </div>

    <div class="description">
        <p>Remove the vehicle including its constraints from the world.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">world</code>
                        <span class="type"><a href="../classes/World.html" class="crosslink">World</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_setBrake" class="method item">
    <h3 class="name"><code>setBrake</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>brake</code>
                </li>
                <li class="arg">
                        <code>wheelIndex</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_objects_RaycastVehicle.js.html#l112"><code>src&#x2F;objects&#x2F;RaycastVehicle.js:112</code></a>
        </p>



    </div>

    <div class="description">
        <p>Set the braking force of a wheel</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">brake</code>
                        <span class="type">Number</span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">wheelIndex</code>
                        <span class="type">Integer</span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_setSteeringValue" class="method item">
    <h3 class="name"><code>setSteeringValue</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>value</code>
                </li>
                <li class="arg">
                        <code>wheelIndex</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_objects_RaycastVehicle.js.html#l89"><code>src&#x2F;objects&#x2F;RaycastVehicle.js:89</code></a>
        </p>



    </div>

    <div class="description">
        <p>Set the steering value of a wheel.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">value</code>
                        <span class="type">Number</span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">wheelIndex</code>
                        <span class="type">Integer</span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_updateWheelTransform" class="method item">
    <h3 class="name"><code>updateWheelTransform</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>wheelIndex</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_objects_RaycastVehicle.js.html#l380"><code>src&#x2F;objects&#x2F;RaycastVehicle.js:380</code></a>
        </p>



    </div>

    <div class="description">
        <p>Update one of the wheel transform.
Note when rendering wheels: during each step, wheel transforms are updated BEFORE the chassis; ie. their position becomes invalid after the step. Thus when you render wheels, you must update wheel transforms before rendering them. See raycastVehicle demo for an example.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">wheelIndex</code>
                        <span class="type">Integer</span>


                    <div class="param-description">
                        <p>The wheel index to update.</p>

                    </div>

                </li>
            </ul>
        </div>



</div>
            </div>

            <div id="properties" class="api-class-tabpanel">
                <h2 class="off-left">Properties</h2>

                    <div id="property_chassisBody" class="property item">
                        <h3 class="name"><code>chassisBody</code></h3>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_RaycastVehicle.js.html#l22"><code>src&#x2F;objects&#x2F;RaycastVehicle.js:22</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_indexForwardAxis" class="property item">
                        <h3 class="name"><code>indexForwardAxis</code></h3>
                        <span class="type">Integer</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_RaycastVehicle.js.html#l51"><code>src&#x2F;objects&#x2F;RaycastVehicle.js:51</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Index of the forward axis, 0=x, 1=y, 2=z</p>
                    
                        </div>
                    
                            <p><strong>Default:</strong> 0</p>
                    
                    
                    </div>
                    <div id="property_indexRightAxis" class="property item">
                        <h3 class="name"><code>indexRightAxis</code></h3>
                        <span class="type">Integer</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_RaycastVehicle.js.html#l44"><code>src&#x2F;objects&#x2F;RaycastVehicle.js:44</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Index of the right axis, 0=x, 1=y, 2=z</p>
                    
                        </div>
                    
                            <p><strong>Default:</strong> 1</p>
                    
                    
                    </div>
                    <div id="property_indexUpAxis" class="property item">
                        <h3 class="name"><code>indexUpAxis</code></h3>
                        <span class="type">Integer</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_RaycastVehicle.js.html#l58"><code>src&#x2F;objects&#x2F;RaycastVehicle.js:58</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Index of the up axis, 0=x, 1=y, 2=z</p>
                    
                        </div>
                    
                            <p><strong>Default:</strong> 2</p>
                    
                    
                    </div>
                    <div id="property_sliding" class="property item">
                        <h3 class="name"><code>sliding</code></h3>
                        <span class="type">Boolean</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_RaycastVehicle.js.html#l33"><code>src&#x2F;objects&#x2F;RaycastVehicle.js:33</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Will be set to true if the car is sliding.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_wheelInfos" class="property item">
                        <h3 class="name"><code>wheelInfos</code></h3>
                        <span class="type">Array</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_RaycastVehicle.js.html#l27"><code>src&#x2F;objects&#x2F;RaycastVehicle.js:27</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>An array of WheelInfo objects.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_world" class="property item">
                        <h3 class="name"><code>world</code></h3>
                        <span class="type"><a href="../classes/World.html" class="crosslink">World</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_RaycastVehicle.js.html#l39"><code>src&#x2F;objects&#x2F;RaycastVehicle.js:39</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
            </div>


    </div>
</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="../assets/vendor/prettify/prettify-min.js"></script>
<script>prettyPrint();</script>
<script src="../assets/js/yui-prettify.js"></script>
<script src="../assets/../api.js"></script>
<script src="../assets/js/api-filter.js"></script>
<script src="../assets/js/api-list.js"></script>
<script src="../assets/js/api-search.js"></script>
<script src="../assets/js/apidocs.js"></script>
</body>
</html>
