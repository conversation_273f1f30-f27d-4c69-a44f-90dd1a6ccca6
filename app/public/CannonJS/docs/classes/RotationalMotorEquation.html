<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>RotationalMotorEquation - cannon</title>
    <link rel="stylesheet" href="http://yui.yahooapis.com/3.9.1/build/cssgrids/cssgrids-min.css">
    <link rel="stylesheet" href="../assets/vendor/prettify/prettify-min.css">
    <link rel="stylesheet" href="../assets/css/main.css" id="site_styles">
    <link rel="icon" href="../assets/favicon.ico">
    <script src="http://yui.yahooapis.com/combo?3.9.1/build/yui/yui-min.js"></script>
</head>
<body class="yui3-skin-sam">

<div id="doc">
    <div id="hd" class="yui3-g header">
        <div class="yui3-u-3-4">
                <h1><img src="../assets/css/logo.png" title="cannon" width="117" height="52"></h1>
        </div>
        <div class="yui3-u-1-4 version">
            <em>API Docs for: 0.6.1</em>
        </div>
    </div>
    <div id="bd" class="yui3-g">

        <div class="yui3-u-1-4">
            <div id="docs-sidebar" class="sidebar apidocs">
                <div id="api-list">
                    <h2 class="off-left">APIs</h2>
                    <div id="api-tabview" class="tabview">
                        <ul class="tabs">
                            <li><a href="#api-classes">Classes</a></li>
                            <li><a href="#api-modules">Modules</a></li>
                        </ul>
                
                        <div id="api-tabview-filter">
                            <input type="search" id="api-filter" placeholder="Type to filter APIs">
                        </div>
                
                        <div id="api-tabview-panel">
                            <ul id="api-classes" class="apis classes">
                                <li><a href="../classes/AABB.html">AABB</a></li>
                                <li><a href="../classes/ArrayCollisionMatrix.html">ArrayCollisionMatrix</a></li>
                                <li><a href="../classes/Body.html">Body</a></li>
                                <li><a href="../classes/Box.html">Box</a></li>
                                <li><a href="../classes/Broadphase.html">Broadphase</a></li>
                                <li><a href="../classes/ConeEquation.html">ConeEquation</a></li>
                                <li><a href="../classes/ConeTwistConstraint.html">ConeTwistConstraint</a></li>
                                <li><a href="../classes/Constraint.html">Constraint</a></li>
                                <li><a href="../classes/ContactEquation.html">ContactEquation</a></li>
                                <li><a href="../classes/ContactMaterial.html">ContactMaterial</a></li>
                                <li><a href="../classes/ConvexPolyhedron.html">ConvexPolyhedron</a></li>
                                <li><a href="../classes/Cylinder.html">Cylinder</a></li>
                                <li><a href="../classes/Demo.html">Demo</a></li>
                                <li><a href="../classes/DistanceConstraint.html">DistanceConstraint</a></li>
                                <li><a href="../classes/Equation.html">Equation</a></li>
                                <li><a href="../classes/EventTarget.html">EventTarget</a></li>
                                <li><a href="../classes/FrictionEquation.html">FrictionEquation</a></li>
                                <li><a href="../classes/GridBroadphase.html">GridBroadphase</a></li>
                                <li><a href="../classes/GSSolver.html">GSSolver</a></li>
                                <li><a href="../classes/Heightfield.html">Heightfield</a></li>
                                <li><a href="../classes/HingeConstraint.html">HingeConstraint</a></li>
                                <li><a href="../classes/JacobianElement.html">JacobianElement</a></li>
                                <li><a href="../classes/LockConstraint.html">LockConstraint</a></li>
                                <li><a href="../classes/Mat3.html">Mat3</a></li>
                                <li><a href="../classes/Material.html">Material</a></li>
                                <li><a href="../classes/NaiveBroadphase.html">NaiveBroadphase</a></li>
                                <li><a href="../classes/Narrowphase.html">Narrowphase</a></li>
                                <li><a href="../classes/ObjectCollisionMatrix.html">ObjectCollisionMatrix</a></li>
                                <li><a href="../classes/Octree.html">Octree</a></li>
                                <li><a href="../classes/OctreeNode.html">OctreeNode</a></li>
                                <li><a href="../classes/Particle.html">Particle</a></li>
                                <li><a href="../classes/Plane.html">Plane</a></li>
                                <li><a href="../classes/PointToPointConstraint.html">PointToPointConstraint</a></li>
                                <li><a href="../classes/Pool.html">Pool</a></li>
                                <li><a href="../classes/Quaternion.html">Quaternion</a></li>
                                <li><a href="../classes/Ray.html">Ray</a></li>
                                <li><a href="../classes/RaycastResult.html">RaycastResult</a></li>
                                <li><a href="../classes/RaycastVehicle.html">RaycastVehicle</a></li>
                                <li><a href="../classes/RigidVehicle.html">RigidVehicle</a></li>
                                <li><a href="../classes/RotationalEquation.html">RotationalEquation</a></li>
                                <li><a href="../classes/RotationalMotorEquation.html">RotationalMotorEquation</a></li>
                                <li><a href="../classes/SAPBroadphase.html">SAPBroadphase</a></li>
                                <li><a href="../classes/Shape.html">Shape</a></li>
                                <li><a href="../classes/Solver.html">Solver</a></li>
                                <li><a href="../classes/Sphere.html">Sphere</a></li>
                                <li><a href="../classes/SPHSystem.html">SPHSystem</a></li>
                                <li><a href="../classes/SplitSolver.html">SplitSolver</a></li>
                                <li><a href="../classes/Spring.html">Spring</a></li>
                                <li><a href="../classes/Transform.html">Transform</a></li>
                                <li><a href="../classes/Trimesh.html">Trimesh</a></li>
                                <li><a href="../classes/TupleDictionary.html">TupleDictionary</a></li>
                                <li><a href="../classes/Vec3.html">Vec3</a></li>
                                <li><a href="../classes/Vec3Pool.html">Vec3Pool</a></li>
                                <li><a href="../classes/WheelInfo.html">WheelInfo</a></li>
                                <li><a href="../classes/World.html">World</a></li>
                            </ul>
                
                            <ul id="api-modules" class="apis modules">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="yui3-u-3-4">
                <div id="api-options">
                    Show:
                    <label for="api-show-inherited">
                        <input type="checkbox" id="api-show-inherited" checked>
                        Inherited
                    </label>
            
                    <label for="api-show-protected">
                        <input type="checkbox" id="api-show-protected">
                        Protected
                    </label>
            
                    <label for="api-show-private">
                        <input type="checkbox" id="api-show-private">
                        Private
                    </label>
                    <label for="api-show-deprecated">
                        <input type="checkbox" id="api-show-deprecated">
                        Deprecated
                    </label>
            
                </div>
            
            <div class="apidocs">
                <div id="docs-main">
                    <div class="content">
<h1>RotationalMotorEquation Class</h1>
<div class="box meta">

        <div class="extends">
            Extends <a href="../classes/Equation.html" class="crosslink">Equation</a>
        </div>

        <div class="foundat">
            Defined in: <a href="../files/src_equations_RotationalMotorEquation.js.html#l7"><code>src&#x2F;equations&#x2F;RotationalMotorEquation.js:7</code></a>
        </div>


</div>


<div class="box intro">
    <p>Rotational motor constraint. Tries to keep the relative angular velocity of the bodies to a given value.</p>

</div>

    <div class="constructor">
        <h2>Constructor</h2>
        <div id="method_RotationalMotorEquation" class="method item">
            <h3 class="name"><code>RotationalMotorEquation</code></h3>
        
                <div class="args">
                    <span class="paren">(</span><ul class="args-list inline commas">
                        <li class="arg">
                                <code>bodyA</code>
                        </li>
                        <li class="arg">
                                <code>bodyB</code>
                        </li>
                        <li class="arg">
                                <code>maxForce</code>
                        </li>
                    </ul><span class="paren">)</span>
                </div>
        
        
        
        
        
        
        
        
            <div class="meta">
                        <p>
                        Defined in
                <a href="../files/src_equations_RotationalMotorEquation.js.html#l7"><code>src&#x2F;equations&#x2F;RotationalMotorEquation.js:7</code></a>
                </p>
        
        
        
            </div>
        
            <div class="description">
                
            </div>
        
                <div class="params">
                    <h4>Parameters:</h4>
        
                    <ul class="params-list">
                        <li class="param">
                                <code class="param-name">bodyA</code>
                                <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>
        
        
                            <div class="param-description">
                                 
                            </div>
        
                        </li>
                        <li class="param">
                                <code class="param-name">bodyB</code>
                                <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>
        
        
                            <div class="param-description">
                                 
                            </div>
        
                        </li>
                        <li class="param">
                                <code class="param-name">maxForce</code>
                                <span class="type">Number</span>
        
        
                            <div class="param-description">
                                 
                            </div>
        
                        </li>
                    </ul>
                </div>
        
        
        
        </div>
    </div>

<div id="classdocs" class="tabview">
    <ul class="api-class-tabs">
        <li class="api-class-tab index"><a href="#index">Index</a></li>

            <li class="api-class-tab methods"><a href="#methods">Methods</a></li>
            <li class="api-class-tab properties"><a href="#properties">Properties</a></li>
    </ul>

    <div>
        <div id="index" class="api-class-tabpanel index">
            <h2 class="off-left">Item Index</h2>

                <div class="index-section methods">
                    <h3>Methods</h3>

                    <ul class="index-list methods extends">
                            <li class="index-item method inherited">
                                <a href="#method_addToWlambda">addToWlambda</a>

                            </li>
                            <li class="index-item method inherited">
                                <a href="#method_computeB">computeB</a>

                            </li>
                            <li class="index-item method inherited">
                                <a href="#method_computeGiMf">computeGiMf</a>

                            </li>
                            <li class="index-item method inherited">
                                <a href="#method_computeGiMGt">computeGiMGt</a>

                            </li>
                            <li class="index-item method inherited">
                                <a href="#method_computeGq">computeGq</a>

                            </li>
                            <li class="index-item method inherited">
                                <a href="#method_computeGW">computeGW</a>

                            </li>
                            <li class="index-item method inherited">
                                <a href="#method_computeGWlambda">computeGWlambda</a>

                            </li>
                            <li class="index-item method inherited">
                                <a href="#method_computeInvC">computeInvC</a>

                            </li>
                            <li class="index-item method inherited">
                                <a href="#method_setSpookParams">setSpookParams</a>

                            </li>
                    </ul>
                </div>

                <div class="index-section properties">
                    <h3>Properties</h3>

                    <ul class="index-list properties extends">
                            <li class="index-item property inherited">
                                <a href="#property_a">a</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_axisA">axisA</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_axisB">axisB</a>

                            </li>
                            <li class="index-item property inherited">
                                <a href="#property_b">b</a>

                            </li>
                            <li class="index-item property inherited">
                                <a href="#property_bi">bi</a>

                            </li>
                            <li class="index-item property inherited">
                                <a href="#property_bj">bj</a>

                            </li>
                            <li class="index-item property inherited">
                                <a href="#property_enabled">enabled</a>

                            </li>
                            <li class="index-item property inherited">
                                <a href="#property_eps">eps</a>

                            </li>
                            <li class="index-item property inherited">
                                <a href="#property_jacobianElementA">jacobianElementA</a>

                            </li>
                            <li class="index-item property inherited">
                                <a href="#property_jacobianElementB">jacobianElementB</a>

                            </li>
                            <li class="index-item property inherited">
                                <a href="#property_maxForce">maxForce</a>

                            </li>
                            <li class="index-item property inherited">
                                <a href="#property_minForce">minForce</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_targetVelocity">targetVelocity</a>

                            </li>
                    </ul>
                </div>


        </div>

            <div id="methods" class="api-class-tabpanel">
                <h2 class="off-left">Methods</h2>

<div id="method_addToWlambda" class="method item inherited">
    <h3 class="name"><code>addToWlambda</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>deltalambda</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>Inherited from
                <a href="../classes/Equation.html#method_addToWlambda">Equation</a>:
        <a href="../files/src_equations_Equation.js.html#l228"><code>src&#x2F;equations&#x2F;Equation.js:228</code></a>
        </p>



    </div>

    <div class="description">
        <p>Add constraint velocity to the bodies.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">deltalambda</code>
                        <span class="type">Number</span>


                    <div class="param-description">
                        
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_computeB" class="method item inherited">
    <h3 class="name"><code>computeB</code></h3>

        <span class="paren">()</span>

        <span class="returns-inline">
            <span class="type">Number</span>
        </span>







    <div class="meta">
                <p>Inherited from
                <a href="../classes/Equation.html#method_computeB">Equation</a>:
        <a href="../files/src_equations_Equation.js.html#l95"><code>src&#x2F;equations&#x2F;Equation.js:95</code></a>
        </p>



    </div>

    <div class="description">
        <p>Computes the RHS of the SPOOK equation</p>

    </div>


        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type">Number</span>:
            </div>
        </div>


</div>
<div id="method_computeGiMf" class="method item inherited">
    <h3 class="name"><code>computeGiMf</code></h3>

        <span class="paren">()</span>

        <span class="returns-inline">
            <span class="type">Number</span>
        </span>







    <div class="meta">
                <p>Inherited from
                <a href="../classes/Equation.html#method_computeGiMf">Equation</a>:
        <a href="../files/src_equations_Equation.js.html#l159"><code>src&#x2F;equations&#x2F;Equation.js:159</code></a>
        </p>



    </div>

    <div class="description">
        <p>Computes G<em>inv(M)</em>f, where M is the mass matrix with diagonal blocks for each body, and f are the forces on the bodies.</p>

    </div>


        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type">Number</span>:
            </div>
        </div>


</div>
<div id="method_computeGiMGt" class="method item inherited">
    <h3 class="name"><code>computeGiMGt</code></h3>

        <span class="paren">()</span>

        <span class="returns-inline">
            <span class="type">Number</span>
        </span>







    <div class="meta">
                <p>Inherited from
                <a href="../classes/Equation.html#method_computeGiMGt">Equation</a>:
        <a href="../files/src_equations_Equation.js.html#l191"><code>src&#x2F;equations&#x2F;Equation.js:191</code></a>
        </p>



    </div>

    <div class="description">
        <p>Computes G<em>inv(M)</em>G&#39;</p>

    </div>


        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type">Number</span>:
            </div>
        </div>


</div>
<div id="method_computeGq" class="method item inherited">
    <h3 class="name"><code>computeGq</code></h3>

        <span class="paren">()</span>

        <span class="returns-inline">
            <span class="type">Number</span>
        </span>







    <div class="meta">
                <p>Inherited from
                <a href="../classes/Equation.html#method_computeGq">Equation</a>:
        <a href="../files/src_equations_Equation.js.html#l107"><code>src&#x2F;equations&#x2F;Equation.js:107</code></a>
        </p>



    </div>

    <div class="description">
        <p>Computes G*q, where q are the generalized body coordinates</p>

    </div>


        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type">Number</span>:
            </div>
        </div>


</div>
<div id="method_computeGW" class="method item inherited">
    <h3 class="name"><code>computeGW</code></h3>

        <span class="paren">()</span>

        <span class="returns-inline">
            <span class="type">Number</span>
        </span>







    <div class="meta">
                <p>Inherited from
                <a href="../classes/Equation.html#method_computeGW">Equation</a>:
        <a href="../files/src_equations_Equation.js.html#l124"><code>src&#x2F;equations&#x2F;Equation.js:124</code></a>
        </p>



    </div>

    <div class="description">
        <p>Computes G*W, where W are the body velocities</p>

    </div>


        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type">Number</span>:
            </div>
        </div>


</div>
<div id="method_computeGWlambda" class="method item inherited">
    <h3 class="name"><code>computeGWlambda</code></h3>

        <span class="paren">()</span>

        <span class="returns-inline">
            <span class="type">Number</span>
        </span>







    <div class="meta">
                <p>Inherited from
                <a href="../classes/Equation.html#method_computeGWlambda">Equation</a>:
        <a href="../files/src_equations_Equation.js.html#l142"><code>src&#x2F;equations&#x2F;Equation.js:142</code></a>
        </p>



    </div>

    <div class="description">
        <p>Computes G*Wlambda, where W are the body velocities</p>

    </div>


        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type">Number</span>:
            </div>
        </div>


</div>
<div id="method_computeInvC" class="method item inherited">
    <h3 class="name"><code>computeInvC</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>eps</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type">Number</span>
        </span>







    <div class="meta">
                <p>Inherited from
                <a href="../classes/Equation.html#method_computeInvC">Equation</a>:
        <a href="../files/src_equations_Equation.js.html#l262"><code>src&#x2F;equations&#x2F;Equation.js:262</code></a>
        </p>



    </div>

    <div class="description">
        <p>Compute the denominator part of the SPOOK equation: C = G<em>inv(M)</em>G&#39; + eps</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">eps</code>
                        <span class="type">Number</span>


                    <div class="param-description">
                        
                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type">Number</span>:
            </div>
        </div>


</div>
<div id="method_setSpookParams" class="method item inherited">
    <h3 class="name"><code>setSpookParams</code></h3>

        <span class="paren">()</span>








    <div class="meta">
                <p>Inherited from
                <a href="../classes/Equation.html#method_setSpookParams">Equation</a>:
        <a href="../files/src_equations_Equation.js.html#l82"><code>src&#x2F;equations&#x2F;Equation.js:82</code></a>
        </p>



    </div>

    <div class="description">
        <p>Recalculates a,b,eps.</p>

    </div>




</div>
            </div>

            <div id="properties" class="api-class-tabpanel">
                <h2 class="off-left">Properties</h2>

                    <div id="property_a" class="property item inherited">
                        <h3 class="name"><code>a</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>Inherited from
                                    <a href="../classes/Equation.html#property_a">Equation</a>:
                            <a href="../files/src_equations_Equation.js.html#l41"><code>src&#x2F;equations&#x2F;Equation.js:41</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>SPOOK parameter</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_axisA" class="property item">
                        <h3 class="name"><code>axisA</code></h3>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_equations_RotationalMotorEquation.js.html#l21"><code>src&#x2F;equations&#x2F;RotationalMotorEquation.js:21</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>World oriented rotational axis</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_axisB" class="property item">
                        <h3 class="name"><code>axisB</code></h3>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_equations_RotationalMotorEquation.js.html#l27"><code>src&#x2F;equations&#x2F;RotationalMotorEquation.js:27</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>World oriented rotational axis</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_b" class="property item inherited">
                        <h3 class="name"><code>b</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>Inherited from
                                    <a href="../classes/Equation.html#property_b">Equation</a>:
                            <a href="../files/src_equations_Equation.js.html#l47"><code>src&#x2F;equations&#x2F;Equation.js:47</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>SPOOK parameter</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_bi" class="property item inherited">
                        <h3 class="name"><code>bi</code></h3>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>Inherited from
                                    <a href="../classes/Equation.html#property_bi">Equation</a>:
                            <a href="../files/src_equations_Equation.js.html#l29"><code>src&#x2F;equations&#x2F;Equation.js:29</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_bj" class="property item inherited">
                        <h3 class="name"><code>bj</code></h3>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>Inherited from
                                    <a href="../classes/Equation.html#property_bj">Equation</a>:
                            <a href="../files/src_equations_Equation.js.html#l35"><code>src&#x2F;equations&#x2F;Equation.js:35</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_enabled" class="property item inherited">
                        <h3 class="name"><code>enabled</code></h3>
                        <span class="type">Boolean</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>Inherited from
                                    <a href="../classes/Equation.html#property_enabled">Equation</a>:
                            <a href="../files/src_equations_Equation.js.html#l69"><code>src&#x2F;equations&#x2F;Equation.js:69</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                            <p><strong>Default:</strong> true</p>
                    
                    
                    </div>
                    <div id="property_eps" class="property item inherited">
                        <h3 class="name"><code>eps</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>Inherited from
                                    <a href="../classes/Equation.html#property_eps">Equation</a>:
                            <a href="../files/src_equations_Equation.js.html#l53"><code>src&#x2F;equations&#x2F;Equation.js:53</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>SPOOK parameter</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_jacobianElementA" class="property item inherited">
                        <h3 class="name"><code>jacobianElementA</code></h3>
                        <span class="type"><a href="../classes/JacobianElement.html" class="crosslink">JacobianElement</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>Inherited from
                                    <a href="../classes/Equation.html#property_jacobianElementA">Equation</a>:
                            <a href="../files/src_equations_Equation.js.html#l59"><code>src&#x2F;equations&#x2F;Equation.js:59</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_jacobianElementB" class="property item inherited">
                        <h3 class="name"><code>jacobianElementB</code></h3>
                        <span class="type"><a href="../classes/JacobianElement.html" class="crosslink">JacobianElement</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>Inherited from
                                    <a href="../classes/Equation.html#property_jacobianElementB">Equation</a>:
                            <a href="../files/src_equations_Equation.js.html#l64"><code>src&#x2F;equations&#x2F;Equation.js:64</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_maxForce" class="property item inherited">
                        <h3 class="name"><code>maxForce</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>Inherited from
                                    <a href="../classes/Equation.html#property_maxForce">Equation</a>:
                            <a href="../files/src_equations_Equation.js.html#l24"><code>src&#x2F;equations&#x2F;Equation.js:24</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_minForce" class="property item inherited">
                        <h3 class="name"><code>minForce</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>Inherited from
                                    <a href="../classes/Equation.html#property_minForce">Equation</a>:
                            <a href="../files/src_equations_Equation.js.html#l19"><code>src&#x2F;equations&#x2F;Equation.js:19</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_targetVelocity" class="property item">
                        <h3 class="name"><code>targetVelocity</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_equations_RotationalMotorEquation.js.html#l33"><code>src&#x2F;equations&#x2F;RotationalMotorEquation.js:33</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Motor velocity</p>
                    
                        </div>
                    
                    
                    
                    </div>
            </div>


    </div>
</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="../assets/vendor/prettify/prettify-min.js"></script>
<script>prettyPrint();</script>
<script src="../assets/js/yui-prettify.js"></script>
<script src="../assets/../api.js"></script>
<script src="../assets/js/api-filter.js"></script>
<script src="../assets/js/api-list.js"></script>
<script src="../assets/js/api-search.js"></script>
<script src="../assets/js/apidocs.js"></script>
</body>
</html>
