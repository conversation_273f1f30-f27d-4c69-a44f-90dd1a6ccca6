<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Mat3 - cannon</title>
    <link rel="stylesheet" href="http://yui.yahooapis.com/3.9.1/build/cssgrids/cssgrids-min.css">
    <link rel="stylesheet" href="../assets/vendor/prettify/prettify-min.css">
    <link rel="stylesheet" href="../assets/css/main.css" id="site_styles">
    <link rel="icon" href="../assets/favicon.ico">
    <script src="http://yui.yahooapis.com/combo?3.9.1/build/yui/yui-min.js"></script>
</head>
<body class="yui3-skin-sam">

<div id="doc">
    <div id="hd" class="yui3-g header">
        <div class="yui3-u-3-4">
                <h1><img src="../assets/css/logo.png" title="cannon" width="117" height="52"></h1>
        </div>
        <div class="yui3-u-1-4 version">
            <em>API Docs for: 0.6.1</em>
        </div>
    </div>
    <div id="bd" class="yui3-g">

        <div class="yui3-u-1-4">
            <div id="docs-sidebar" class="sidebar apidocs">
                <div id="api-list">
                    <h2 class="off-left">APIs</h2>
                    <div id="api-tabview" class="tabview">
                        <ul class="tabs">
                            <li><a href="#api-classes">Classes</a></li>
                            <li><a href="#api-modules">Modules</a></li>
                        </ul>
                
                        <div id="api-tabview-filter">
                            <input type="search" id="api-filter" placeholder="Type to filter APIs">
                        </div>
                
                        <div id="api-tabview-panel">
                            <ul id="api-classes" class="apis classes">
                                <li><a href="../classes/AABB.html">AABB</a></li>
                                <li><a href="../classes/ArrayCollisionMatrix.html">ArrayCollisionMatrix</a></li>
                                <li><a href="../classes/Body.html">Body</a></li>
                                <li><a href="../classes/Box.html">Box</a></li>
                                <li><a href="../classes/Broadphase.html">Broadphase</a></li>
                                <li><a href="../classes/ConeEquation.html">ConeEquation</a></li>
                                <li><a href="../classes/ConeTwistConstraint.html">ConeTwistConstraint</a></li>
                                <li><a href="../classes/Constraint.html">Constraint</a></li>
                                <li><a href="../classes/ContactEquation.html">ContactEquation</a></li>
                                <li><a href="../classes/ContactMaterial.html">ContactMaterial</a></li>
                                <li><a href="../classes/ConvexPolyhedron.html">ConvexPolyhedron</a></li>
                                <li><a href="../classes/Cylinder.html">Cylinder</a></li>
                                <li><a href="../classes/Demo.html">Demo</a></li>
                                <li><a href="../classes/DistanceConstraint.html">DistanceConstraint</a></li>
                                <li><a href="../classes/Equation.html">Equation</a></li>
                                <li><a href="../classes/EventTarget.html">EventTarget</a></li>
                                <li><a href="../classes/FrictionEquation.html">FrictionEquation</a></li>
                                <li><a href="../classes/GridBroadphase.html">GridBroadphase</a></li>
                                <li><a href="../classes/GSSolver.html">GSSolver</a></li>
                                <li><a href="../classes/Heightfield.html">Heightfield</a></li>
                                <li><a href="../classes/HingeConstraint.html">HingeConstraint</a></li>
                                <li><a href="../classes/JacobianElement.html">JacobianElement</a></li>
                                <li><a href="../classes/LockConstraint.html">LockConstraint</a></li>
                                <li><a href="../classes/Mat3.html">Mat3</a></li>
                                <li><a href="../classes/Material.html">Material</a></li>
                                <li><a href="../classes/NaiveBroadphase.html">NaiveBroadphase</a></li>
                                <li><a href="../classes/Narrowphase.html">Narrowphase</a></li>
                                <li><a href="../classes/ObjectCollisionMatrix.html">ObjectCollisionMatrix</a></li>
                                <li><a href="../classes/Octree.html">Octree</a></li>
                                <li><a href="../classes/OctreeNode.html">OctreeNode</a></li>
                                <li><a href="../classes/Particle.html">Particle</a></li>
                                <li><a href="../classes/Plane.html">Plane</a></li>
                                <li><a href="../classes/PointToPointConstraint.html">PointToPointConstraint</a></li>
                                <li><a href="../classes/Pool.html">Pool</a></li>
                                <li><a href="../classes/Quaternion.html">Quaternion</a></li>
                                <li><a href="../classes/Ray.html">Ray</a></li>
                                <li><a href="../classes/RaycastResult.html">RaycastResult</a></li>
                                <li><a href="../classes/RaycastVehicle.html">RaycastVehicle</a></li>
                                <li><a href="../classes/RigidVehicle.html">RigidVehicle</a></li>
                                <li><a href="../classes/RotationalEquation.html">RotationalEquation</a></li>
                                <li><a href="../classes/RotationalMotorEquation.html">RotationalMotorEquation</a></li>
                                <li><a href="../classes/SAPBroadphase.html">SAPBroadphase</a></li>
                                <li><a href="../classes/Shape.html">Shape</a></li>
                                <li><a href="../classes/Solver.html">Solver</a></li>
                                <li><a href="../classes/Sphere.html">Sphere</a></li>
                                <li><a href="../classes/SPHSystem.html">SPHSystem</a></li>
                                <li><a href="../classes/SplitSolver.html">SplitSolver</a></li>
                                <li><a href="../classes/Spring.html">Spring</a></li>
                                <li><a href="../classes/Transform.html">Transform</a></li>
                                <li><a href="../classes/Trimesh.html">Trimesh</a></li>
                                <li><a href="../classes/TupleDictionary.html">TupleDictionary</a></li>
                                <li><a href="../classes/Vec3.html">Vec3</a></li>
                                <li><a href="../classes/Vec3Pool.html">Vec3Pool</a></li>
                                <li><a href="../classes/WheelInfo.html">WheelInfo</a></li>
                                <li><a href="../classes/World.html">World</a></li>
                            </ul>
                
                            <ul id="api-modules" class="apis modules">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="yui3-u-3-4">
                <div id="api-options">
                    Show:
                    <label for="api-show-inherited">
                        <input type="checkbox" id="api-show-inherited" checked>
                        Inherited
                    </label>
            
                    <label for="api-show-protected">
                        <input type="checkbox" id="api-show-protected">
                        Protected
                    </label>
            
                    <label for="api-show-private">
                        <input type="checkbox" id="api-show-private">
                        Private
                    </label>
                    <label for="api-show-deprecated">
                        <input type="checkbox" id="api-show-deprecated">
                        Deprecated
                    </label>
            
                </div>
            
            <div class="apidocs">
                <div id="docs-main">
                    <div class="content">
<h1>Mat3 Class</h1>
<div class="box meta">


        <div class="foundat">
            Defined in: <a href="../files/src_math_Mat3.js.html#l5"><code>src&#x2F;math&#x2F;Mat3.js:5</code></a>
        </div>


</div>


<div class="box intro">
    <p>A 3x3 matrix.</p>

</div>

    <div class="constructor">
        <h2>Constructor</h2>
        <div id="method_Mat3" class="method item">
            <h3 class="name"><code>Mat3</code></h3>
        
                <div class="args">
                    <span class="paren">(</span><ul class="args-list inline commas">
                        <li class="arg">
                                <code>array</code>
                        </li>
                    </ul><span class="paren">)</span>
                </div>
        
        
        
        
        
        
        
        
            <div class="meta">
                        <p>
                        Defined in
                <a href="../files/src_math_Mat3.js.html#l5"><code>src&#x2F;math&#x2F;Mat3.js:5</code></a>
                </p>
        
        
        
            </div>
        
            <div class="description">
                
            </div>
        
                <div class="params">
                    <h4>Parameters:</h4>
        
                    <ul class="params-list">
                        <li class="param">
                                <code class="param-name">array</code>
                                <span class="type">Object</span>
        
        
                            <div class="param-description">
                                <p>elements Array of nine elements. Optional.</p>
        
                            </div>
        
                        </li>
                    </ul>
                </div>
        
        
        
        </div>
    </div>

<div id="classdocs" class="tabview">
    <ul class="api-class-tabs">
        <li class="api-class-tab index"><a href="#index">Index</a></li>

            <li class="api-class-tab methods"><a href="#methods">Methods</a></li>
            <li class="api-class-tab properties"><a href="#properties">Properties</a></li>
    </ul>

    <div>
        <div id="index" class="api-class-tabpanel index">
            <h2 class="off-left">Item Index</h2>

                <div class="index-section methods">
                    <h3>Methods</h3>

                    <ul class="index-list methods">
                            <li class="index-item method">
                                <a href="#method_copy">copy</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_e">e</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_getTrace">getTrace</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_identity">identity</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_mmult">mmult</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_reverse">reverse</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_scale">scale</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_setRotationFromQuaternion">setRotationFromQuaternion</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_setTrace">setTrace</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_setZero">setZero</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_smult">smult</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_solve">solve</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_toString">toString</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_transpose">transpose</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_vmult">vmult</a>

                            </li>
                    </ul>
                </div>

                <div class="index-section properties">
                    <h3>Properties</h3>

                    <ul class="index-list properties">
                            <li class="index-item property">
                                <a href="#property_elements">elements</a>

                            </li>
                    </ul>
                </div>


        </div>

            <div id="methods" class="api-class-tabpanel">
                <h2 class="off-left">Methods</h2>

<div id="method_copy" class="method item">
    <h3 class="name"><code>copy</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>source</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type"><a href="../classes/Mat3.html" class="crosslink">Mat3</a></span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Mat3.js.html#l244"><code>src&#x2F;math&#x2F;Mat3.js:244</code></a>
        </p>



    </div>

    <div class="description">
        <p>Copy another matrix into this matrix object.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">source</code>
                        <span class="type"><a href="../classes/Mat3.html" class="crosslink">Mat3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type"><a href="../classes/Mat3.html" class="crosslink">Mat3</a></span>:
                    <p>this</p>

            </div>
        </div>


</div>
<div id="method_e" class="method item">
    <h3 class="name"><code>e</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>row</code>
                </li>
                <li class="arg">
                        <code>column</code>
                </li>
                <li class="arg">
                        <code>value</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type">Number</span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Mat3.js.html#l227"><code>src&#x2F;math&#x2F;Mat3.js:227</code></a>
        </p>



    </div>

    <div class="description">
        <p>Get an element in the matrix by index. Index starts at 0, not 1!!!</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">row</code>
                        <span class="type">Number</span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">column</code>
                        <span class="type">Number</span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">value</code>
                        <span class="type">Number</span>


                    <div class="param-description">
                        <p>Optional. If provided, the matrix element will be set to this value.</p>

                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type">Number</span>:
            </div>
        </div>


</div>
<div id="method_getTrace" class="method item">
    <h3 class="name"><code>getTrace</code></h3>

        <span class="paren">()</span>

        <span class="returns-inline">
            <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Mat3.js.html#l74"><code>src&#x2F;math&#x2F;Mat3.js:74</code></a>
        </p>



    </div>

    <div class="description">
        <p>Gets the matrix diagonal elements</p>

    </div>


        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>:
            </div>
        </div>


</div>
<div id="method_identity" class="method item">
    <h3 class="name"><code>identity</code></h3>

        <span class="paren">()</span>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Mat3.js.html#l24"><code>src&#x2F;math&#x2F;Mat3.js:24</code></a>
        </p>



    </div>

    <div class="description">
        <p>Sets the matrix to identity</p>

    </div>




</div>
<div id="method_mmult" class="method item">
    <h3 class="name"><code>mmult</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>m</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type"><a href="../classes/Mat3.html" class="crosslink">Mat3</a></span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Mat3.js.html#l118"><code>src&#x2F;math&#x2F;Mat3.js:118</code></a>
        </p>



    </div>

    <div class="description">
        <p>Matrix multiplication</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">m</code>
                        <span class="type"><a href="../classes/Mat3.html" class="crosslink">Mat3</a></span>


                    <div class="param-description">
                        <p>Matrix to multiply with from left side.</p>

                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type"><a href="../classes/Mat3.html" class="crosslink">Mat3</a></span>:
                    <p>The result.</p>

            </div>
        </div>


</div>
<div id="method_reverse" class="method item">
    <h3 class="name"><code>reverse</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>target</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type"><a href="../classes/Mat3.html" class="crosslink">Mat3</a></span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Mat3.js.html#l271"><code>src&#x2F;math&#x2F;Mat3.js:271</code></a>
        </p>



    </div>

    <div class="description">
        <p>reverse the matrix</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">target</code>
                        <span class="type"><a href="../classes/Mat3.html" class="crosslink">Mat3</a></span>


                    <div class="param-description">
                        <p>Optional. Target matrix to save in.</p>

                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type"><a href="../classes/Mat3.html" class="crosslink">Mat3</a></span>:
                    <p>The solution x</p>

            </div>
        </div>


</div>
<div id="method_scale" class="method item">
    <h3 class="name"><code>scale</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>v</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type"><a href="../classes/Mat3.html" class="crosslink">Mat3</a></span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Mat3.js.html#l138"><code>src&#x2F;math&#x2F;Mat3.js:138</code></a>
        </p>



    </div>

    <div class="description">
        <p>Scale each column of the matrix</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">v</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type"><a href="../classes/Mat3.html" class="crosslink">Mat3</a></span>:
                    <p>The result.</p>

            </div>
        </div>


</div>
<div id="method_setRotationFromQuaternion" class="method item">
    <h3 class="name"><code>setRotationFromQuaternion</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>q</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Mat3.js.html#l375"><code>src&#x2F;math&#x2F;Mat3.js:375</code></a>
        </p>



    </div>

    <div class="description">
        <p>Set the matrix from a quaterion</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">q</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_setTrace" class="method item">
    <h3 class="name"><code>setTrace</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>vec3</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Mat3.js.html#l62"><code>src&#x2F;math&#x2F;Mat3.js:62</code></a>
        </p>



    </div>

    <div class="description">
        <p>Sets the matrix diagonal elements from a Vec3</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">vec3</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_setZero" class="method item">
    <h3 class="name"><code>setZero</code></h3>

        <span class="paren">()</span>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Mat3.js.html#l45"><code>src&#x2F;math&#x2F;Mat3.js:45</code></a>
        </p>



    </div>

    <div class="description">
        <p>Set all elements to zero</p>

    </div>




</div>
<div id="method_smult" class="method item">
    <h3 class="name"><code>smult</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>s</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Mat3.js.html#l107"><code>src&#x2F;math&#x2F;Mat3.js:107</code></a>
        </p>



    </div>

    <div class="description">
        <p>Matrix-scalar multiplication</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">s</code>
                        <span class="type">Number</span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_solve" class="method item">
    <h3 class="name"><code>solve</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>b</code>
                </li>
                <li class="arg">
                        <code>target</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Mat3.js.html#l156"><code>src&#x2F;math&#x2F;Mat3.js:156</code></a>
        </p>



    </div>

    <div class="description">
        <p>Solve Ax=b</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">b</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                        <p>The right hand side</p>

                    </div>

                </li>
                <li class="param">
                        <code class="param-name">target</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                        <p>Optional. Target vector to save in.</p>

                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>:
                    <p>The solution x</p>

            </div>
        </div>


</div>
<div id="method_toString" class="method item">
    <h3 class="name"><code>toString</code></h3>

        <span class="paren">()</span>

        <span class="returns-inline">
            <span class="type"></span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Mat3.js.html#l257"><code>src&#x2F;math&#x2F;Mat3.js:257</code></a>
        </p>



    </div>

    <div class="description">
        <p>Returns a string representation of the matrix.</p>

    </div>


        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                    <p>string</p>

            </div>
        </div>


</div>
<div id="method_transpose" class="method item">
    <h3 class="name"><code>transpose</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>target</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type"><a href="../classes/Mat3.html" class="crosslink">Mat3</a></span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Mat3.js.html#l403"><code>src&#x2F;math&#x2F;Mat3.js:403</code></a>
        </p>



    </div>

    <div class="description">
        <p>Transpose the matrix</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">target</code>
                        <span class="type"><a href="../classes/Mat3.html" class="crosslink">Mat3</a></span>


                    <div class="param-description">
                        <p>Where to store the result.</p>

                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type"><a href="../classes/Mat3.html" class="crosslink">Mat3</a></span>:
                    <p>The target Mat3, or a new Mat3 if target was omitted.</p>

            </div>
        </div>


</div>
<div id="method_vmult" class="method item">
    <h3 class="name"><code>vmult</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>v</code>
                </li>
                <li class="arg">
                        <code>target</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_math_Mat3.js.html#l87"><code>src&#x2F;math&#x2F;Mat3.js:87</code></a>
        </p>



    </div>

    <div class="description">
        <p>Matrix-Vector multiplication</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">v</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                        <p>The vector to multiply with</p>

                    </div>

                </li>
                <li class="param">
                        <code class="param-name">target</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                        <p>Optional, target to save the result in.</p>

                    </div>

                </li>
            </ul>
        </div>



</div>
            </div>

            <div id="properties" class="api-class-tabpanel">
                <h2 class="off-left">Properties</h2>

                    <div id="property_elements" class="property item">
                        <h3 class="name"><code>elements</code></h3>
                        <span class="type">Array</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_math_Mat3.js.html#l13"><code>src&#x2F;math&#x2F;Mat3.js:13</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>A vector of length 9, containing all matrix elements</p>
                    
                        </div>
                    
                    
                    
                    </div>
            </div>


    </div>
</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="../assets/vendor/prettify/prettify-min.js"></script>
<script>prettyPrint();</script>
<script src="../assets/js/yui-prettify.js"></script>
<script src="../assets/../api.js"></script>
<script src="../assets/js/api-filter.js"></script>
<script src="../assets/js/api-list.js"></script>
<script src="../assets/js/api-search.js"></script>
<script src="../assets/js/apidocs.js"></script>
</body>
</html>
