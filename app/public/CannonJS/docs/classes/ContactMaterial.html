<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>ContactMaterial - cannon</title>
    <link rel="stylesheet" href="http://yui.yahooapis.com/3.9.1/build/cssgrids/cssgrids-min.css">
    <link rel="stylesheet" href="../assets/vendor/prettify/prettify-min.css">
    <link rel="stylesheet" href="../assets/css/main.css" id="site_styles">
    <link rel="icon" href="../assets/favicon.ico">
    <script src="http://yui.yahooapis.com/combo?3.9.1/build/yui/yui-min.js"></script>
</head>
<body class="yui3-skin-sam">

<div id="doc">
    <div id="hd" class="yui3-g header">
        <div class="yui3-u-3-4">
                <h1><img src="../assets/css/logo.png" title="cannon" width="117" height="52"></h1>
        </div>
        <div class="yui3-u-1-4 version">
            <em>API Docs for: 0.6.1</em>
        </div>
    </div>
    <div id="bd" class="yui3-g">

        <div class="yui3-u-1-4">
            <div id="docs-sidebar" class="sidebar apidocs">
                <div id="api-list">
                    <h2 class="off-left">APIs</h2>
                    <div id="api-tabview" class="tabview">
                        <ul class="tabs">
                            <li><a href="#api-classes">Classes</a></li>
                            <li><a href="#api-modules">Modules</a></li>
                        </ul>
                
                        <div id="api-tabview-filter">
                            <input type="search" id="api-filter" placeholder="Type to filter APIs">
                        </div>
                
                        <div id="api-tabview-panel">
                            <ul id="api-classes" class="apis classes">
                                <li><a href="../classes/AABB.html">AABB</a></li>
                                <li><a href="../classes/ArrayCollisionMatrix.html">ArrayCollisionMatrix</a></li>
                                <li><a href="../classes/Body.html">Body</a></li>
                                <li><a href="../classes/Box.html">Box</a></li>
                                <li><a href="../classes/Broadphase.html">Broadphase</a></li>
                                <li><a href="../classes/ConeEquation.html">ConeEquation</a></li>
                                <li><a href="../classes/ConeTwistConstraint.html">ConeTwistConstraint</a></li>
                                <li><a href="../classes/Constraint.html">Constraint</a></li>
                                <li><a href="../classes/ContactEquation.html">ContactEquation</a></li>
                                <li><a href="../classes/ContactMaterial.html">ContactMaterial</a></li>
                                <li><a href="../classes/ConvexPolyhedron.html">ConvexPolyhedron</a></li>
                                <li><a href="../classes/Cylinder.html">Cylinder</a></li>
                                <li><a href="../classes/Demo.html">Demo</a></li>
                                <li><a href="../classes/DistanceConstraint.html">DistanceConstraint</a></li>
                                <li><a href="../classes/Equation.html">Equation</a></li>
                                <li><a href="../classes/EventTarget.html">EventTarget</a></li>
                                <li><a href="../classes/FrictionEquation.html">FrictionEquation</a></li>
                                <li><a href="../classes/GridBroadphase.html">GridBroadphase</a></li>
                                <li><a href="../classes/GSSolver.html">GSSolver</a></li>
                                <li><a href="../classes/Heightfield.html">Heightfield</a></li>
                                <li><a href="../classes/HingeConstraint.html">HingeConstraint</a></li>
                                <li><a href="../classes/JacobianElement.html">JacobianElement</a></li>
                                <li><a href="../classes/LockConstraint.html">LockConstraint</a></li>
                                <li><a href="../classes/Mat3.html">Mat3</a></li>
                                <li><a href="../classes/Material.html">Material</a></li>
                                <li><a href="../classes/NaiveBroadphase.html">NaiveBroadphase</a></li>
                                <li><a href="../classes/Narrowphase.html">Narrowphase</a></li>
                                <li><a href="../classes/ObjectCollisionMatrix.html">ObjectCollisionMatrix</a></li>
                                <li><a href="../classes/Octree.html">Octree</a></li>
                                <li><a href="../classes/OctreeNode.html">OctreeNode</a></li>
                                <li><a href="../classes/Particle.html">Particle</a></li>
                                <li><a href="../classes/Plane.html">Plane</a></li>
                                <li><a href="../classes/PointToPointConstraint.html">PointToPointConstraint</a></li>
                                <li><a href="../classes/Pool.html">Pool</a></li>
                                <li><a href="../classes/Quaternion.html">Quaternion</a></li>
                                <li><a href="../classes/Ray.html">Ray</a></li>
                                <li><a href="../classes/RaycastResult.html">RaycastResult</a></li>
                                <li><a href="../classes/RaycastVehicle.html">RaycastVehicle</a></li>
                                <li><a href="../classes/RigidVehicle.html">RigidVehicle</a></li>
                                <li><a href="../classes/RotationalEquation.html">RotationalEquation</a></li>
                                <li><a href="../classes/RotationalMotorEquation.html">RotationalMotorEquation</a></li>
                                <li><a href="../classes/SAPBroadphase.html">SAPBroadphase</a></li>
                                <li><a href="../classes/Shape.html">Shape</a></li>
                                <li><a href="../classes/Solver.html">Solver</a></li>
                                <li><a href="../classes/Sphere.html">Sphere</a></li>
                                <li><a href="../classes/SPHSystem.html">SPHSystem</a></li>
                                <li><a href="../classes/SplitSolver.html">SplitSolver</a></li>
                                <li><a href="../classes/Spring.html">Spring</a></li>
                                <li><a href="../classes/Transform.html">Transform</a></li>
                                <li><a href="../classes/Trimesh.html">Trimesh</a></li>
                                <li><a href="../classes/TupleDictionary.html">TupleDictionary</a></li>
                                <li><a href="../classes/Vec3.html">Vec3</a></li>
                                <li><a href="../classes/Vec3Pool.html">Vec3Pool</a></li>
                                <li><a href="../classes/WheelInfo.html">WheelInfo</a></li>
                                <li><a href="../classes/World.html">World</a></li>
                            </ul>
                
                            <ul id="api-modules" class="apis modules">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="yui3-u-3-4">
                <div id="api-options">
                    Show:
                    <label for="api-show-inherited">
                        <input type="checkbox" id="api-show-inherited" checked>
                        Inherited
                    </label>
            
                    <label for="api-show-protected">
                        <input type="checkbox" id="api-show-protected">
                        Protected
                    </label>
            
                    <label for="api-show-private">
                        <input type="checkbox" id="api-show-private">
                        Private
                    </label>
                    <label for="api-show-deprecated">
                        <input type="checkbox" id="api-show-deprecated">
                        Deprecated
                    </label>
            
                </div>
            
            <div class="apidocs">
                <div id="docs-main">
                    <div class="content">
<h1>ContactMaterial Class</h1>
<div class="box meta">


        <div class="foundat">
            Defined in: <a href="../files/src_material_ContactMaterial.js.html#l5"><code>src&#x2F;material&#x2F;ContactMaterial.js:5</code></a>
        </div>


</div>


<div class="box intro">
    <p>Defines what happens when two materials meet.</p>

</div>

    <div class="constructor">
        <h2>Constructor</h2>
        <div id="method_ContactMaterial" class="method item">
            <h3 class="name"><code>ContactMaterial</code></h3>
        
                <div class="args">
                    <span class="paren">(</span><ul class="args-list inline commas">
                        <li class="arg">
                                <code>m1</code>
                        </li>
                        <li class="arg">
                                <code>m2</code>
                        </li>
                        <li class="arg">
                                <code class="optional">[options]</code>
                        </li>
                    </ul><span class="paren">)</span>
                </div>
        
        
        
        
        
        
        
        
            <div class="meta">
                        <p>
                        Defined in
                <a href="../files/src_material_ContactMaterial.js.html#l5"><code>src&#x2F;material&#x2F;ContactMaterial.js:5</code></a>
                </p>
        
        
        
            </div>
        
            <div class="description">
                
            </div>
        
                <div class="params">
                    <h4>Parameters:</h4>
        
                    <ul class="params-list">
                        <li class="param">
                                <code class="param-name">m1</code>
                                <span class="type"><a href="../classes/Material.html" class="crosslink">Material</a></span>
        
        
                            <div class="param-description">
                                 
                            </div>
        
                        </li>
                        <li class="param">
                                <code class="param-name">m2</code>
                                <span class="type"><a href="../classes/Material.html" class="crosslink">Material</a></span>
        
        
                            <div class="param-description">
                                 
                            </div>
        
                        </li>
                        <li class="param">
                                <code class="param-name optional">[options]</code>
                                <span class="type">Object</span>
                                <span class="flag optional" title="This parameter is optional.">optional</span>
        
        
                            <div class="param-description">
                                 
                            </div>
        
                                <ul class="params-list">
                                    <li class="param">
                                            <code class="param-name optional">[friction=0.3]</code>
                                            <span class="type">Number</span>
                                            <span class="flag optional" title="This parameter is optional.">optional</span>
        
                                        <div class="param-description">
                                            
                                        </div>
        
                                    </li>
                                    <li class="param">
                                            <code class="param-name optional">[restitution=0.3]</code>
                                            <span class="type">Number</span>
                                            <span class="flag optional" title="This parameter is optional.">optional</span>
        
                                        <div class="param-description">
                                            
                                        </div>
        
                                    </li>
                                    <li class="param">
                                            <code class="param-name optional">[contactEquationStiffness=1e7]</code>
                                            <span class="type">Number</span>
                                            <span class="flag optional" title="This parameter is optional.">optional</span>
        
                                        <div class="param-description">
                                            
                                        </div>
        
                                    </li>
                                    <li class="param">
                                            <code class="param-name optional">[contactEquationRelaxation=3]</code>
                                            <span class="type">Number</span>
                                            <span class="flag optional" title="This parameter is optional.">optional</span>
        
                                        <div class="param-description">
                                            
                                        </div>
        
                                    </li>
                                    <li class="param">
                                            <code class="param-name optional">[frictionEquationStiffness=1e7]</code>
                                            <span class="type">Number</span>
                                            <span class="flag optional" title="This parameter is optional.">optional</span>
        
                                        <div class="param-description">
                                            
                                        </div>
        
                                    </li>
                                    <li class="param">
                                            <code class="param-name optional">[frictionEquationRelaxation=3]</code>
                                            <span class="type">Number</span>
                                            <span class="flag optional" title="This parameter is optional.">optional</span>
        
                                        <div class="param-description">
                                            
                                        </div>
        
                                    </li>
                                </ul>
                        </li>
                    </ul>
                </div>
        
        
        
        </div>
    </div>

<div id="classdocs" class="tabview">
    <ul class="api-class-tabs">
        <li class="api-class-tab index"><a href="#index">Index</a></li>

            <li class="api-class-tab properties"><a href="#properties">Properties</a></li>
    </ul>

    <div>
        <div id="index" class="api-class-tabpanel index">
            <h2 class="off-left">Item Index</h2>


                <div class="index-section properties">
                    <h3>Properties</h3>

                    <ul class="index-list properties">
                            <li class="index-item property">
                                <a href="#property_contactEquationRelaxation">contactEquationRelaxation</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_contactEquationStiffness">contactEquationStiffness</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_friction">friction</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_frictionEquationRelaxation">frictionEquationRelaxation</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_frictionEquationStiffness">frictionEquationStiffness</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_id">id</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_materials">materials</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_restitution">restitution</a>

                            </li>
                    </ul>
                </div>


        </div>


            <div id="properties" class="api-class-tabpanel">
                <h2 class="off-left">Properties</h2>

                    <div id="property_contactEquationRelaxation" class="property item">
                        <h3 class="name"><code>contactEquationRelaxation</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_material_ContactMaterial.js.html#l60"><code>src&#x2F;material&#x2F;ContactMaterial.js:60</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Relaxation time of the produced contact equations</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_contactEquationStiffness" class="property item">
                        <h3 class="name"><code>contactEquationStiffness</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_material_ContactMaterial.js.html#l54"><code>src&#x2F;material&#x2F;ContactMaterial.js:54</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Stiffness of the produced contact equations</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_friction" class="property item">
                        <h3 class="name"><code>friction</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_material_ContactMaterial.js.html#l42"><code>src&#x2F;material&#x2F;ContactMaterial.js:42</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Friction coefficient</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_frictionEquationRelaxation" class="property item">
                        <h3 class="name"><code>frictionEquationRelaxation</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_material_ContactMaterial.js.html#l72"><code>src&#x2F;material&#x2F;ContactMaterial.js:72</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Relaxation time of the produced friction equations</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_frictionEquationStiffness" class="property item">
                        <h3 class="name"><code>frictionEquationStiffness</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_material_ContactMaterial.js.html#l66"><code>src&#x2F;material&#x2F;ContactMaterial.js:66</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Stiffness of the produced friction equations</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_id" class="property item">
                        <h3 class="name"><code>id</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_material_ContactMaterial.js.html#l29"><code>src&#x2F;material&#x2F;ContactMaterial.js:29</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Identifier of this material</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_materials" class="property item">
                        <h3 class="name"><code>materials</code></h3>
                        <span class="type">Array</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_material_ContactMaterial.js.html#l35"><code>src&#x2F;material&#x2F;ContactMaterial.js:35</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Participating materials</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_restitution" class="property item">
                        <h3 class="name"><code>restitution</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_material_ContactMaterial.js.html#l48"><code>src&#x2F;material&#x2F;ContactMaterial.js:48</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Restitution coefficient</p>
                    
                        </div>
                    
                    
                    
                    </div>
            </div>


    </div>
</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="../assets/vendor/prettify/prettify-min.js"></script>
<script>prettyPrint();</script>
<script src="../assets/js/yui-prettify.js"></script>
<script src="../assets/../api.js"></script>
<script src="../assets/js/api-filter.js"></script>
<script src="../assets/js/api-list.js"></script>
<script src="../assets/js/api-search.js"></script>
<script src="../assets/js/apidocs.js"></script>
</body>
</html>
