<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Body - cannon</title>
    <link rel="stylesheet" href="http://yui.yahooapis.com/3.9.1/build/cssgrids/cssgrids-min.css">
    <link rel="stylesheet" href="../assets/vendor/prettify/prettify-min.css">
    <link rel="stylesheet" href="../assets/css/main.css" id="site_styles">
    <link rel="icon" href="../assets/favicon.ico">
    <script src="http://yui.yahooapis.com/combo?3.9.1/build/yui/yui-min.js"></script>
</head>
<body class="yui3-skin-sam">

<div id="doc">
    <div id="hd" class="yui3-g header">
        <div class="yui3-u-3-4">
                <h1><img src="../assets/css/logo.png" title="cannon" width="117" height="52"></h1>
        </div>
        <div class="yui3-u-1-4 version">
            <em>API Docs for: 0.6.1</em>
        </div>
    </div>
    <div id="bd" class="yui3-g">

        <div class="yui3-u-1-4">
            <div id="docs-sidebar" class="sidebar apidocs">
                <div id="api-list">
                    <h2 class="off-left">APIs</h2>
                    <div id="api-tabview" class="tabview">
                        <ul class="tabs">
                            <li><a href="#api-classes">Classes</a></li>
                            <li><a href="#api-modules">Modules</a></li>
                        </ul>
                
                        <div id="api-tabview-filter">
                            <input type="search" id="api-filter" placeholder="Type to filter APIs">
                        </div>
                
                        <div id="api-tabview-panel">
                            <ul id="api-classes" class="apis classes">
                                <li><a href="../classes/AABB.html">AABB</a></li>
                                <li><a href="../classes/ArrayCollisionMatrix.html">ArrayCollisionMatrix</a></li>
                                <li><a href="../classes/Body.html">Body</a></li>
                                <li><a href="../classes/Box.html">Box</a></li>
                                <li><a href="../classes/Broadphase.html">Broadphase</a></li>
                                <li><a href="../classes/ConeEquation.html">ConeEquation</a></li>
                                <li><a href="../classes/ConeTwistConstraint.html">ConeTwistConstraint</a></li>
                                <li><a href="../classes/Constraint.html">Constraint</a></li>
                                <li><a href="../classes/ContactEquation.html">ContactEquation</a></li>
                                <li><a href="../classes/ContactMaterial.html">ContactMaterial</a></li>
                                <li><a href="../classes/ConvexPolyhedron.html">ConvexPolyhedron</a></li>
                                <li><a href="../classes/Cylinder.html">Cylinder</a></li>
                                <li><a href="../classes/Demo.html">Demo</a></li>
                                <li><a href="../classes/DistanceConstraint.html">DistanceConstraint</a></li>
                                <li><a href="../classes/Equation.html">Equation</a></li>
                                <li><a href="../classes/EventTarget.html">EventTarget</a></li>
                                <li><a href="../classes/FrictionEquation.html">FrictionEquation</a></li>
                                <li><a href="../classes/GridBroadphase.html">GridBroadphase</a></li>
                                <li><a href="../classes/GSSolver.html">GSSolver</a></li>
                                <li><a href="../classes/Heightfield.html">Heightfield</a></li>
                                <li><a href="../classes/HingeConstraint.html">HingeConstraint</a></li>
                                <li><a href="../classes/JacobianElement.html">JacobianElement</a></li>
                                <li><a href="../classes/LockConstraint.html">LockConstraint</a></li>
                                <li><a href="../classes/Mat3.html">Mat3</a></li>
                                <li><a href="../classes/Material.html">Material</a></li>
                                <li><a href="../classes/NaiveBroadphase.html">NaiveBroadphase</a></li>
                                <li><a href="../classes/Narrowphase.html">Narrowphase</a></li>
                                <li><a href="../classes/ObjectCollisionMatrix.html">ObjectCollisionMatrix</a></li>
                                <li><a href="../classes/Octree.html">Octree</a></li>
                                <li><a href="../classes/OctreeNode.html">OctreeNode</a></li>
                                <li><a href="../classes/Particle.html">Particle</a></li>
                                <li><a href="../classes/Plane.html">Plane</a></li>
                                <li><a href="../classes/PointToPointConstraint.html">PointToPointConstraint</a></li>
                                <li><a href="../classes/Pool.html">Pool</a></li>
                                <li><a href="../classes/Quaternion.html">Quaternion</a></li>
                                <li><a href="../classes/Ray.html">Ray</a></li>
                                <li><a href="../classes/RaycastResult.html">RaycastResult</a></li>
                                <li><a href="../classes/RaycastVehicle.html">RaycastVehicle</a></li>
                                <li><a href="../classes/RigidVehicle.html">RigidVehicle</a></li>
                                <li><a href="../classes/RotationalEquation.html">RotationalEquation</a></li>
                                <li><a href="../classes/RotationalMotorEquation.html">RotationalMotorEquation</a></li>
                                <li><a href="../classes/SAPBroadphase.html">SAPBroadphase</a></li>
                                <li><a href="../classes/Shape.html">Shape</a></li>
                                <li><a href="../classes/Solver.html">Solver</a></li>
                                <li><a href="../classes/Sphere.html">Sphere</a></li>
                                <li><a href="../classes/SPHSystem.html">SPHSystem</a></li>
                                <li><a href="../classes/SplitSolver.html">SplitSolver</a></li>
                                <li><a href="../classes/Spring.html">Spring</a></li>
                                <li><a href="../classes/Transform.html">Transform</a></li>
                                <li><a href="../classes/Trimesh.html">Trimesh</a></li>
                                <li><a href="../classes/TupleDictionary.html">TupleDictionary</a></li>
                                <li><a href="../classes/Vec3.html">Vec3</a></li>
                                <li><a href="../classes/Vec3Pool.html">Vec3Pool</a></li>
                                <li><a href="../classes/WheelInfo.html">WheelInfo</a></li>
                                <li><a href="../classes/World.html">World</a></li>
                            </ul>
                
                            <ul id="api-modules" class="apis modules">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="yui3-u-3-4">
                <div id="api-options">
                    Show:
                    <label for="api-show-inherited">
                        <input type="checkbox" id="api-show-inherited" checked>
                        Inherited
                    </label>
            
                    <label for="api-show-protected">
                        <input type="checkbox" id="api-show-protected">
                        Protected
                    </label>
            
                    <label for="api-show-private">
                        <input type="checkbox" id="api-show-private">
                        Private
                    </label>
                    <label for="api-show-deprecated">
                        <input type="checkbox" id="api-show-deprecated">
                        Deprecated
                    </label>
            
                </div>
            
            <div class="apidocs">
                <div id="docs-main">
                    <div class="content">
<h1>Body Class</h1>
<div class="box meta">

        <div class="extends">
            Extends <a href="../classes/EventTarget.html" class="crosslink">EventTarget</a>
        </div>

        <div class="foundat">
            Defined in: <a href="../files/src_objects_Body.js.html#l12"><code>src&#x2F;objects&#x2F;Body.js:12</code></a>
        </div>


</div>


<div class="box intro">
    <p>Base class for all body types.</p>

</div>

    <div class="constructor">
        <h2>Constructor</h2>
        <div id="method_Body" class="method item">
            <h3 class="name"><code>Body</code></h3>
        
                <div class="args">
                    <span class="paren">(</span><ul class="args-list inline commas">
                        <li class="arg">
                                <code class="optional">[options]</code>
                        </li>
                    </ul><span class="paren">)</span>
                </div>
        
        
        
        
        
        
        
        
            <div class="meta">
                        <p>
                        Defined in
                <a href="../files/src_objects_Body.js.html#l12"><code>src&#x2F;objects&#x2F;Body.js:12</code></a>
                </p>
        
        
        
            </div>
        
            <div class="description">
                
            </div>
        
                <div class="params">
                    <h4>Parameters:</h4>
        
                    <ul class="params-list">
                        <li class="param">
                                <code class="param-name optional">[options]</code>
                                <span class="type">Object</span>
                                <span class="flag optional" title="This parameter is optional.">optional</span>
        
        
                            <div class="param-description">
                                 
                            </div>
        
                                <ul class="params-list">
                                    <li class="param">
                                            <code class="param-name optional">[position]</code>
                                            <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
                                            <span class="flag optional" title="This parameter is optional.">optional</span>
        
                                        <div class="param-description">
                                            
                                        </div>
        
                                    </li>
                                    <li class="param">
                                            <code class="param-name optional">[velocity]</code>
                                            <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
                                            <span class="flag optional" title="This parameter is optional.">optional</span>
        
                                        <div class="param-description">
                                            
                                        </div>
        
                                    </li>
                                    <li class="param">
                                            <code class="param-name optional">[angularVelocity]</code>
                                            <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
                                            <span class="flag optional" title="This parameter is optional.">optional</span>
        
                                        <div class="param-description">
                                            
                                        </div>
        
                                    </li>
                                    <li class="param">
                                            <code class="param-name optional">[quaternion]</code>
                                            <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>
                                            <span class="flag optional" title="This parameter is optional.">optional</span>
        
                                        <div class="param-description">
                                            
                                        </div>
        
                                    </li>
                                    <li class="param">
                                            <code class="param-name optional">[mass]</code>
                                            <span class="type">Number</span>
                                            <span class="flag optional" title="This parameter is optional.">optional</span>
        
                                        <div class="param-description">
                                            
                                        </div>
        
                                    </li>
                                    <li class="param">
                                            <code class="param-name optional">[material]</code>
                                            <span class="type"><a href="../classes/Material.html" class="crosslink">Material</a></span>
                                            <span class="flag optional" title="This parameter is optional.">optional</span>
        
                                        <div class="param-description">
                                            
                                        </div>
        
                                    </li>
                                    <li class="param">
                                            <code class="param-name optional">[type]</code>
                                            <span class="type">Number</span>
                                            <span class="flag optional" title="This parameter is optional.">optional</span>
        
                                        <div class="param-description">
                                            
                                        </div>
        
                                    </li>
                                    <li class="param">
                                            <code class="param-name optional">[linearDamping=0.01]</code>
                                            <span class="type">Number</span>
                                            <span class="flag optional" title="This parameter is optional.">optional</span>
        
                                        <div class="param-description">
                                            
                                        </div>
        
                                    </li>
                                    <li class="param">
                                            <code class="param-name optional">[angularDamping=0.01]</code>
                                            <span class="type">Number</span>
                                            <span class="flag optional" title="This parameter is optional.">optional</span>
        
                                        <div class="param-description">
                                            
                                        </div>
        
                                    </li>
                                    <li class="param">
                                            <code class="param-name optional">[allowSleep=true]</code>
                                            <span class="type">Boolean</span>
                                            <span class="flag optional" title="This parameter is optional.">optional</span>
        
                                        <div class="param-description">
                                            
                                        </div>
        
                                    </li>
                                    <li class="param">
                                            <code class="param-name optional">[sleepSpeedLimit=0.1]</code>
                                            <span class="type">Number</span>
                                            <span class="flag optional" title="This parameter is optional.">optional</span>
        
                                        <div class="param-description">
                                            
                                        </div>
        
                                    </li>
                                    <li class="param">
                                            <code class="param-name optional">[sleepTimeLimit=1]</code>
                                            <span class="type">Number</span>
                                            <span class="flag optional" title="This parameter is optional.">optional</span>
        
                                        <div class="param-description">
                                            
                                        </div>
        
                                    </li>
                                    <li class="param">
                                            <code class="param-name optional">[collisionFilterGroup=1]</code>
                                            <span class="type">Number</span>
                                            <span class="flag optional" title="This parameter is optional.">optional</span>
        
                                        <div class="param-description">
                                            
                                        </div>
        
                                    </li>
                                    <li class="param">
                                            <code class="param-name optional">[collisionFilterMask=1]</code>
                                            <span class="type">Number</span>
                                            <span class="flag optional" title="This parameter is optional.">optional</span>
        
                                        <div class="param-description">
                                            
                                        </div>
        
                                    </li>
                                    <li class="param">
                                            <code class="param-name optional">[fixedRotation=false]</code>
                                            <span class="type">Boolean</span>
                                            <span class="flag optional" title="This parameter is optional.">optional</span>
        
                                        <div class="param-description">
                                            
                                        </div>
        
                                    </li>
                                    <li class="param">
                                            <code class="param-name optional">[shape]</code>
                                            <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>
                                            <span class="flag optional" title="This parameter is optional.">optional</span>
        
                                        <div class="param-description">
                                            
                                        </div>
        
                                    </li>
                                </ul>
                        </li>
                    </ul>
                </div>
        
        
        
                <div class="example">
                    <h4>Example:</h4>
        
                    <div class="example-content">
                        <pre class="code prettyprint"><code>var body = new Body({
            mass: 1
        });
        var shape = new Sphere(1);
        body.addShape(shape);
        world.add(body);
        </code></pre>
                    </div>
                </div>
        </div>
    </div>

<div id="classdocs" class="tabview">
    <ul class="api-class-tabs">
        <li class="api-class-tab index"><a href="#index">Index</a></li>

            <li class="api-class-tab methods"><a href="#methods">Methods</a></li>
            <li class="api-class-tab properties"><a href="#properties">Properties</a></li>
    </ul>

    <div>
        <div id="index" class="api-class-tabpanel index">
            <h2 class="off-left">Item Index</h2>

                <div class="index-section methods">
                    <h3>Methods</h3>

                    <ul class="index-list methods extends">
                            <li class="index-item method inherited">
                                <a href="#method_addEventListener">addEventListener</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_addShape">addShape</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_applyForce">applyForce</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_applyImpulse">applyImpulse</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_applyLocalForce">applyLocalForce</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_applyLocalImpulse">applyLocalImpulse</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_computeAABB">computeAABB</a>

                            </li>
                            <li class="index-item method inherited">
                                <a href="#method_dispatchEvent">dispatchEvent</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_getVelocityAtWorldPoint">getVelocityAtWorldPoint</a>

                            </li>
                            <li class="index-item method inherited">
                                <a href="#method_hasEventListener">hasEventListener</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_pointToLocalFrame">pointToLocalFrame</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_pointToWorldFrame">pointToWorldFrame</a>

                            </li>
                            <li class="index-item method inherited">
                                <a href="#method_removeEventListener">removeEventListener</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_sleep">sleep</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_sleepTick">sleepTick</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_updateBoundingRadius">updateBoundingRadius</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_updateInertiaWorld">updateInertiaWorld</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_updateMassProperties">updateMassProperties</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_updateSolveMassProperties">updateSolveMassProperties</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_vectorToLocalFrame">vectorToLocalFrame</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_vectorToWorldFrame">vectorToWorldFrame</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_wakeUp">wakeUp</a>

                            </li>
                    </ul>
                </div>

                <div class="index-section properties">
                    <h3>Properties</h3>

                    <ul class="index-list properties extends">
                            <li class="index-item property">
                                <a href="#property_aabb">aabb</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_aabbNeedsUpdate">aabbNeedsUpdate</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_allowSleep">allowSleep</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_angularDamping">angularDamping</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_angularVelocity">angularVelocity</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_AWAKE">AWAKE</a>

                                    <span class="flag static">static</span>
                            </li>
                            <li class="index-item property">
                                <a href="#property_collisionFilterGroup">collisionFilterGroup</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_collisionFilterMask">collisionFilterMask</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_collisionResponse">collisionResponse</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_DYNAMIC">DYNAMIC</a>

                                    <span class="flag static">static</span>
                            </li>
                            <li class="index-item property">
                                <a href="#property_fixedRotation">fixedRotation</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_force">force</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_inertia">inertia</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_initAngularVelocity">initAngularVelocity</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_initPosition">initPosition</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_initQuaternion">initQuaternion</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_initVelocity">initVelocity</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_invInertia">invInertia</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_invInertiaSolve">invInertiaSolve</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_invInertiaWorld">invInertiaWorld</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_invInertiaWorldSolve">invInertiaWorldSolve</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_invMass">invMass</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_KINEMATIC">KINEMATIC</a>

                                    <span class="flag static">static</span>
                            </li>
                            <li class="index-item property">
                                <a href="#property_linearDamping">linearDamping</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_mass">mass</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_material">material</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_position">position</a>

                            </li>
                            <li class="index-item property deprecated">
                                <a href="#property_postStep">postStep</a>

                                    <span class="flag deprecated">deprecated</span>
                            </li>
                            <li class="index-item property deprecated">
                                <a href="#property_preStep">preStep</a>

                                    <span class="flag deprecated">deprecated</span>
                            </li>
                            <li class="index-item property">
                                <a href="#property_previousPosition">previousPosition</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_quaternion">quaternion</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_shapeOffsets">shapeOffsets</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_shapeOrientations">shapeOrientations</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_shapes">shapes</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_SLEEPING">SLEEPING</a>

                                    <span class="flag static">static</span>
                            </li>
                            <li class="index-item property">
                                <a href="#property_sleepSpeedLimit">sleepSpeedLimit</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_sleepState">sleepState</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_sleepTimeLimit">sleepTimeLimit</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_SLEEPY">SLEEPY</a>

                                    <span class="flag static">static</span>
                            </li>
                            <li class="index-item property">
                                <a href="#property_STATIC">STATIC</a>

                                    <span class="flag static">static</span>
                            </li>
                            <li class="index-item property">
                                <a href="#property_torque">torque</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_type">type</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_velocity">velocity</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_world">world</a>

                            </li>
                    </ul>
                </div>


        </div>

            <div id="methods" class="api-class-tabpanel">
                <h2 class="off-left">Methods</h2>

<div id="method_addEventListener" class="method item inherited">
    <h3 class="name"><code>addEventListener</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>type</code>
                </li>
                <li class="arg">
                        <code>listener</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type"><a href="../classes/EventTarget.html" class="crosslink">EventTarget</a></span>
        </span>







    <div class="meta">
                <p>Inherited from
                <a href="../classes/EventTarget.html#method_addEventListener">EventTarget</a>:
        <a href="../files/src_utils_EventTarget.js.html#l15"><code>src&#x2F;utils&#x2F;EventTarget.js:15</code></a>
        </p>



    </div>

    <div class="description">
        <p>Add an event listener</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">type</code>
                        <span class="type">String</span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">listener</code>
                        <span class="type">Function</span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type"><a href="../classes/EventTarget.html" class="crosslink">EventTarget</a></span>:
                    <p>The self object, for chainability.</p>

            </div>
        </div>


</div>
<div id="method_addShape" class="method item">
    <h3 class="name"><code>addShape</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>shape</code>
                </li>
                <li class="arg">
                        <code>offset</code>
                </li>
                <li class="arg">
                        <code>quaternion</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_objects_Body.js.html#l507"><code>src&#x2F;objects&#x2F;Body.js:507</code></a>
        </p>



    </div>

    <div class="description">
        <p>Add a shape to the body with a local offset and orientation.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">shape</code>
                        <span class="type"><a href="../classes/Shape.html" class="crosslink">Shape</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">offset</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">quaternion</code>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>:
                    <p>The body object, for chainability.</p>

            </div>
        </div>


</div>
<div id="method_applyForce" class="method item">
    <h3 class="name"><code>applyForce</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>force</code>
                </li>
                <li class="arg">
                        <code>worldPoint</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_objects_Body.js.html#l637"><code>src&#x2F;objects&#x2F;Body.js:637</code></a>
        </p>



    </div>

    <div class="description">
        <p>Apply force to a world point. This could for example be a point on the Body surface. Applying force this way will add to Body.force and Body.torque.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">force</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                        <p>The amount of force to add.</p>

                    </div>

                </li>
                <li class="param">
                        <code class="param-name">worldPoint</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                        <p>A world point to apply the force on.</p>

                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_applyImpulse" class="method item">
    <h3 class="name"><code>applyImpulse</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>impulse</code>
                </li>
                <li class="arg">
                        <code>worldPoint</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_objects_Body.js.html#l688"><code>src&#x2F;objects&#x2F;Body.js:688</code></a>
        </p>



    </div>

    <div class="description">
        <p>Apply impulse to a world point. This could for example be a point on the Body surface. An impulse is a force added to a body during a short period of time (impulse = force * time). Impulses will be added to Body.velocity and Body.angularVelocity.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">impulse</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                        <p>The amount of impulse to add.</p>

                    </div>

                </li>
                <li class="param">
                        <code class="param-name">worldPoint</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                        <p>A world point to apply the force on.</p>

                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_applyLocalForce" class="method item">
    <h3 class="name"><code>applyLocalForce</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>force</code>
                </li>
                <li class="arg">
                        <code>localPoint</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_objects_Body.js.html#l665"><code>src&#x2F;objects&#x2F;Body.js:665</code></a>
        </p>



    </div>

    <div class="description">
        <p>Apply force to a local point in the body.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">force</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                        <p>The force vector to apply, defined locally in the body frame.</p>

                    </div>

                </li>
                <li class="param">
                        <code class="param-name">localPoint</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                        <p>A local point in the body to apply the force on.</p>

                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_applyLocalImpulse" class="method item">
    <h3 class="name"><code>applyLocalImpulse</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>force</code>
                </li>
                <li class="arg">
                        <code>localPoint</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_objects_Body.js.html#l729"><code>src&#x2F;objects&#x2F;Body.js:729</code></a>
        </p>



    </div>

    <div class="description">
        <p>Apply locally-defined impulse to a local point in the body.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">force</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                        <p>The force vector to apply, defined locally in the body frame.</p>

                    </div>

                </li>
                <li class="param">
                        <code class="param-name">localPoint</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                        <p>A local point in the body to apply the force on.</p>

                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_computeAABB" class="method item">
    <h3 class="name"><code>computeAABB</code></h3>

        <span class="paren">()</span>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_objects_Body.js.html#l562"><code>src&#x2F;objects&#x2F;Body.js:562</code></a>
        </p>



    </div>

    <div class="description">
        <p>Updates the .aabb</p>

    </div>




</div>
<div id="method_dispatchEvent" class="method item inherited">
    <h3 class="name"><code>dispatchEvent</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>event</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type"><a href="../classes/EventTarget.html" class="crosslink">EventTarget</a></span>
        </span>







    <div class="meta">
                <p>Inherited from
                <a href="../classes/EventTarget.html#method_dispatchEvent">EventTarget</a>:
        <a href="../files/src_utils_EventTarget.js.html#l68"><code>src&#x2F;utils&#x2F;EventTarget.js:68</code></a>
        </p>



    </div>

    <div class="description">
        <p>Emit an event.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">event</code>
                        <span class="type">Object</span>


                    <div class="param-description">
                         
                    </div>

                        <ul class="params-list">
                            <li class="param">
                                    <code class="param-name">type</code>
                                    <span class="type">String</span>

                                <div class="param-description">
                                    
                                </div>

                            </li>
                        </ul>
                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type"><a href="../classes/EventTarget.html" class="crosslink">EventTarget</a></span>:
                    <p>The self object, for chainability.</p>

            </div>
        </div>


</div>
<div id="method_getVelocityAtWorldPoint" class="method item">
    <h3 class="name"><code>getVelocityAtWorldPoint</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>worldPoint</code>
                </li>
                <li class="arg">
                        <code>result</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_objects_Body.js.html#l782"><code>src&#x2F;objects&#x2F;Body.js:782</code></a>
        </p>



    </div>

    <div class="description">
        <p>Get world velocity of a point in the body.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">worldPoint</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">result</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>:
                    <p>The result vector.</p>

            </div>
        </div>


</div>
<div id="method_hasEventListener" class="method item inherited">
    <h3 class="name"><code>hasEventListener</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>type</code>
                </li>
                <li class="arg">
                        <code>listener</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type">Boolean</span>
        </span>







    <div class="meta">
                <p>Inherited from
                <a href="../classes/EventTarget.html#method_hasEventListener">EventTarget</a>:
        <a href="../files/src_utils_EventTarget.js.html#l34"><code>src&#x2F;utils&#x2F;EventTarget.js:34</code></a>
        </p>



    </div>

    <div class="description">
        <p>Check if an event listener is added</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">type</code>
                        <span class="type">String</span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">listener</code>
                        <span class="type">Function</span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type">Boolean</span>:
            </div>
        </div>


</div>
<div id="method_pointToLocalFrame" class="method item">
    <h3 class="name"><code>pointToLocalFrame</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>worldPoint</code>
                </li>
                <li class="arg">
                        <code>result</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_objects_Body.js.html#l450"><code>src&#x2F;objects&#x2F;Body.js:450</code></a>
        </p>



    </div>

    <div class="description">
        <p>Convert a world point to local body frame.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">worldPoint</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">result</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>:
            </div>
        </div>


</div>
<div id="method_pointToWorldFrame" class="method item">
    <h3 class="name"><code>pointToWorldFrame</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>localPoint</code>
                </li>
                <li class="arg">
                        <code>result</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_objects_Body.js.html#l477"><code>src&#x2F;objects&#x2F;Body.js:477</code></a>
        </p>



    </div>

    <div class="description">
        <p>Convert a local body point to world frame.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">localPoint</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">result</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>:
            </div>
        </div>


</div>
<div id="method_removeEventListener" class="method item inherited">
    <h3 class="name"><code>removeEventListener</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>type</code>
                </li>
                <li class="arg">
                        <code>listener</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type"><a href="../classes/EventTarget.html" class="crosslink">EventTarget</a></span>
        </span>







    <div class="meta">
                <p>Inherited from
                <a href="../classes/EventTarget.html#method_removeEventListener">EventTarget</a>:
        <a href="../files/src_utils_EventTarget.js.html#l50"><code>src&#x2F;utils&#x2F;EventTarget.js:50</code></a>
        </p>



    </div>

    <div class="description">
        <p>Remove an event listener</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">type</code>
                        <span class="type">String</span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">listener</code>
                        <span class="type">Function</span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type"><a href="../classes/EventTarget.html" class="crosslink">EventTarget</a></span>:
                    <p>The self object, for chainability.</p>

            </div>
        </div>


</div>
<div id="method_sleep" class="method item">
    <h3 class="name"><code>sleep</code></h3>

        <span class="paren">()</span>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_objects_Body.js.html#l393"><code>src&#x2F;objects&#x2F;Body.js:393</code></a>
        </p>



    </div>

    <div class="description">
        <p>Force body sleep</p>

    </div>




</div>
<div id="method_sleepTick" class="method item">
    <h3 class="name"><code>sleepTick</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>time</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_objects_Body.js.html#l411"><code>src&#x2F;objects&#x2F;Body.js:411</code></a>
        </p>



    </div>

    <div class="description">
        <p>Called every timestep to update internal sleep timer and change sleep state if needed.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">time</code>
                        <span class="type">Number</span>


                    <div class="param-description">
                        <p>The world time in seconds</p>

                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_updateBoundingRadius" class="method item">
    <h3 class="name"><code>updateBoundingRadius</code></h3>

        <span class="paren">()</span>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_objects_Body.js.html#l537"><code>src&#x2F;objects&#x2F;Body.js:537</code></a>
        </p>



    </div>

    <div class="description">
        <p>Update the bounding radius of the body. Should be done if any of the shapes are changed.</p>

    </div>




</div>
<div id="method_updateInertiaWorld" class="method item">
    <h3 class="name"><code>updateInertiaWorld</code></h3>

        <span class="paren">()</span>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_objects_Body.js.html#l608"><code>src&#x2F;objects&#x2F;Body.js:608</code></a>
        </p>



    </div>

    <div class="description">
        <p>Update .inertiaWorld and .invInertiaWorld</p>

    </div>




</div>
<div id="method_updateMassProperties" class="method item">
    <h3 class="name"><code>updateMassProperties</code></h3>

        <span class="paren">()</span>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_objects_Body.js.html#l754"><code>src&#x2F;objects&#x2F;Body.js:754</code></a>
        </p>



    </div>

    <div class="description">
        <p>Should be called whenever you change the body shape or mass.</p>

    </div>




</div>
<div id="method_updateSolveMassProperties" class="method item">
    <h3 class="name"><code>updateSolveMassProperties</code></h3>

        <span class="paren">()</span>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_objects_Body.js.html#l434"><code>src&#x2F;objects&#x2F;Body.js:434</code></a>
        </p>



    </div>

    <div class="description">
        <p>If the body is sleeping, it should be immovable / have infinite mass during solve. We solve it by having a separate &quot;solve mass&quot;.</p>

    </div>




</div>
<div id="method_vectorToLocalFrame" class="method item">
    <h3 class="name"><code>vectorToLocalFrame</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>worldPoint</code>
                </li>
                <li class="arg">
                        <code>result</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_objects_Body.js.html#l464"><code>src&#x2F;objects&#x2F;Body.js:464</code></a>
        </p>



    </div>

    <div class="description">
        <p>Convert a world vector to local body frame.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">worldPoint</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">result</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>:
            </div>
        </div>


</div>
<div id="method_vectorToWorldFrame" class="method item">
    <h3 class="name"><code>vectorToWorldFrame</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>localVector</code>
                </li>
                <li class="arg">
                        <code>result</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
        </span>







    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_objects_Body.js.html#l491"><code>src&#x2F;objects&#x2F;Body.js:491</code></a>
        </p>



    </div>

    <div class="description">
        <p>Convert a local body point to world frame.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">localVector</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">result</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>:
            </div>
        </div>


</div>
<div id="method_wakeUp" class="method item">
    <h3 class="name"><code>wakeUp</code></h3>

        <span class="paren">()</span>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_objects_Body.js.html#l381"><code>src&#x2F;objects&#x2F;Body.js:381</code></a>
        </p>



    </div>

    <div class="description">
        <p>Wake the body up.</p>

    </div>




</div>
            </div>

            <div id="properties" class="api-class-tabpanel">
                <h2 class="off-left">Properties</h2>

                    <div id="property_aabb" class="property item">
                        <h3 class="name"><code>aabb</code></h3>
                        <span class="type"><a href="../classes/AABB.html" class="crosslink">AABB</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Body.js.html#l308"><code>src&#x2F;objects&#x2F;Body.js:308</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_aabbNeedsUpdate" class="property item">
                        <h3 class="name"><code>aabbNeedsUpdate</code></h3>
                        <span class="type">Boolean</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Body.js.html#l314"><code>src&#x2F;objects&#x2F;Body.js:314</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Indicates if the AABB needs to be updated before use.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_allowSleep" class="property item">
                        <h3 class="name"><code>allowSleep</code></h3>
                        <span class="type">Boolean</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Body.js.html#l172"><code>src&#x2F;objects&#x2F;Body.js:172</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>If true, the body will automatically fall to sleep.</p>
                    
                        </div>
                    
                            <p><strong>Default:</strong> true</p>
                    
                    
                    </div>
                    <div id="property_angularDamping" class="property item">
                        <h3 class="name"><code>angularDamping</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Body.js.html#l303"><code>src&#x2F;objects&#x2F;Body.js:303</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_angularVelocity" class="property item">
                        <h3 class="name"><code>angularVelocity</code></h3>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Body.js.html#l231"><code>src&#x2F;objects&#x2F;Body.js:231</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_AWAKE" class="property item">
                        <h3 class="name"><code>AWAKE</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                            <span class="flag static">static</span>
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Body.js.html#l358"><code>src&#x2F;objects&#x2F;Body.js:358</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_collisionFilterGroup" class="property item">
                        <h3 class="name"><code>collisionFilterGroup</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Body.js.html#l74"><code>src&#x2F;objects&#x2F;Body.js:74</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_collisionFilterMask" class="property item">
                        <h3 class="name"><code>collisionFilterMask</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Body.js.html#l79"><code>src&#x2F;objects&#x2F;Body.js:79</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_collisionResponse" class="property item">
                        <h3 class="name"><code>collisionResponse</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Body.js.html#l84"><code>src&#x2F;objects&#x2F;Body.js:84</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Whether to produce contact forces when in contact with other bodies. Note that contacts will be generated, but they will be disabled.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_DYNAMIC" class="property item">
                        <h3 class="name"><code>DYNAMIC</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                            <span class="flag static">static</span>
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Body.js.html#l332"><code>src&#x2F;objects&#x2F;Body.js:332</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>A dynamic body is fully simulated. Can be moved manually by the user, but normally they move according to forces. A dynamic body can collide with all body types. A dynamic body always has finite, non-zero mass.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_fixedRotation" class="property item">
                        <h3 class="name"><code>fixedRotation</code></h3>
                        <span class="type">Boolean</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Body.js.html#l296"><code>src&#x2F;objects&#x2F;Body.js:296</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Set to true if you don&#39;t want the body to rotate. Make sure to run .updateMassProperties() after changing this.</p>
                    
                        </div>
                    
                            <p><strong>Default:</strong> false</p>
                    
                    
                    </div>
                    <div id="property_force" class="property item">
                        <h3 class="name"><code>force</code></h3>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Body.js.html#l128"><code>src&#x2F;objects&#x2F;Body.js:128</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Linear force on the body</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_inertia" class="property item">
                        <h3 class="name"><code>inertia</code></h3>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Body.js.html#l268"><code>src&#x2F;objects&#x2F;Body.js:268</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_initAngularVelocity" class="property item">
                        <h3 class="name"><code>initAngularVelocity</code></h3>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Body.js.html#l241"><code>src&#x2F;objects&#x2F;Body.js:241</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_initPosition" class="property item">
                        <h3 class="name"><code>initPosition</code></h3>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Body.js.html#l105"><code>src&#x2F;objects&#x2F;Body.js:105</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Initial position of the body</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_initQuaternion" class="property item">
                        <h3 class="name"><code>initQuaternion</code></h3>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Body.js.html#l225"><code>src&#x2F;objects&#x2F;Body.js:225</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_initVelocity" class="property item">
                        <h3 class="name"><code>initVelocity</code></h3>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Body.js.html#l122"><code>src&#x2F;objects&#x2F;Body.js:122</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_invInertia" class="property item">
                        <h3 class="name"><code>invInertia</code></h3>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Body.js.html#l274"><code>src&#x2F;objects&#x2F;Body.js:274</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_invInertiaSolve" class="property item">
                        <h3 class="name"><code>invInertiaSolve</code></h3>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Body.js.html#l286"><code>src&#x2F;objects&#x2F;Body.js:286</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_invInertiaWorld" class="property item">
                        <h3 class="name"><code>invInertiaWorld</code></h3>
                        <span class="type"><a href="../classes/Mat3.html" class="crosslink">Mat3</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Body.js.html#l279"><code>src&#x2F;objects&#x2F;Body.js:279</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_invInertiaWorldSolve" class="property item">
                        <h3 class="name"><code>invInertiaWorldSolve</code></h3>
                        <span class="type"><a href="../classes/Mat3.html" class="crosslink">Mat3</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Body.js.html#l291"><code>src&#x2F;objects&#x2F;Body.js:291</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_invMass" class="property item">
                        <h3 class="name"><code>invMass</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Body.js.html#l144"><code>src&#x2F;objects&#x2F;Body.js:144</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_KINEMATIC" class="property item">
                        <h3 class="name"><code>KINEMATIC</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                            <span class="flag static">static</span>
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Body.js.html#l348"><code>src&#x2F;objects&#x2F;Body.js:348</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>A kinematic body moves under simulation according to its velocity. They do not respond to forces. They can be moved manually, but normally a kinematic body is moved by setting its velocity. A kinematic body behaves as if it has infinite mass. Kinematic bodies do not collide with other static or kinematic bodies.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_linearDamping" class="property item">
                        <h3 class="name"><code>linearDamping</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Body.js.html#l156"><code>src&#x2F;objects&#x2F;Body.js:156</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_mass" class="property item">
                        <h3 class="name"><code>mass</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Body.js.html#l137"><code>src&#x2F;objects&#x2F;Body.js:137</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                            <p><strong>Default:</strong> 0</p>
                    
                    
                    </div>
                    <div id="property_material" class="property item">
                        <h3 class="name"><code>material</code></h3>
                        <span class="type"><a href="../classes/Material.html" class="crosslink">Material</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Body.js.html#l150"><code>src&#x2F;objects&#x2F;Body.js:150</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_position" class="property item">
                        <h3 class="name"><code>position</code></h3>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Body.js.html#l90"><code>src&#x2F;objects&#x2F;Body.js:90</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_postStep" class="property item deprecated">
                        <h3 class="name"><code>postStep</code></h3>
                        <span class="type">Function</span>
                    
                            <span class="flag deprecated" title="Use World events instead">deprecated</span>
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Body.js.html#l64"><code>src&#x2F;objects&#x2F;Body.js:64</code></a>
                            </p>
                    
                                <p>Deprecated: Use World events instead</p>
                    
                        </div>
                    
                        <div class="description">
                            <p>Callback function that is used AFTER stepping the system. Inside the function, &quot;this&quot; will refer to this Body object.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_preStep" class="property item deprecated">
                        <h3 class="name"><code>preStep</code></h3>
                        <span class="type">Function</span>
                    
                            <span class="flag deprecated" title="Use World events instead">deprecated</span>
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Body.js.html#l56"><code>src&#x2F;objects&#x2F;Body.js:56</code></a>
                            </p>
                    
                                <p>Deprecated: Use World events instead</p>
                    
                        </div>
                    
                        <div class="description">
                            <p>Callback function that is used BEFORE stepping the system. Use it to apply forces, for example. Inside the function, &quot;this&quot; will refer to this Body object.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_previousPosition" class="property item">
                        <h3 class="name"><code>previousPosition</code></h3>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Body.js.html#l100"><code>src&#x2F;objects&#x2F;Body.js:100</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_quaternion" class="property item">
                        <h3 class="name"><code>quaternion</code></h3>
                        <span class="type"><a href="../classes/Quaternion.html" class="crosslink">Quaternion</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Body.js.html#l214"><code>src&#x2F;objects&#x2F;Body.js:214</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Orientation of the body</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_shapeOffsets" class="property item">
                        <h3 class="name"><code>shapeOffsets</code></h3>
                        <span class="type">Array</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Body.js.html#l256"><code>src&#x2F;objects&#x2F;Body.js:256</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_shapeOrientations" class="property item">
                        <h3 class="name"><code>shapeOrientations</code></h3>
                        <span class="type">Array</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Body.js.html#l262"><code>src&#x2F;objects&#x2F;Body.js:262</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_shapes" class="property item">
                        <h3 class="name"><code>shapes</code></h3>
                        <span class="type">Array</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Body.js.html#l250"><code>src&#x2F;objects&#x2F;Body.js:250</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_SLEEPING" class="property item">
                        <h3 class="name"><code>SLEEPING</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                            <span class="flag static">static</span>
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Body.js.html#l372"><code>src&#x2F;objects&#x2F;Body.js:372</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_sleepSpeedLimit" class="property item">
                        <h3 class="name"><code>sleepSpeedLimit</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Body.js.html#l187"><code>src&#x2F;objects&#x2F;Body.js:187</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>If the speed (the norm of the velocity) is smaller than this value, the body is considered sleepy.</p>
                    
                        </div>
                    
                            <p><strong>Default:</strong> 0.1</p>
                    
                    
                    </div>
                    <div id="property_sleepState" class="property item">
                        <h3 class="name"><code>sleepState</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Body.js.html#l180"><code>src&#x2F;objects&#x2F;Body.js:180</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Current sleep state.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_sleepTimeLimit" class="property item">
                        <h3 class="name"><code>sleepTimeLimit</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Body.js.html#l195"><code>src&#x2F;objects&#x2F;Body.js:195</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>If the body has been sleepy for this sleepTimeLimit seconds, it is considered sleeping.</p>
                    
                        </div>
                    
                            <p><strong>Default:</strong> 1</p>
                    
                    
                    </div>
                    <div id="property_SLEEPY" class="property item">
                        <h3 class="name"><code>SLEEPY</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                            <span class="flag static">static</span>
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Body.js.html#l365"><code>src&#x2F;objects&#x2F;Body.js:365</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_STATIC" class="property item">
                        <h3 class="name"><code>STATIC</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                            <span class="flag static">static</span>
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Body.js.html#l340"><code>src&#x2F;objects&#x2F;Body.js:340</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>A static body does not move during simulation and behaves as if it has infinite mass. Static bodies can be moved manually by setting the position of the body. The velocity of a static body is always zero. Static bodies do not collide with other static or kinematic bodies.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_torque" class="property item">
                        <h3 class="name"><code>torque</code></h3>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Body.js.html#l208"><code>src&#x2F;objects&#x2F;Body.js:208</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Rotational force on the body, around center of mass</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_type" class="property item">
                        <h3 class="name"><code>type</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Body.js.html#l162"><code>src&#x2F;objects&#x2F;Body.js:162</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>One of: Body.DYNAMIC, Body.STATIC and Body.KINEMATIC.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_velocity" class="property item">
                        <h3 class="name"><code>velocity</code></h3>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Body.js.html#l112"><code>src&#x2F;objects&#x2F;Body.js:112</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_world" class="property item">
                        <h3 class="name"><code>world</code></h3>
                        <span class="type"><a href="../classes/World.html" class="crosslink">World</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Body.js.html#l49"><code>src&#x2F;objects&#x2F;Body.js:49</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Reference to the world the body is living in</p>
                    
                        </div>
                    
                    
                    
                    </div>
            </div>


    </div>
</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="../assets/vendor/prettify/prettify-min.js"></script>
<script>prettyPrint();</script>
<script src="../assets/js/yui-prettify.js"></script>
<script src="../assets/../api.js"></script>
<script src="../assets/js/api-filter.js"></script>
<script src="../assets/js/api-list.js"></script>
<script src="../assets/js/api-search.js"></script>
<script src="../assets/js/apidocs.js"></script>
</body>
</html>
