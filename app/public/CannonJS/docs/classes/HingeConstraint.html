<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>HingeConstraint - cannon</title>
    <link rel="stylesheet" href="http://yui.yahooapis.com/3.9.1/build/cssgrids/cssgrids-min.css">
    <link rel="stylesheet" href="../assets/vendor/prettify/prettify-min.css">
    <link rel="stylesheet" href="../assets/css/main.css" id="site_styles">
    <link rel="icon" href="../assets/favicon.ico">
    <script src="http://yui.yahooapis.com/combo?3.9.1/build/yui/yui-min.js"></script>
</head>
<body class="yui3-skin-sam">

<div id="doc">
    <div id="hd" class="yui3-g header">
        <div class="yui3-u-3-4">
                <h1><img src="../assets/css/logo.png" title="cannon" width="117" height="52"></h1>
        </div>
        <div class="yui3-u-1-4 version">
            <em>API Docs for: 0.6.1</em>
        </div>
    </div>
    <div id="bd" class="yui3-g">

        <div class="yui3-u-1-4">
            <div id="docs-sidebar" class="sidebar apidocs">
                <div id="api-list">
                    <h2 class="off-left">APIs</h2>
                    <div id="api-tabview" class="tabview">
                        <ul class="tabs">
                            <li><a href="#api-classes">Classes</a></li>
                            <li><a href="#api-modules">Modules</a></li>
                        </ul>
                
                        <div id="api-tabview-filter">
                            <input type="search" id="api-filter" placeholder="Type to filter APIs">
                        </div>
                
                        <div id="api-tabview-panel">
                            <ul id="api-classes" class="apis classes">
                                <li><a href="../classes/AABB.html">AABB</a></li>
                                <li><a href="../classes/ArrayCollisionMatrix.html">ArrayCollisionMatrix</a></li>
                                <li><a href="../classes/Body.html">Body</a></li>
                                <li><a href="../classes/Box.html">Box</a></li>
                                <li><a href="../classes/Broadphase.html">Broadphase</a></li>
                                <li><a href="../classes/ConeEquation.html">ConeEquation</a></li>
                                <li><a href="../classes/ConeTwistConstraint.html">ConeTwistConstraint</a></li>
                                <li><a href="../classes/Constraint.html">Constraint</a></li>
                                <li><a href="../classes/ContactEquation.html">ContactEquation</a></li>
                                <li><a href="../classes/ContactMaterial.html">ContactMaterial</a></li>
                                <li><a href="../classes/ConvexPolyhedron.html">ConvexPolyhedron</a></li>
                                <li><a href="../classes/Cylinder.html">Cylinder</a></li>
                                <li><a href="../classes/Demo.html">Demo</a></li>
                                <li><a href="../classes/DistanceConstraint.html">DistanceConstraint</a></li>
                                <li><a href="../classes/Equation.html">Equation</a></li>
                                <li><a href="../classes/EventTarget.html">EventTarget</a></li>
                                <li><a href="../classes/FrictionEquation.html">FrictionEquation</a></li>
                                <li><a href="../classes/GridBroadphase.html">GridBroadphase</a></li>
                                <li><a href="../classes/GSSolver.html">GSSolver</a></li>
                                <li><a href="../classes/Heightfield.html">Heightfield</a></li>
                                <li><a href="../classes/HingeConstraint.html">HingeConstraint</a></li>
                                <li><a href="../classes/JacobianElement.html">JacobianElement</a></li>
                                <li><a href="../classes/LockConstraint.html">LockConstraint</a></li>
                                <li><a href="../classes/Mat3.html">Mat3</a></li>
                                <li><a href="../classes/Material.html">Material</a></li>
                                <li><a href="../classes/NaiveBroadphase.html">NaiveBroadphase</a></li>
                                <li><a href="../classes/Narrowphase.html">Narrowphase</a></li>
                                <li><a href="../classes/ObjectCollisionMatrix.html">ObjectCollisionMatrix</a></li>
                                <li><a href="../classes/Octree.html">Octree</a></li>
                                <li><a href="../classes/OctreeNode.html">OctreeNode</a></li>
                                <li><a href="../classes/Particle.html">Particle</a></li>
                                <li><a href="../classes/Plane.html">Plane</a></li>
                                <li><a href="../classes/PointToPointConstraint.html">PointToPointConstraint</a></li>
                                <li><a href="../classes/Pool.html">Pool</a></li>
                                <li><a href="../classes/Quaternion.html">Quaternion</a></li>
                                <li><a href="../classes/Ray.html">Ray</a></li>
                                <li><a href="../classes/RaycastResult.html">RaycastResult</a></li>
                                <li><a href="../classes/RaycastVehicle.html">RaycastVehicle</a></li>
                                <li><a href="../classes/RigidVehicle.html">RigidVehicle</a></li>
                                <li><a href="../classes/RotationalEquation.html">RotationalEquation</a></li>
                                <li><a href="../classes/RotationalMotorEquation.html">RotationalMotorEquation</a></li>
                                <li><a href="../classes/SAPBroadphase.html">SAPBroadphase</a></li>
                                <li><a href="../classes/Shape.html">Shape</a></li>
                                <li><a href="../classes/Solver.html">Solver</a></li>
                                <li><a href="../classes/Sphere.html">Sphere</a></li>
                                <li><a href="../classes/SPHSystem.html">SPHSystem</a></li>
                                <li><a href="../classes/SplitSolver.html">SplitSolver</a></li>
                                <li><a href="../classes/Spring.html">Spring</a></li>
                                <li><a href="../classes/Transform.html">Transform</a></li>
                                <li><a href="../classes/Trimesh.html">Trimesh</a></li>
                                <li><a href="../classes/TupleDictionary.html">TupleDictionary</a></li>
                                <li><a href="../classes/Vec3.html">Vec3</a></li>
                                <li><a href="../classes/Vec3Pool.html">Vec3Pool</a></li>
                                <li><a href="../classes/WheelInfo.html">WheelInfo</a></li>
                                <li><a href="../classes/World.html">World</a></li>
                            </ul>
                
                            <ul id="api-modules" class="apis modules">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="yui3-u-3-4">
                <div id="api-options">
                    Show:
                    <label for="api-show-inherited">
                        <input type="checkbox" id="api-show-inherited" checked>
                        Inherited
                    </label>
            
                    <label for="api-show-protected">
                        <input type="checkbox" id="api-show-protected">
                        Protected
                    </label>
            
                    <label for="api-show-private">
                        <input type="checkbox" id="api-show-private">
                        Private
                    </label>
                    <label for="api-show-deprecated">
                        <input type="checkbox" id="api-show-deprecated">
                        Deprecated
                    </label>
            
                </div>
            
            <div class="apidocs">
                <div id="docs-main">
                    <div class="content">
<h1>HingeConstraint Class</h1>
<div class="box meta">

        <div class="extends">
            Extends <a href="../classes/PointToPointConstraint.html" class="crosslink">PointToPointConstraint</a>
        </div>

        <div class="foundat">
            Defined in: <a href="../files/src_constraints_HingeConstraint.js.html#l10"><code>src&#x2F;constraints&#x2F;HingeConstraint.js:10</code></a>
        </div>


</div>


<div class="box intro">
    <p>Hinge constraint. Think of it as a door hinge. It tries to keep the door in the correct place and with the correct orientation.</p>

</div>

    <div class="constructor">
        <h2>Constructor</h2>
        <div id="method_HingeConstraint" class="method item">
            <h3 class="name"><code>HingeConstraint</code></h3>
        
                <div class="args">
                    <span class="paren">(</span><ul class="args-list inline commas">
                        <li class="arg">
                                <code>bodyA</code>
                        </li>
                        <li class="arg">
                                <code>bodyB</code>
                        </li>
                        <li class="arg">
                                <code class="optional">[options]</code>
                        </li>
                    </ul><span class="paren">)</span>
                </div>
        
        
        
        
        
        
        
        
            <div class="meta">
                        <p>
                        Defined in
                <a href="../files/src_constraints_HingeConstraint.js.html#l10"><code>src&#x2F;constraints&#x2F;HingeConstraint.js:10</code></a>
                </p>
        
        
        
            </div>
        
            <div class="description">
                
            </div>
        
                <div class="params">
                    <h4>Parameters:</h4>
        
                    <ul class="params-list">
                        <li class="param">
                                <code class="param-name">bodyA</code>
                                <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>
        
        
                            <div class="param-description">
                                 
                            </div>
        
                        </li>
                        <li class="param">
                                <code class="param-name">bodyB</code>
                                <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>
        
        
                            <div class="param-description">
                                 
                            </div>
        
                        </li>
                        <li class="param">
                                <code class="param-name optional">[options]</code>
                                <span class="type">Object</span>
                                <span class="flag optional" title="This parameter is optional.">optional</span>
        
        
                            <div class="param-description">
                                 
                            </div>
        
                                <ul class="params-list">
                                    <li class="param">
                                            <code class="param-name optional">[pivotA]</code>
                                            <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
                                            <span class="flag optional" title="This parameter is optional.">optional</span>
        
                                        <div class="param-description">
                                            <p>A point defined locally in bodyA. This defines the offset of axisA.</p>
        
                                        </div>
        
                                    </li>
                                    <li class="param">
                                            <code class="param-name optional">[axisA]</code>
                                            <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
                                            <span class="flag optional" title="This parameter is optional.">optional</span>
        
                                        <div class="param-description">
                                            <p>An axis that bodyA can rotate around, defined locally in bodyA.</p>
        
                                        </div>
        
                                    </li>
                                    <li class="param">
                                            <code class="param-name optional">[pivotB]</code>
                                            <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
                                            <span class="flag optional" title="This parameter is optional.">optional</span>
        
                                        <div class="param-description">
                                            
                                        </div>
        
                                    </li>
                                    <li class="param">
                                            <code class="param-name optional">[axisB]</code>
                                            <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
                                            <span class="flag optional" title="This parameter is optional.">optional</span>
        
                                        <div class="param-description">
                                            
                                        </div>
        
                                    </li>
                                    <li class="param">
                                            <code class="param-name optional">[maxForce=1e6]</code>
                                            <span class="type">Number</span>
                                            <span class="flag optional" title="This parameter is optional.">optional</span>
        
                                        <div class="param-description">
                                            
                                        </div>
        
                                    </li>
                                </ul>
                        </li>
                    </ul>
                </div>
        
        
        
        </div>
    </div>

<div id="classdocs" class="tabview">
    <ul class="api-class-tabs">
        <li class="api-class-tab index"><a href="#index">Index</a></li>

            <li class="api-class-tab methods"><a href="#methods">Methods</a></li>
            <li class="api-class-tab properties"><a href="#properties">Properties</a></li>
    </ul>

    <div>
        <div id="index" class="api-class-tabpanel index">
            <h2 class="off-left">Item Index</h2>

                <div class="index-section methods">
                    <h3>Methods</h3>

                    <ul class="index-list methods extends">
                            <li class="index-item method inherited">
                                <a href="#method_disable">disable</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_disableMotor">disableMotor</a>

                            </li>
                            <li class="index-item method inherited">
                                <a href="#method_enable">enable</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_enableMotor">enableMotor</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_setMotorMaxForce">setMotorMaxForce</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_setMotorSpeed">setMotorSpeed</a>

                            </li>
                            <li class="index-item method inherited">
                                <a href="#method_update">update</a>

                            </li>
                    </ul>
                </div>

                <div class="index-section properties">
                    <h3>Properties</h3>

                    <ul class="index-list properties extends">
                            <li class="index-item property">
                                <a href="#property_axisA">axisA</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_axisB">axisB</a>

                            </li>
                            <li class="index-item property inherited">
                                <a href="#property_bodyA">bodyA</a>

                            </li>
                            <li class="index-item property inherited">
                                <a href="#property_bodyB">bodyB</a>

                            </li>
                            <li class="index-item property inherited">
                                <a href="#property_collideConnected">collideConnected</a>

                            </li>
                            <li class="index-item property inherited">
                                <a href="#property_equations">equations</a>

                            </li>
                            <li class="index-item property inherited">
                                <a href="#property_equationX">equationX</a>

                            </li>
                            <li class="index-item property inherited">
                                <a href="#property_equationY">equationY</a>

                            </li>
                            <li class="index-item property inherited">
                                <a href="#property_equationZ">equationZ</a>

                            </li>
                            <li class="index-item property inherited">
                                <a href="#property_id">id</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_motorEquation">motorEquation</a>

                            </li>
                            <li class="index-item property inherited">
                                <a href="#property_pivotA">pivotA</a>

                            </li>
                            <li class="index-item property inherited">
                                <a href="#property_pivotB">pivotB</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_rotationalEquation1">rotationalEquation1</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_rotationalEquation2">rotationalEquation2</a>

                            </li>
                    </ul>
                </div>


        </div>

            <div id="methods" class="api-class-tabpanel">
                <h2 class="off-left">Methods</h2>

<div id="method_disable" class="method item inherited">
    <h3 class="name"><code>disable</code></h3>

        <span class="paren">()</span>








    <div class="meta">
                <p>Inherited from
                <a href="../classes/Constraint.html#method_disable">Constraint</a>:
        <a href="../files/src_constraints_Constraint.js.html#l80"><code>src&#x2F;constraints&#x2F;Constraint.js:80</code></a>
        </p>



    </div>

    <div class="description">
        <p>Disables all equations in the constraint.</p>

    </div>




</div>
<div id="method_disableMotor" class="method item">
    <h3 class="name"><code>disableMotor</code></h3>

        <span class="paren">()</span>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_constraints_HingeConstraint.js.html#l80"><code>src&#x2F;constraints&#x2F;HingeConstraint.js:80</code></a>
        </p>



    </div>

    <div class="description">
        
    </div>




</div>
<div id="method_enable" class="method item inherited">
    <h3 class="name"><code>enable</code></h3>

        <span class="paren">()</span>








    <div class="meta">
                <p>Inherited from
                <a href="../classes/Constraint.html#method_enable">Constraint</a>:
        <a href="../files/src_constraints_Constraint.js.html#l69"><code>src&#x2F;constraints&#x2F;Constraint.js:69</code></a>
        </p>



    </div>

    <div class="description">
        <p>Enables all equations in the constraint.</p>

    </div>




</div>
<div id="method_enableMotor" class="method item">
    <h3 class="name"><code>enableMotor</code></h3>

        <span class="paren">()</span>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_constraints_HingeConstraint.js.html#l73"><code>src&#x2F;constraints&#x2F;HingeConstraint.js:73</code></a>
        </p>



    </div>

    <div class="description">
        
    </div>




</div>
<div id="method_setMotorMaxForce" class="method item">
    <h3 class="name"><code>setMotorMaxForce</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>maxForce</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_constraints_HingeConstraint.js.html#l95"><code>src&#x2F;constraints&#x2F;HingeConstraint.js:95</code></a>
        </p>



    </div>

    <div class="description">
        
    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">maxForce</code>
                        <span class="type">Number</span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_setMotorSpeed" class="method item">
    <h3 class="name"><code>setMotorSpeed</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>speed</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_constraints_HingeConstraint.js.html#l87"><code>src&#x2F;constraints&#x2F;HingeConstraint.js:87</code></a>
        </p>



    </div>

    <div class="description">
        
    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">speed</code>
                        <span class="type">Number</span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_update" class="method item inherited">
    <h3 class="name"><code>update</code></h3>

        <span class="paren">()</span>








    <div class="meta">
                <p>Inherited from
                <a href="../classes/Constraint.html#method_update">Constraint</a>:
        <a href="../files/src_constraints_Constraint.js.html#l61"><code>src&#x2F;constraints&#x2F;Constraint.js:61</code></a>
        </p>



    </div>

    <div class="description">
        <p>Update all the equations with data.</p>

    </div>




</div>
            </div>

            <div id="properties" class="api-class-tabpanel">
                <h2 class="off-left">Properties</h2>

                    <div id="property_axisA" class="property item">
                        <h3 class="name"><code>axisA</code></h3>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_constraints_HingeConstraint.js.html#l33"><code>src&#x2F;constraints&#x2F;HingeConstraint.js:33</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Rotation axis, defined locally in bodyA.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_axisB" class="property item">
                        <h3 class="name"><code>axisB</code></h3>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_constraints_HingeConstraint.js.html#l40"><code>src&#x2F;constraints&#x2F;HingeConstraint.js:40</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Rotation axis, defined locally in bodyB.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_bodyA" class="property item inherited">
                        <h3 class="name"><code>bodyA</code></h3>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>Inherited from
                                    <a href="../classes/Constraint.html#property_bodyA">Constraint</a>:
                            <a href="../files/src_constraints_Constraint.js.html#l29"><code>src&#x2F;constraints&#x2F;Constraint.js:29</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_bodyB" class="property item inherited">
                        <h3 class="name"><code>bodyB</code></h3>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>Inherited from
                                    <a href="../classes/Constraint.html#property_bodyB">Constraint</a>:
                            <a href="../files/src_constraints_Constraint.js.html#l34"><code>src&#x2F;constraints&#x2F;Constraint.js:34</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_collideConnected" class="property item inherited">
                        <h3 class="name"><code>collideConnected</code></h3>
                        <span class="type">Boolean</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>Inherited from
                                    <a href="../classes/Constraint.html#property_collideConnected">Constraint</a>:
                            <a href="../files/src_constraints_Constraint.js.html#l44"><code>src&#x2F;constraints&#x2F;Constraint.js:44</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Set to true if you want the bodies to collide when they are connected.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_equations" class="property item inherited">
                        <h3 class="name"><code>equations</code></h3>
                        <span class="type">Array</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>Inherited from
                                    <a href="../classes/Constraint.html#property_equations">Constraint</a>:
                            <a href="../files/src_constraints_Constraint.js.html#l22"><code>src&#x2F;constraints&#x2F;Constraint.js:22</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Equations to be solved in this constraint</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_equationX" class="property item inherited">
                        <h3 class="name"><code>equationX</code></h3>
                        <span class="type"><a href="../classes/ContactEquation.html" class="crosslink">ContactEquation</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>Inherited from
                                    <a href="../classes/PointToPointConstraint.html#property_equationX">PointToPointConstraint</a>:
                            <a href="../files/src_constraints_PointToPointConstraint.js.html#l49"><code>src&#x2F;constraints&#x2F;PointToPointConstraint.js:49</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_equationY" class="property item inherited">
                        <h3 class="name"><code>equationY</code></h3>
                        <span class="type"><a href="../classes/ContactEquation.html" class="crosslink">ContactEquation</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>Inherited from
                                    <a href="../classes/PointToPointConstraint.html#property_equationY">PointToPointConstraint</a>:
                            <a href="../files/src_constraints_PointToPointConstraint.js.html#l54"><code>src&#x2F;constraints&#x2F;PointToPointConstraint.js:54</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_equationZ" class="property item inherited">
                        <h3 class="name"><code>equationZ</code></h3>
                        <span class="type"><a href="../classes/ContactEquation.html" class="crosslink">ContactEquation</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>Inherited from
                                    <a href="../classes/PointToPointConstraint.html#property_equationZ">PointToPointConstraint</a>:
                            <a href="../files/src_constraints_PointToPointConstraint.js.html#l59"><code>src&#x2F;constraints&#x2F;PointToPointConstraint.js:59</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_id" class="property item inherited">
                        <h3 class="name"><code>id</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>Inherited from
                                    <a href="../classes/Constraint.html#property_id">Constraint</a>:
                            <a href="../files/src_constraints_Constraint.js.html#l39"><code>src&#x2F;constraints&#x2F;Constraint.js:39</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_motorEquation" class="property item">
                        <h3 class="name"><code>motorEquation</code></h3>
                        <span class="type"><a href="../classes/RotationalMotorEquation.html" class="crosslink">RotationalMotorEquation</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_constraints_HingeConstraint.js.html#l57"><code>src&#x2F;constraints&#x2F;HingeConstraint.js:57</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_pivotA" class="property item inherited">
                        <h3 class="name"><code>pivotA</code></h3>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>Inherited from
                                    <a href="../classes/PointToPointConstraint.html#property_pivotA">PointToPointConstraint</a>:
                            <a href="../files/src_constraints_PointToPointConstraint.js.html#l37"><code>src&#x2F;constraints&#x2F;PointToPointConstraint.js:37</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Pivot, defined locally in bodyA.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_pivotB" class="property item inherited">
                        <h3 class="name"><code>pivotB</code></h3>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>Inherited from
                                    <a href="../classes/PointToPointConstraint.html#property_pivotB">PointToPointConstraint</a>:
                            <a href="../files/src_constraints_PointToPointConstraint.js.html#l43"><code>src&#x2F;constraints&#x2F;PointToPointConstraint.js:43</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Pivot, defined locally in bodyB.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_rotationalEquation1" class="property item">
                        <h3 class="name"><code>rotationalEquation1</code></h3>
                        <span class="type"><a href="../classes/RotationalEquation.html" class="crosslink">RotationalEquation</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_constraints_HingeConstraint.js.html#l47"><code>src&#x2F;constraints&#x2F;HingeConstraint.js:47</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_rotationalEquation2" class="property item">
                        <h3 class="name"><code>rotationalEquation2</code></h3>
                        <span class="type"><a href="../classes/RotationalEquation.html" class="crosslink">RotationalEquation</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_constraints_HingeConstraint.js.html#l52"><code>src&#x2F;constraints&#x2F;HingeConstraint.js:52</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
            </div>


    </div>
</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="../assets/vendor/prettify/prettify-min.js"></script>
<script>prettyPrint();</script>
<script src="../assets/js/yui-prettify.js"></script>
<script src="../assets/../api.js"></script>
<script src="../assets/js/api-filter.js"></script>
<script src="../assets/js/api-list.js"></script>
<script src="../assets/js/api-search.js"></script>
<script src="../assets/js/apidocs.js"></script>
</body>
</html>
