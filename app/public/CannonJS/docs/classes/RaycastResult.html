<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>RaycastResult - cannon</title>
    <link rel="stylesheet" href="http://yui.yahooapis.com/3.9.1/build/cssgrids/cssgrids-min.css">
    <link rel="stylesheet" href="../assets/vendor/prettify/prettify-min.css">
    <link rel="stylesheet" href="../assets/css/main.css" id="site_styles">
    <link rel="icon" href="../assets/favicon.ico">
    <script src="http://yui.yahooapis.com/combo?3.9.1/build/yui/yui-min.js"></script>
</head>
<body class="yui3-skin-sam">

<div id="doc">
    <div id="hd" class="yui3-g header">
        <div class="yui3-u-3-4">
                <h1><img src="../assets/css/logo.png" title="cannon" width="117" height="52"></h1>
        </div>
        <div class="yui3-u-1-4 version">
            <em>API Docs for: 0.6.1</em>
        </div>
    </div>
    <div id="bd" class="yui3-g">

        <div class="yui3-u-1-4">
            <div id="docs-sidebar" class="sidebar apidocs">
                <div id="api-list">
                    <h2 class="off-left">APIs</h2>
                    <div id="api-tabview" class="tabview">
                        <ul class="tabs">
                            <li><a href="#api-classes">Classes</a></li>
                            <li><a href="#api-modules">Modules</a></li>
                        </ul>
                
                        <div id="api-tabview-filter">
                            <input type="search" id="api-filter" placeholder="Type to filter APIs">
                        </div>
                
                        <div id="api-tabview-panel">
                            <ul id="api-classes" class="apis classes">
                                <li><a href="../classes/AABB.html">AABB</a></li>
                                <li><a href="../classes/ArrayCollisionMatrix.html">ArrayCollisionMatrix</a></li>
                                <li><a href="../classes/Body.html">Body</a></li>
                                <li><a href="../classes/Box.html">Box</a></li>
                                <li><a href="../classes/Broadphase.html">Broadphase</a></li>
                                <li><a href="../classes/ConeEquation.html">ConeEquation</a></li>
                                <li><a href="../classes/ConeTwistConstraint.html">ConeTwistConstraint</a></li>
                                <li><a href="../classes/Constraint.html">Constraint</a></li>
                                <li><a href="../classes/ContactEquation.html">ContactEquation</a></li>
                                <li><a href="../classes/ContactMaterial.html">ContactMaterial</a></li>
                                <li><a href="../classes/ConvexPolyhedron.html">ConvexPolyhedron</a></li>
                                <li><a href="../classes/Cylinder.html">Cylinder</a></li>
                                <li><a href="../classes/Demo.html">Demo</a></li>
                                <li><a href="../classes/DistanceConstraint.html">DistanceConstraint</a></li>
                                <li><a href="../classes/Equation.html">Equation</a></li>
                                <li><a href="../classes/EventTarget.html">EventTarget</a></li>
                                <li><a href="../classes/FrictionEquation.html">FrictionEquation</a></li>
                                <li><a href="../classes/GridBroadphase.html">GridBroadphase</a></li>
                                <li><a href="../classes/GSSolver.html">GSSolver</a></li>
                                <li><a href="../classes/Heightfield.html">Heightfield</a></li>
                                <li><a href="../classes/HingeConstraint.html">HingeConstraint</a></li>
                                <li><a href="../classes/JacobianElement.html">JacobianElement</a></li>
                                <li><a href="../classes/LockConstraint.html">LockConstraint</a></li>
                                <li><a href="../classes/Mat3.html">Mat3</a></li>
                                <li><a href="../classes/Material.html">Material</a></li>
                                <li><a href="../classes/NaiveBroadphase.html">NaiveBroadphase</a></li>
                                <li><a href="../classes/Narrowphase.html">Narrowphase</a></li>
                                <li><a href="../classes/ObjectCollisionMatrix.html">ObjectCollisionMatrix</a></li>
                                <li><a href="../classes/Octree.html">Octree</a></li>
                                <li><a href="../classes/OctreeNode.html">OctreeNode</a></li>
                                <li><a href="../classes/Particle.html">Particle</a></li>
                                <li><a href="../classes/Plane.html">Plane</a></li>
                                <li><a href="../classes/PointToPointConstraint.html">PointToPointConstraint</a></li>
                                <li><a href="../classes/Pool.html">Pool</a></li>
                                <li><a href="../classes/Quaternion.html">Quaternion</a></li>
                                <li><a href="../classes/Ray.html">Ray</a></li>
                                <li><a href="../classes/RaycastResult.html">RaycastResult</a></li>
                                <li><a href="../classes/RaycastVehicle.html">RaycastVehicle</a></li>
                                <li><a href="../classes/RigidVehicle.html">RigidVehicle</a></li>
                                <li><a href="../classes/RotationalEquation.html">RotationalEquation</a></li>
                                <li><a href="../classes/RotationalMotorEquation.html">RotationalMotorEquation</a></li>
                                <li><a href="../classes/SAPBroadphase.html">SAPBroadphase</a></li>
                                <li><a href="../classes/Shape.html">Shape</a></li>
                                <li><a href="../classes/Solver.html">Solver</a></li>
                                <li><a href="../classes/Sphere.html">Sphere</a></li>
                                <li><a href="../classes/SPHSystem.html">SPHSystem</a></li>
                                <li><a href="../classes/SplitSolver.html">SplitSolver</a></li>
                                <li><a href="../classes/Spring.html">Spring</a></li>
                                <li><a href="../classes/Transform.html">Transform</a></li>
                                <li><a href="../classes/Trimesh.html">Trimesh</a></li>
                                <li><a href="../classes/TupleDictionary.html">TupleDictionary</a></li>
                                <li><a href="../classes/Vec3.html">Vec3</a></li>
                                <li><a href="../classes/Vec3Pool.html">Vec3Pool</a></li>
                                <li><a href="../classes/WheelInfo.html">WheelInfo</a></li>
                                <li><a href="../classes/World.html">World</a></li>
                            </ul>
                
                            <ul id="api-modules" class="apis modules">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="yui3-u-3-4">
                <div id="api-options">
                    Show:
                    <label for="api-show-inherited">
                        <input type="checkbox" id="api-show-inherited" checked>
                        Inherited
                    </label>
            
                    <label for="api-show-protected">
                        <input type="checkbox" id="api-show-protected">
                        Protected
                    </label>
            
                    <label for="api-show-private">
                        <input type="checkbox" id="api-show-private">
                        Private
                    </label>
                    <label for="api-show-deprecated">
                        <input type="checkbox" id="api-show-deprecated">
                        Deprecated
                    </label>
            
                </div>
            
            <div class="apidocs">
                <div id="docs-main">
                    <div class="content">
<h1>RaycastResult Class</h1>
<div class="box meta">


        <div class="foundat">
            Defined in: <a href="../files/src_collision_RaycastResult.js.html#l5"><code>src&#x2F;collision&#x2F;RaycastResult.js:5</code></a>
        </div>


</div>


<div class="box intro">
    <p>Storage for Ray casting data.</p>

</div>

    <div class="constructor">
        <h2>Constructor</h2>
        <div id="method_RaycastResult" class="method item">
            <h3 class="name"><code>RaycastResult</code></h3>
        
                <span class="paren">()</span>
        
        
        
        
        
        
        
        
            <div class="meta">
                        <p>
                        Defined in
                <a href="../files/src_collision_RaycastResult.js.html#l5"><code>src&#x2F;collision&#x2F;RaycastResult.js:5</code></a>
                </p>
        
        
        
            </div>
        
            <div class="description">
                
            </div>
        
        
        
        
        </div>
    </div>

<div id="classdocs" class="tabview">
    <ul class="api-class-tabs">
        <li class="api-class-tab index"><a href="#index">Index</a></li>

            <li class="api-class-tab methods"><a href="#methods">Methods</a></li>
            <li class="api-class-tab properties"><a href="#properties">Properties</a></li>
    </ul>

    <div>
        <div id="index" class="api-class-tabpanel index">
            <h2 class="off-left">Item Index</h2>

                <div class="index-section methods">
                    <h3>Methods</h3>

                    <ul class="index-list methods">
                            <li class="index-item method">
                                <a href="#method_abort">abort</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_reset">reset</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_set">set</a>

                            </li>
                    </ul>
                </div>

                <div class="index-section properties">
                    <h3>Properties</h3>

                    <ul class="index-list properties">
                            <li class="index-item property private">
                                <a href="#property__shouldStop">_shouldStop</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_body">body</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_distance">distance</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_hasHit">hasHit</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_hitFaceIndex">hitFaceIndex</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_hitNormalWorld">hitNormalWorld</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_hitPointWorld">hitPointWorld</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_rayFromWorld">rayFromWorld</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_rayToWorld">rayToWorld</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_shape">shape</a>

                            </li>
                    </ul>
                </div>


        </div>

            <div id="methods" class="api-class-tabpanel">
                <h2 class="off-left">Methods</h2>

<div id="method_abort" class="method item">
    <h3 class="name"><code>abort</code></h3>

        <span class="paren">()</span>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_collision_RaycastResult.js.html#l89"><code>src&#x2F;collision&#x2F;RaycastResult.js:89</code></a>
        </p>



    </div>

    <div class="description">
        
    </div>




</div>
<div id="method_reset" class="method item">
    <h3 class="name"><code>reset</code></h3>

        <span class="paren">()</span>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_collision_RaycastResult.js.html#l72"><code>src&#x2F;collision&#x2F;RaycastResult.js:72</code></a>
        </p>



    </div>

    <div class="description">
        <p>Reset all result data.</p>

    </div>




</div>
<div id="method_set" class="method item">
    <h3 class="name"><code>set</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>rayFromWorld</code>
                </li>
                <li class="arg">
                        <code>rayToWorld</code>
                </li>
                <li class="arg">
                        <code>hitNormalWorld</code>
                </li>
                <li class="arg">
                        <code>hitPointWorld</code>
                </li>
                <li class="arg">
                        <code>shape</code>
                </li>
                <li class="arg">
                        <code>body</code>
                </li>
                <li class="arg">
                        <code>distance</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_collision_RaycastResult.js.html#l96"><code>src&#x2F;collision&#x2F;RaycastResult.js:96</code></a>
        </p>



    </div>

    <div class="description">
        
    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">rayFromWorld</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">rayToWorld</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">hitNormalWorld</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">hitPointWorld</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">shape</code>
                        <span class="type"><a href="../classes/Shape.html" class="crosslink">Shape</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">body</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">distance</code>
                        <span class="type">Number</span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
            </div>

            <div id="properties" class="api-class-tabpanel">
                <h2 class="off-left">Properties</h2>

                    <div id="property__shouldStop" class="property item private">
                        <h3 class="name"><code>_shouldStop</code></h3>
                        <span class="type">Boolean</span>
                    
                    
                            <span class="flag private">private</span>
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_collision_RaycastResult.js.html#l63"><code>src&#x2F;collision&#x2F;RaycastResult.js:63</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>If the ray should stop traversing the bodies.</p>
                    
                        </div>
                    
                            <p><strong>Default:</strong> false</p>
                    
                    
                    </div>
                    <div id="property_body" class="property item">
                        <h3 class="name"><code>body</code></h3>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_collision_RaycastResult.js.html#l43"><code>src&#x2F;collision&#x2F;RaycastResult.js:43</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>The hit body, or null.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_distance" class="property item">
                        <h3 class="name"><code>distance</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_collision_RaycastResult.js.html#l56"><code>src&#x2F;collision&#x2F;RaycastResult.js:56</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Distance to the hit. Will be set to -1 if there was no hit.</p>
                    
                        </div>
                    
                            <p><strong>Default:</strong> -1</p>
                    
                    
                    </div>
                    <div id="property_hasHit" class="property item">
                        <h3 class="name"><code>hasHit</code></h3>
                        <span class="type">Boolean</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_collision_RaycastResult.js.html#l32"><code>src&#x2F;collision&#x2F;RaycastResult.js:32</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_hitFaceIndex" class="property item">
                        <h3 class="name"><code>hitFaceIndex</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_collision_RaycastResult.js.html#l49"><code>src&#x2F;collision&#x2F;RaycastResult.js:49</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>The index of the hit triangle, if the hit shape was a trimesh.</p>
                    
                        </div>
                    
                            <p><strong>Default:</strong> -1</p>
                    
                    
                    </div>
                    <div id="property_hitNormalWorld" class="property item">
                        <h3 class="name"><code>hitNormalWorld</code></h3>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_collision_RaycastResult.js.html#l22"><code>src&#x2F;collision&#x2F;RaycastResult.js:22</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_hitPointWorld" class="property item">
                        <h3 class="name"><code>hitPointWorld</code></h3>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_collision_RaycastResult.js.html#l27"><code>src&#x2F;collision&#x2F;RaycastResult.js:27</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_rayFromWorld" class="property item">
                        <h3 class="name"><code>rayFromWorld</code></h3>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_collision_RaycastResult.js.html#l12"><code>src&#x2F;collision&#x2F;RaycastResult.js:12</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_rayToWorld" class="property item">
                        <h3 class="name"><code>rayToWorld</code></h3>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_collision_RaycastResult.js.html#l17"><code>src&#x2F;collision&#x2F;RaycastResult.js:17</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_shape" class="property item">
                        <h3 class="name"><code>shape</code></h3>
                        <span class="type"><a href="../classes/Shape.html" class="crosslink">Shape</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_collision_RaycastResult.js.html#l37"><code>src&#x2F;collision&#x2F;RaycastResult.js:37</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>The hit shape, or null.</p>
                    
                        </div>
                    
                    
                    
                    </div>
            </div>


    </div>
</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="../assets/vendor/prettify/prettify-min.js"></script>
<script>prettyPrint();</script>
<script src="../assets/js/yui-prettify.js"></script>
<script src="../assets/../api.js"></script>
<script src="../assets/js/api-filter.js"></script>
<script src="../assets/js/api-list.js"></script>
<script src="../assets/js/api-search.js"></script>
<script src="../assets/js/apidocs.js"></script>
</body>
</html>
