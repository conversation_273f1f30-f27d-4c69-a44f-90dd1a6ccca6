<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Spring - cannon</title>
    <link rel="stylesheet" href="http://yui.yahooapis.com/3.9.1/build/cssgrids/cssgrids-min.css">
    <link rel="stylesheet" href="../assets/vendor/prettify/prettify-min.css">
    <link rel="stylesheet" href="../assets/css/main.css" id="site_styles">
    <link rel="icon" href="../assets/favicon.ico">
    <script src="http://yui.yahooapis.com/combo?3.9.1/build/yui/yui-min.js"></script>
</head>
<body class="yui3-skin-sam">

<div id="doc">
    <div id="hd" class="yui3-g header">
        <div class="yui3-u-3-4">
                <h1><img src="../assets/css/logo.png" title="cannon" width="117" height="52"></h1>
        </div>
        <div class="yui3-u-1-4 version">
            <em>API Docs for: 0.6.1</em>
        </div>
    </div>
    <div id="bd" class="yui3-g">

        <div class="yui3-u-1-4">
            <div id="docs-sidebar" class="sidebar apidocs">
                <div id="api-list">
                    <h2 class="off-left">APIs</h2>
                    <div id="api-tabview" class="tabview">
                        <ul class="tabs">
                            <li><a href="#api-classes">Classes</a></li>
                            <li><a href="#api-modules">Modules</a></li>
                        </ul>
                
                        <div id="api-tabview-filter">
                            <input type="search" id="api-filter" placeholder="Type to filter APIs">
                        </div>
                
                        <div id="api-tabview-panel">
                            <ul id="api-classes" class="apis classes">
                                <li><a href="../classes/AABB.html">AABB</a></li>
                                <li><a href="../classes/ArrayCollisionMatrix.html">ArrayCollisionMatrix</a></li>
                                <li><a href="../classes/Body.html">Body</a></li>
                                <li><a href="../classes/Box.html">Box</a></li>
                                <li><a href="../classes/Broadphase.html">Broadphase</a></li>
                                <li><a href="../classes/ConeEquation.html">ConeEquation</a></li>
                                <li><a href="../classes/ConeTwistConstraint.html">ConeTwistConstraint</a></li>
                                <li><a href="../classes/Constraint.html">Constraint</a></li>
                                <li><a href="../classes/ContactEquation.html">ContactEquation</a></li>
                                <li><a href="../classes/ContactMaterial.html">ContactMaterial</a></li>
                                <li><a href="../classes/ConvexPolyhedron.html">ConvexPolyhedron</a></li>
                                <li><a href="../classes/Cylinder.html">Cylinder</a></li>
                                <li><a href="../classes/Demo.html">Demo</a></li>
                                <li><a href="../classes/DistanceConstraint.html">DistanceConstraint</a></li>
                                <li><a href="../classes/Equation.html">Equation</a></li>
                                <li><a href="../classes/EventTarget.html">EventTarget</a></li>
                                <li><a href="../classes/FrictionEquation.html">FrictionEquation</a></li>
                                <li><a href="../classes/GridBroadphase.html">GridBroadphase</a></li>
                                <li><a href="../classes/GSSolver.html">GSSolver</a></li>
                                <li><a href="../classes/Heightfield.html">Heightfield</a></li>
                                <li><a href="../classes/HingeConstraint.html">HingeConstraint</a></li>
                                <li><a href="../classes/JacobianElement.html">JacobianElement</a></li>
                                <li><a href="../classes/LockConstraint.html">LockConstraint</a></li>
                                <li><a href="../classes/Mat3.html">Mat3</a></li>
                                <li><a href="../classes/Material.html">Material</a></li>
                                <li><a href="../classes/NaiveBroadphase.html">NaiveBroadphase</a></li>
                                <li><a href="../classes/Narrowphase.html">Narrowphase</a></li>
                                <li><a href="../classes/ObjectCollisionMatrix.html">ObjectCollisionMatrix</a></li>
                                <li><a href="../classes/Octree.html">Octree</a></li>
                                <li><a href="../classes/OctreeNode.html">OctreeNode</a></li>
                                <li><a href="../classes/Particle.html">Particle</a></li>
                                <li><a href="../classes/Plane.html">Plane</a></li>
                                <li><a href="../classes/PointToPointConstraint.html">PointToPointConstraint</a></li>
                                <li><a href="../classes/Pool.html">Pool</a></li>
                                <li><a href="../classes/Quaternion.html">Quaternion</a></li>
                                <li><a href="../classes/Ray.html">Ray</a></li>
                                <li><a href="../classes/RaycastResult.html">RaycastResult</a></li>
                                <li><a href="../classes/RaycastVehicle.html">RaycastVehicle</a></li>
                                <li><a href="../classes/RigidVehicle.html">RigidVehicle</a></li>
                                <li><a href="../classes/RotationalEquation.html">RotationalEquation</a></li>
                                <li><a href="../classes/RotationalMotorEquation.html">RotationalMotorEquation</a></li>
                                <li><a href="../classes/SAPBroadphase.html">SAPBroadphase</a></li>
                                <li><a href="../classes/Shape.html">Shape</a></li>
                                <li><a href="../classes/Solver.html">Solver</a></li>
                                <li><a href="../classes/Sphere.html">Sphere</a></li>
                                <li><a href="../classes/SPHSystem.html">SPHSystem</a></li>
                                <li><a href="../classes/SplitSolver.html">SplitSolver</a></li>
                                <li><a href="../classes/Spring.html">Spring</a></li>
                                <li><a href="../classes/Transform.html">Transform</a></li>
                                <li><a href="../classes/Trimesh.html">Trimesh</a></li>
                                <li><a href="../classes/TupleDictionary.html">TupleDictionary</a></li>
                                <li><a href="../classes/Vec3.html">Vec3</a></li>
                                <li><a href="../classes/Vec3Pool.html">Vec3Pool</a></li>
                                <li><a href="../classes/WheelInfo.html">WheelInfo</a></li>
                                <li><a href="../classes/World.html">World</a></li>
                            </ul>
                
                            <ul id="api-modules" class="apis modules">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="yui3-u-3-4">
                <div id="api-options">
                    Show:
                    <label for="api-show-inherited">
                        <input type="checkbox" id="api-show-inherited" checked>
                        Inherited
                    </label>
            
                    <label for="api-show-protected">
                        <input type="checkbox" id="api-show-protected">
                        Protected
                    </label>
            
                    <label for="api-show-private">
                        <input type="checkbox" id="api-show-private">
                        Private
                    </label>
                    <label for="api-show-deprecated">
                        <input type="checkbox" id="api-show-deprecated">
                        Deprecated
                    </label>
            
                </div>
            
            <div class="apidocs">
                <div id="docs-main">
                    <div class="content">
<h1>Spring Class</h1>
<div class="box meta">


        <div class="foundat">
            Defined in: <a href="../files/src_objects_Spring.js.html#l5"><code>src&#x2F;objects&#x2F;Spring.js:5</code></a>
        </div>


</div>


<div class="box intro">
    <p>A spring, connecting two bodies.</p>

</div>

    <div class="constructor">
        <h2>Constructor</h2>
        <div id="method_Spring" class="method item">
            <h3 class="name"><code>Spring</code></h3>
        
                <div class="args">
                    <span class="paren">(</span><ul class="args-list inline commas">
                        <li class="arg">
                                <code>bodyA</code>
                        </li>
                        <li class="arg">
                                <code>bodyB</code>
                        </li>
                        <li class="arg">
                                <code class="optional">[options]</code>
                        </li>
                    </ul><span class="paren">)</span>
                </div>
        
        
        
        
        
        
        
        
            <div class="meta">
                        <p>
                        Defined in
                <a href="../files/src_objects_Spring.js.html#l5"><code>src&#x2F;objects&#x2F;Spring.js:5</code></a>
                </p>
        
        
        
            </div>
        
            <div class="description">
                
            </div>
        
                <div class="params">
                    <h4>Parameters:</h4>
        
                    <ul class="params-list">
                        <li class="param">
                                <code class="param-name">bodyA</code>
                                <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>
        
        
                            <div class="param-description">
                                 
                            </div>
        
                        </li>
                        <li class="param">
                                <code class="param-name">bodyB</code>
                                <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>
        
        
                            <div class="param-description">
                                 
                            </div>
        
                        </li>
                        <li class="param">
                                <code class="param-name optional">[options]</code>
                                <span class="type">Object</span>
                                <span class="flag optional" title="This parameter is optional.">optional</span>
        
        
                            <div class="param-description">
                                 
                            </div>
        
                                <ul class="params-list">
                                    <li class="param">
                                            <code class="param-name optional">[restLength]</code>
                                            <span class="type">Number</span>
                                            <span class="flag optional" title="This parameter is optional.">optional</span>
        
                                        <div class="param-description">
                                            <p>A number &gt; 0. Default: 1</p>
        
                                        </div>
        
                                    </li>
                                    <li class="param">
                                            <code class="param-name optional">[stiffness]</code>
                                            <span class="type">Number</span>
                                            <span class="flag optional" title="This parameter is optional.">optional</span>
        
                                        <div class="param-description">
                                            <p>A number &gt;= 0. Default: 100</p>
        
                                        </div>
        
                                    </li>
                                    <li class="param">
                                            <code class="param-name optional">[damping]</code>
                                            <span class="type">Number</span>
                                            <span class="flag optional" title="This parameter is optional.">optional</span>
        
                                        <div class="param-description">
                                            <p>A number &gt;= 0. Default: 1</p>
        
                                        </div>
        
                                    </li>
                                    <li class="param">
                                            <code class="param-name optional">[worldAnchorA]</code>
                                            <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
                                            <span class="flag optional" title="This parameter is optional.">optional</span>
        
                                        <div class="param-description">
                                            <p>Where to hook the spring to body A, in world coordinates.</p>
        
                                        </div>
        
                                    </li>
                                    <li class="param">
                                            <code class="param-name optional">[worldAnchorB]</code>
                                            <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
                                            <span class="flag optional" title="This parameter is optional.">optional</span>
        
                                        <div class="param-description">
                                            
                                        </div>
        
                                    </li>
                                    <li class="param">
                                            <code class="param-name optional">[localAnchorA]</code>
                                            <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
                                            <span class="flag optional" title="This parameter is optional.">optional</span>
        
                                        <div class="param-description">
                                            <p>Where to hook the spring to body A, in local body coordinates.</p>
        
                                        </div>
        
                                    </li>
                                    <li class="param">
                                            <code class="param-name optional">[localAnchorB]</code>
                                            <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
                                            <span class="flag optional" title="This parameter is optional.">optional</span>
        
                                        <div class="param-description">
                                            
                                        </div>
        
                                    </li>
                                </ul>
                        </li>
                    </ul>
                </div>
        
        
        
        </div>
    </div>

<div id="classdocs" class="tabview">
    <ul class="api-class-tabs">
        <li class="api-class-tab index"><a href="#index">Index</a></li>

            <li class="api-class-tab methods"><a href="#methods">Methods</a></li>
            <li class="api-class-tab properties"><a href="#properties">Properties</a></li>
    </ul>

    <div>
        <div id="index" class="api-class-tabpanel index">
            <h2 class="off-left">Item Index</h2>

                <div class="index-section methods">
                    <h3>Methods</h3>

                    <ul class="index-list methods">
                            <li class="index-item method">
                                <a href="#method_applyForce">applyForce</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_getWorldAnchorA">getWorldAnchorA</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_getWorldAnchorB">getWorldAnchorB</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_setWorldAnchorA">setWorldAnchorA</a>

                            </li>
                            <li class="index-item method">
                                <a href="#method_setWorldAnchorB">setWorldAnchorB</a>

                            </li>
                    </ul>
                </div>

                <div class="index-section properties">
                    <h3>Properties</h3>

                    <ul class="index-list properties">
                            <li class="index-item property">
                                <a href="#property_bodyA">bodyA</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_bodyB">bodyB</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_damping">damping</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_localAnchorA">localAnchorA</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_localAnchorB">localAnchorB</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_restLength">restLength</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_stiffness">stiffness</a>

                            </li>
                    </ul>
                </div>


        </div>

            <div id="methods" class="api-class-tabpanel">
                <h2 class="off-left">Methods</h2>

<div id="method_applyForce" class="method item">
    <h3 class="name"><code>applyForce</code></h3>

        <span class="paren">()</span>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_objects_Spring.js.html#l135"><code>src&#x2F;objects&#x2F;Spring.js:135</code></a>
        </p>



    </div>

    <div class="description">
        <p>Apply the spring force to the connected bodies.</p>

    </div>




</div>
<div id="method_getWorldAnchorA" class="method item">
    <h3 class="name"><code>getWorldAnchorA</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>result</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_objects_Spring.js.html#l105"><code>src&#x2F;objects&#x2F;Spring.js:105</code></a>
        </p>



    </div>

    <div class="description">
        <p>Get the anchor point on body A, in world coordinates.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">result</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                        <p>The vector to store the result in.</p>

                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_getWorldAnchorB" class="method item">
    <h3 class="name"><code>getWorldAnchorB</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>result</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_objects_Spring.js.html#l114"><code>src&#x2F;objects&#x2F;Spring.js:114</code></a>
        </p>



    </div>

    <div class="description">
        <p>Get the anchor point on body B, in world coordinates.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">result</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                        <p>The vector to store the result in.</p>

                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_setWorldAnchorA" class="method item">
    <h3 class="name"><code>setWorldAnchorA</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>worldAnchorA</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_objects_Spring.js.html#l87"><code>src&#x2F;objects&#x2F;Spring.js:87</code></a>
        </p>



    </div>

    <div class="description">
        <p>Set the anchor point on body A, using world coordinates.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">worldAnchorA</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_setWorldAnchorB" class="method item">
    <h3 class="name"><code>setWorldAnchorB</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>worldAnchorB</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>
                Defined in
        <a href="../files/src_objects_Spring.js.html#l96"><code>src&#x2F;objects&#x2F;Spring.js:96</code></a>
        </p>



    </div>

    <div class="description">
        <p>Set the anchor point on body B, using world coordinates.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">worldAnchorB</code>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
            </div>

            <div id="properties" class="api-class-tabpanel">
                <h2 class="off-left">Properties</h2>

                    <div id="property_bodyA" class="property item">
                        <h3 class="name"><code>bodyA</code></h3>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Spring.js.html#l45"><code>src&#x2F;objects&#x2F;Spring.js:45</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>First connected body.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_bodyB" class="property item">
                        <h3 class="name"><code>bodyB</code></h3>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Spring.js.html#l52"><code>src&#x2F;objects&#x2F;Spring.js:52</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Second connected body.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_damping" class="property item">
                        <h3 class="name"><code>damping</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Spring.js.html#l38"><code>src&#x2F;objects&#x2F;Spring.js:38</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Damping of the spring.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_localAnchorA" class="property item">
                        <h3 class="name"><code>localAnchorA</code></h3>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Spring.js.html#l59"><code>src&#x2F;objects&#x2F;Spring.js:59</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Anchor for bodyA in local bodyA coordinates.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_localAnchorB" class="property item">
                        <h3 class="name"><code>localAnchorB</code></h3>
                        <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Spring.js.html#l66"><code>src&#x2F;objects&#x2F;Spring.js:66</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Anchor for bodyB in local bodyB coordinates.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_restLength" class="property item">
                        <h3 class="name"><code>restLength</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Spring.js.html#l24"><code>src&#x2F;objects&#x2F;Spring.js:24</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Rest length of the spring.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_stiffness" class="property item">
                        <h3 class="name"><code>stiffness</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_objects_Spring.js.html#l31"><code>src&#x2F;objects&#x2F;Spring.js:31</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Stiffness of the spring.</p>
                    
                        </div>
                    
                    
                    
                    </div>
            </div>


    </div>
</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="../assets/vendor/prettify/prettify-min.js"></script>
<script>prettyPrint();</script>
<script src="../assets/js/yui-prettify.js"></script>
<script src="../assets/../api.js"></script>
<script src="../assets/js/api-filter.js"></script>
<script src="../assets/js/api-list.js"></script>
<script src="../assets/js/api-search.js"></script>
<script src="../assets/js/apidocs.js"></script>
</body>
</html>
