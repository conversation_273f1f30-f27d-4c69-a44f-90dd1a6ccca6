<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>GridBroadphase - cannon</title>
    <link rel="stylesheet" href="http://yui.yahooapis.com/3.9.1/build/cssgrids/cssgrids-min.css">
    <link rel="stylesheet" href="../assets/vendor/prettify/prettify-min.css">
    <link rel="stylesheet" href="../assets/css/main.css" id="site_styles">
    <link rel="icon" href="../assets/favicon.ico">
    <script src="http://yui.yahooapis.com/combo?3.9.1/build/yui/yui-min.js"></script>
</head>
<body class="yui3-skin-sam">

<div id="doc">
    <div id="hd" class="yui3-g header">
        <div class="yui3-u-3-4">
                <h1><img src="../assets/css/logo.png" title="cannon" width="117" height="52"></h1>
        </div>
        <div class="yui3-u-1-4 version">
            <em>API Docs for: 0.6.1</em>
        </div>
    </div>
    <div id="bd" class="yui3-g">

        <div class="yui3-u-1-4">
            <div id="docs-sidebar" class="sidebar apidocs">
                <div id="api-list">
                    <h2 class="off-left">APIs</h2>
                    <div id="api-tabview" class="tabview">
                        <ul class="tabs">
                            <li><a href="#api-classes">Classes</a></li>
                            <li><a href="#api-modules">Modules</a></li>
                        </ul>
                
                        <div id="api-tabview-filter">
                            <input type="search" id="api-filter" placeholder="Type to filter APIs">
                        </div>
                
                        <div id="api-tabview-panel">
                            <ul id="api-classes" class="apis classes">
                                <li><a href="../classes/AABB.html">AABB</a></li>
                                <li><a href="../classes/ArrayCollisionMatrix.html">ArrayCollisionMatrix</a></li>
                                <li><a href="../classes/Body.html">Body</a></li>
                                <li><a href="../classes/Box.html">Box</a></li>
                                <li><a href="../classes/Broadphase.html">Broadphase</a></li>
                                <li><a href="../classes/ConeEquation.html">ConeEquation</a></li>
                                <li><a href="../classes/ConeTwistConstraint.html">ConeTwistConstraint</a></li>
                                <li><a href="../classes/Constraint.html">Constraint</a></li>
                                <li><a href="../classes/ContactEquation.html">ContactEquation</a></li>
                                <li><a href="../classes/ContactMaterial.html">ContactMaterial</a></li>
                                <li><a href="../classes/ConvexPolyhedron.html">ConvexPolyhedron</a></li>
                                <li><a href="../classes/Cylinder.html">Cylinder</a></li>
                                <li><a href="../classes/Demo.html">Demo</a></li>
                                <li><a href="../classes/DistanceConstraint.html">DistanceConstraint</a></li>
                                <li><a href="../classes/Equation.html">Equation</a></li>
                                <li><a href="../classes/EventTarget.html">EventTarget</a></li>
                                <li><a href="../classes/FrictionEquation.html">FrictionEquation</a></li>
                                <li><a href="../classes/GridBroadphase.html">GridBroadphase</a></li>
                                <li><a href="../classes/GSSolver.html">GSSolver</a></li>
                                <li><a href="../classes/Heightfield.html">Heightfield</a></li>
                                <li><a href="../classes/HingeConstraint.html">HingeConstraint</a></li>
                                <li><a href="../classes/JacobianElement.html">JacobianElement</a></li>
                                <li><a href="../classes/LockConstraint.html">LockConstraint</a></li>
                                <li><a href="../classes/Mat3.html">Mat3</a></li>
                                <li><a href="../classes/Material.html">Material</a></li>
                                <li><a href="../classes/NaiveBroadphase.html">NaiveBroadphase</a></li>
                                <li><a href="../classes/Narrowphase.html">Narrowphase</a></li>
                                <li><a href="../classes/ObjectCollisionMatrix.html">ObjectCollisionMatrix</a></li>
                                <li><a href="../classes/Octree.html">Octree</a></li>
                                <li><a href="../classes/OctreeNode.html">OctreeNode</a></li>
                                <li><a href="../classes/Particle.html">Particle</a></li>
                                <li><a href="../classes/Plane.html">Plane</a></li>
                                <li><a href="../classes/PointToPointConstraint.html">PointToPointConstraint</a></li>
                                <li><a href="../classes/Pool.html">Pool</a></li>
                                <li><a href="../classes/Quaternion.html">Quaternion</a></li>
                                <li><a href="../classes/Ray.html">Ray</a></li>
                                <li><a href="../classes/RaycastResult.html">RaycastResult</a></li>
                                <li><a href="../classes/RaycastVehicle.html">RaycastVehicle</a></li>
                                <li><a href="../classes/RigidVehicle.html">RigidVehicle</a></li>
                                <li><a href="../classes/RotationalEquation.html">RotationalEquation</a></li>
                                <li><a href="../classes/RotationalMotorEquation.html">RotationalMotorEquation</a></li>
                                <li><a href="../classes/SAPBroadphase.html">SAPBroadphase</a></li>
                                <li><a href="../classes/Shape.html">Shape</a></li>
                                <li><a href="../classes/Solver.html">Solver</a></li>
                                <li><a href="../classes/Sphere.html">Sphere</a></li>
                                <li><a href="../classes/SPHSystem.html">SPHSystem</a></li>
                                <li><a href="../classes/SplitSolver.html">SplitSolver</a></li>
                                <li><a href="../classes/Spring.html">Spring</a></li>
                                <li><a href="../classes/Transform.html">Transform</a></li>
                                <li><a href="../classes/Trimesh.html">Trimesh</a></li>
                                <li><a href="../classes/TupleDictionary.html">TupleDictionary</a></li>
                                <li><a href="../classes/Vec3.html">Vec3</a></li>
                                <li><a href="../classes/Vec3Pool.html">Vec3Pool</a></li>
                                <li><a href="../classes/WheelInfo.html">WheelInfo</a></li>
                                <li><a href="../classes/World.html">World</a></li>
                            </ul>
                
                            <ul id="api-modules" class="apis modules">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="yui3-u-3-4">
                <div id="api-options">
                    Show:
                    <label for="api-show-inherited">
                        <input type="checkbox" id="api-show-inherited" checked>
                        Inherited
                    </label>
            
                    <label for="api-show-protected">
                        <input type="checkbox" id="api-show-protected">
                        Protected
                    </label>
            
                    <label for="api-show-private">
                        <input type="checkbox" id="api-show-private">
                        Private
                    </label>
                    <label for="api-show-deprecated">
                        <input type="checkbox" id="api-show-deprecated">
                        Deprecated
                    </label>
            
                </div>
            
            <div class="apidocs">
                <div id="docs-main">
                    <div class="content">
<h1>GridBroadphase Class</h1>
<div class="box meta">

        <div class="extends">
            Extends <a href="../classes/Broadphase.html" class="crosslink">Broadphase</a>
        </div>

        <div class="foundat">
            Defined in: <a href="../files/src_collision_GridBroadphase.js.html#l7"><code>src&#x2F;collision&#x2F;GridBroadphase.js:7</code></a>
        </div>


</div>


<div class="box intro">
    <p>Axis aligned uniform grid broadphase.</p>

</div>

    <div class="constructor">
        <h2>Constructor</h2>
        <div id="method_GridBroadphase" class="method item">
            <h3 class="name"><code>GridBroadphase</code></h3>
        
                <div class="args">
                    <span class="paren">(</span><ul class="args-list inline commas">
                        <li class="arg">
                                <code>aabbMin</code>
                        </li>
                        <li class="arg">
                                <code>aabbMax</code>
                        </li>
                        <li class="arg">
                                <code>nx</code>
                        </li>
                        <li class="arg">
                                <code>ny</code>
                        </li>
                        <li class="arg">
                                <code>nz</code>
                        </li>
                    </ul><span class="paren">)</span>
                </div>
        
        
        
        
        
        
        
        
            <div class="meta">
                        <p>
                        Defined in
                <a href="../files/src_collision_GridBroadphase.js.html#l7"><code>src&#x2F;collision&#x2F;GridBroadphase.js:7</code></a>
                </p>
        
        
        
            </div>
        
            <div class="description">
                
            </div>
        
                <div class="params">
                    <h4>Parameters:</h4>
        
                    <ul class="params-list">
                        <li class="param">
                                <code class="param-name">aabbMin</code>
                                <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
        
        
                            <div class="param-description">
                                 
                            </div>
        
                        </li>
                        <li class="param">
                                <code class="param-name">aabbMax</code>
                                <span class="type"><a href="../classes/Vec3.html" class="crosslink">Vec3</a></span>
        
        
                            <div class="param-description">
                                 
                            </div>
        
                        </li>
                        <li class="param">
                                <code class="param-name">nx</code>
                                <span class="type">Number</span>
        
        
                            <div class="param-description">
                                <p>Number of boxes along x</p>
        
                            </div>
        
                        </li>
                        <li class="param">
                                <code class="param-name">ny</code>
                                <span class="type">Number</span>
        
        
                            <div class="param-description">
                                <p>Number of boxes along y</p>
        
                            </div>
        
                        </li>
                        <li class="param">
                                <code class="param-name">nz</code>
                                <span class="type">Number</span>
        
        
                            <div class="param-description">
                                <p>Number of boxes along z</p>
        
                            </div>
        
                        </li>
                    </ul>
                </div>
        
        
        
        </div>
    </div>

<div id="classdocs" class="tabview">
    <ul class="api-class-tabs">
        <li class="api-class-tab index"><a href="#index">Index</a></li>

            <li class="api-class-tab methods"><a href="#methods">Methods</a></li>
            <li class="api-class-tab properties"><a href="#properties">Properties</a></li>
    </ul>

    <div>
        <div id="index" class="api-class-tabpanel index">
            <h2 class="off-left">Item Index</h2>

                <div class="index-section methods">
                    <h3>Methods</h3>

                    <ul class="index-list methods extends">
                            <li class="index-item method inherited">
                                <a href="#method_aabbQuery">aabbQuery</a>

                            </li>
                            <li class="index-item method inherited">
                                <a href="#method_boundingSphereCheck">boundingSphereCheck</a>

                            </li>
                            <li class="index-item method inherited">
                                <a href="#method_collisionPairs">collisionPairs</a>

                            </li>
                            <li class="index-item method inherited">
                                <a href="#method_doBoundingBoxBroadphase">doBoundingBoxBroadphase</a>

                            </li>
                            <li class="index-item method inherited">
                                <a href="#method_doBoundingSphereBroadphase">doBoundingSphereBroadphase</a>

                            </li>
                            <li class="index-item method inherited">
                                <a href="#method_intersectionTest">intersectionTest</a>

                            </li>
                            <li class="index-item method inherited">
                                <a href="#method_makePairsUnique">makePairsUnique</a>

                            </li>
                            <li class="index-item method inherited">
                                <a href="#method_needBroadphaseCollision">needBroadphaseCollision</a>

                            </li>
                            <li class="index-item method inherited">
                                <a href="#method_setWorld">setWorld</a>

                            </li>
                    </ul>
                </div>

                <div class="index-section properties">
                    <h3>Properties</h3>

                    <ul class="index-list properties extends">
                            <li class="index-item property inherited">
                                <a href="#property_dirty">dirty</a>

                            </li>
                            <li class="index-item property inherited">
                                <a href="#property_useBoundingBoxes">useBoundingBoxes</a>

                            </li>
                            <li class="index-item property inherited">
                                <a href="#property_world">world</a>

                            </li>
                    </ul>
                </div>


        </div>

            <div id="methods" class="api-class-tabpanel">
                <h2 class="off-left">Methods</h2>

<div id="method_aabbQuery" class="method item inherited">
    <h3 class="name"><code>aabbQuery</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>world</code>
                </li>
                <li class="arg">
                        <code>aabb</code>
                </li>
                <li class="arg">
                        <code>result</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type">Array</span>
        </span>







    <div class="meta">
                <p>Inherited from
                <a href="../classes/Broadphase.html#method_aabbQuery">Broadphase</a>:
        <a href="../files/src_collision_Broadphase.js.html#l197"><code>src&#x2F;collision&#x2F;Broadphase.js:197</code></a>
        </p>



    </div>

    <div class="description">
        <p>Returns all the bodies within the AABB.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">world</code>
                        <span class="type"><a href="../classes/World.html" class="crosslink">World</a></span>


                    <div class="param-description">
                        
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">aabb</code>
                        <span class="type"><a href="../classes/AABB.html" class="crosslink">AABB</a></span>


                    <div class="param-description">
                        
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">result</code>
                        <span class="type">Array</span>


                    <div class="param-description">
                        <p>An array to store resulting bodies in.</p>

                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type">Array</span>:
            </div>
        </div>


</div>
<div id="method_boundingSphereCheck" class="method item inherited">
    <h3 class="name"><code>boundingSphereCheck</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>bodyA</code>
                </li>
                <li class="arg">
                        <code>bodyB</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type">Boolean</span>
        </span>







    <div class="meta">
                <p>Inherited from
                <a href="../classes/Broadphase.html#method_boundingSphereCheck">Broadphase</a>:
        <a href="../files/src_collision_Broadphase.js.html#l183"><code>src&#x2F;collision&#x2F;Broadphase.js:183</code></a>
        </p>



    </div>

    <div class="description">
        <p>Check if the bounding spheres of two bodies overlap.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">bodyA</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                        
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">bodyB</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                        
                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type">Boolean</span>:
            </div>
        </div>


</div>
<div id="method_collisionPairs" class="method item">
    <h3 class="name"><code>collisionPairs</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>world</code>
                </li>
                <li class="arg">
                        <code>pairs1</code>
                </li>
                <li class="arg">
                        <code>pairs2</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
            <p>Inherited from
            <a href="../classes/Broadphase.html#method_collisionPairs">
                Broadphase
            </a>
            but overwritten in
        <a href="../files/src_collision_GridBroadphase.js.html#l42"><code>src&#x2F;collision&#x2F;GridBroadphase.js:42</code></a>
        </p>



    </div>

    <div class="description">
        <p>Get all the collision pairs in the physics world</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">world</code>
                        <span class="type"><a href="../classes/World.html" class="crosslink">World</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">pairs1</code>
                        <span class="type">Array</span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">pairs2</code>
                        <span class="type">Array</span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_doBoundingBoxBroadphase" class="method item inherited">
    <h3 class="name"><code>doBoundingBoxBroadphase</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>bodyA</code>
                </li>
                <li class="arg">
                        <code>bodyB</code>
                </li>
                <li class="arg">
                        <code>pairs1</code>
                </li>
                <li class="arg">
                        <code>pairs2</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>Inherited from
                <a href="../classes/Broadphase.html#method_doBoundingBoxBroadphase">Broadphase</a>:
        <a href="../files/src_collision_Broadphase.js.html#l112"><code>src&#x2F;collision&#x2F;Broadphase.js:112</code></a>
        </p>



    </div>

    <div class="description">
        <p>Check if the bounding boxes of two bodies are intersecting.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">bodyA</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                        
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">bodyB</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                        
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">pairs1</code>
                        <span class="type">Array</span>


                    <div class="param-description">
                        
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">pairs2</code>
                        <span class="type">Array</span>


                    <div class="param-description">
                        
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_doBoundingSphereBroadphase" class="method item inherited">
    <h3 class="name"><code>doBoundingSphereBroadphase</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>bodyA</code>
                </li>
                <li class="arg">
                        <code>bodyB</code>
                </li>
                <li class="arg">
                        <code>pairs1</code>
                </li>
                <li class="arg">
                        <code>pairs2</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>Inherited from
                <a href="../classes/Broadphase.html#method_doBoundingSphereBroadphase">Broadphase</a>:
        <a href="../files/src_collision_Broadphase.js.html#l89"><code>src&#x2F;collision&#x2F;Broadphase.js:89</code></a>
        </p>



    </div>

    <div class="description">
        <p>Check if the bounding spheres of two bodies are intersecting.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">bodyA</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                        
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">bodyB</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                        
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">pairs1</code>
                        <span class="type">Array</span>


                    <div class="param-description">
                        <p>bodyA is appended to this array if intersection</p>

                    </div>

                </li>
                <li class="param">
                        <code class="param-name">pairs2</code>
                        <span class="type">Array</span>


                    <div class="param-description">
                        <p>bodyB is appended to this array if intersection</p>

                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_intersectionTest" class="method item inherited">
    <h3 class="name"><code>intersectionTest</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>bodyA</code>
                </li>
                <li class="arg">
                        <code>bodyB</code>
                </li>
                <li class="arg">
                        <code>pairs1</code>
                </li>
                <li class="arg">
                        <code>pairs2</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>Inherited from
                <a href="../classes/Broadphase.html#method_intersectionTest">Broadphase</a>:
        <a href="../files/src_collision_Broadphase.js.html#l73"><code>src&#x2F;collision&#x2F;Broadphase.js:73</code></a>
        </p>



    </div>

    <div class="description">
        <p>Check if the bounding volumes of two bodies intersect.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">bodyA</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                        
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">bodyB</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                        
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">pairs1</code>
                        <span class="type">Array</span>


                    <div class="param-description">
                        
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">pairs2</code>
                        <span class="type">Array</span>


                    <div class="param-description">
                        
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_makePairsUnique" class="method item inherited">
    <h3 class="name"><code>makePairsUnique</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>pairs1</code>
                </li>
                <li class="arg">
                        <code>pairs2</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>Inherited from
                <a href="../classes/Broadphase.html#method_makePairsUnique">Broadphase</a>:
        <a href="../files/src_collision_Broadphase.js.html#l135"><code>src&#x2F;collision&#x2F;Broadphase.js:135</code></a>
        </p>



    </div>

    <div class="description">
        <p>Removes duplicate pairs from the pair arrays.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">pairs1</code>
                        <span class="type">Array</span>


                    <div class="param-description">
                        
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">pairs2</code>
                        <span class="type">Array</span>


                    <div class="param-description">
                        
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_needBroadphaseCollision" class="method item inherited">
    <h3 class="name"><code>needBroadphaseCollision</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>bodyA</code>
                </li>
                <li class="arg">
                        <code>bodyB</code>
                </li>
            </ul><span class="paren">)</span>
        </div>

        <span class="returns-inline">
            <span class="type">Bool</span>
        </span>







    <div class="meta">
                <p>Inherited from
                <a href="../classes/Broadphase.html#method_needBroadphaseCollision">Broadphase</a>:
        <a href="../files/src_collision_Broadphase.js.html#l48"><code>src&#x2F;collision&#x2F;Broadphase.js:48</code></a>
        </p>



    </div>

    <div class="description">
        <p>Check if a body pair needs to be intersection tested at all.</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">bodyA</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                        
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">bodyB</code>
                        <span class="type"><a href="../classes/Body.html" class="crosslink">Body</a></span>


                    <div class="param-description">
                        
                    </div>

                </li>
            </ul>
        </div>

        <div class="returns">
            <h4>Returns:</h4>

            <div class="returns-description">
                        <span class="type">Bool</span>:
            </div>
        </div>


</div>
<div id="method_setWorld" class="method item inherited">
    <h3 class="name"><code>setWorld</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>world</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>Inherited from
                <a href="../classes/Broadphase.html#method_setWorld">Broadphase</a>:
        <a href="../files/src_collision_Broadphase.js.html#l175"><code>src&#x2F;collision&#x2F;Broadphase.js:175</code></a>
        </p>



    </div>

    <div class="description">
        <p>To be implemented by subcasses</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">world</code>
                        <span class="type"><a href="../classes/World.html" class="crosslink">World</a></span>


                    <div class="param-description">
                        
                    </div>

                </li>
            </ul>
        </div>



</div>
            </div>

            <div id="properties" class="api-class-tabpanel">
                <h2 class="off-left">Properties</h2>

                    <div id="property_dirty" class="property item inherited">
                        <h3 class="name"><code>dirty</code></h3>
                        <span class="type">Boolean</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>Inherited from
                                    <a href="../classes/Broadphase.html#property_dirty">Broadphase</a>:
                            <a href="../files/src_collision_Broadphase.js.html#l30"><code>src&#x2F;collision&#x2F;Broadphase.js:30</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>Set to true if the objects in the world moved.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_useBoundingBoxes" class="property item inherited">
                        <h3 class="name"><code>useBoundingBoxes</code></h3>
                        <span class="type">Boolean</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>Inherited from
                                    <a href="../classes/Broadphase.html#property_useBoundingBoxes">Broadphase</a>:
                            <a href="../files/src_collision_Broadphase.js.html#l23"><code>src&#x2F;collision&#x2F;Broadphase.js:23</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>If set to true, the broadphase uses bounding boxes for intersection test, else it uses bounding spheres.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_world" class="property item inherited">
                        <h3 class="name"><code>world</code></h3>
                        <span class="type"><a href="../classes/World.html" class="crosslink">World</a></span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>Inherited from
                                    <a href="../classes/Broadphase.html#property_world">Broadphase</a>:
                            <a href="../files/src_collision_Broadphase.js.html#l16"><code>src&#x2F;collision&#x2F;Broadphase.js:16</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>The world to search for collisions in.</p>
                    
                        </div>
                    
                    
                    
                    </div>
            </div>


    </div>
</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="../assets/vendor/prettify/prettify-min.js"></script>
<script>prettyPrint();</script>
<script src="../assets/js/yui-prettify.js"></script>
<script src="../assets/../api.js"></script>
<script src="../assets/js/api-filter.js"></script>
<script src="../assets/js/api-list.js"></script>
<script src="../assets/js/api-search.js"></script>
<script src="../assets/js/apidocs.js"></script>
</body>
</html>
