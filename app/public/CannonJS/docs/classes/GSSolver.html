<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>GSSolver - cannon</title>
    <link rel="stylesheet" href="http://yui.yahooapis.com/3.9.1/build/cssgrids/cssgrids-min.css">
    <link rel="stylesheet" href="../assets/vendor/prettify/prettify-min.css">
    <link rel="stylesheet" href="../assets/css/main.css" id="site_styles">
    <link rel="icon" href="../assets/favicon.ico">
    <script src="http://yui.yahooapis.com/combo?3.9.1/build/yui/yui-min.js"></script>
</head>
<body class="yui3-skin-sam">

<div id="doc">
    <div id="hd" class="yui3-g header">
        <div class="yui3-u-3-4">
                <h1><img src="../assets/css/logo.png" title="cannon" width="117" height="52"></h1>
        </div>
        <div class="yui3-u-1-4 version">
            <em>API Docs for: 0.6.1</em>
        </div>
    </div>
    <div id="bd" class="yui3-g">

        <div class="yui3-u-1-4">
            <div id="docs-sidebar" class="sidebar apidocs">
                <div id="api-list">
                    <h2 class="off-left">APIs</h2>
                    <div id="api-tabview" class="tabview">
                        <ul class="tabs">
                            <li><a href="#api-classes">Classes</a></li>
                            <li><a href="#api-modules">Modules</a></li>
                        </ul>
                
                        <div id="api-tabview-filter">
                            <input type="search" id="api-filter" placeholder="Type to filter APIs">
                        </div>
                
                        <div id="api-tabview-panel">
                            <ul id="api-classes" class="apis classes">
                                <li><a href="../classes/AABB.html">AABB</a></li>
                                <li><a href="../classes/ArrayCollisionMatrix.html">ArrayCollisionMatrix</a></li>
                                <li><a href="../classes/Body.html">Body</a></li>
                                <li><a href="../classes/Box.html">Box</a></li>
                                <li><a href="../classes/Broadphase.html">Broadphase</a></li>
                                <li><a href="../classes/ConeEquation.html">ConeEquation</a></li>
                                <li><a href="../classes/ConeTwistConstraint.html">ConeTwistConstraint</a></li>
                                <li><a href="../classes/Constraint.html">Constraint</a></li>
                                <li><a href="../classes/ContactEquation.html">ContactEquation</a></li>
                                <li><a href="../classes/ContactMaterial.html">ContactMaterial</a></li>
                                <li><a href="../classes/ConvexPolyhedron.html">ConvexPolyhedron</a></li>
                                <li><a href="../classes/Cylinder.html">Cylinder</a></li>
                                <li><a href="../classes/Demo.html">Demo</a></li>
                                <li><a href="../classes/DistanceConstraint.html">DistanceConstraint</a></li>
                                <li><a href="../classes/Equation.html">Equation</a></li>
                                <li><a href="../classes/EventTarget.html">EventTarget</a></li>
                                <li><a href="../classes/FrictionEquation.html">FrictionEquation</a></li>
                                <li><a href="../classes/GridBroadphase.html">GridBroadphase</a></li>
                                <li><a href="../classes/GSSolver.html">GSSolver</a></li>
                                <li><a href="../classes/Heightfield.html">Heightfield</a></li>
                                <li><a href="../classes/HingeConstraint.html">HingeConstraint</a></li>
                                <li><a href="../classes/JacobianElement.html">JacobianElement</a></li>
                                <li><a href="../classes/LockConstraint.html">LockConstraint</a></li>
                                <li><a href="../classes/Mat3.html">Mat3</a></li>
                                <li><a href="../classes/Material.html">Material</a></li>
                                <li><a href="../classes/NaiveBroadphase.html">NaiveBroadphase</a></li>
                                <li><a href="../classes/Narrowphase.html">Narrowphase</a></li>
                                <li><a href="../classes/ObjectCollisionMatrix.html">ObjectCollisionMatrix</a></li>
                                <li><a href="../classes/Octree.html">Octree</a></li>
                                <li><a href="../classes/OctreeNode.html">OctreeNode</a></li>
                                <li><a href="../classes/Particle.html">Particle</a></li>
                                <li><a href="../classes/Plane.html">Plane</a></li>
                                <li><a href="../classes/PointToPointConstraint.html">PointToPointConstraint</a></li>
                                <li><a href="../classes/Pool.html">Pool</a></li>
                                <li><a href="../classes/Quaternion.html">Quaternion</a></li>
                                <li><a href="../classes/Ray.html">Ray</a></li>
                                <li><a href="../classes/RaycastResult.html">RaycastResult</a></li>
                                <li><a href="../classes/RaycastVehicle.html">RaycastVehicle</a></li>
                                <li><a href="../classes/RigidVehicle.html">RigidVehicle</a></li>
                                <li><a href="../classes/RotationalEquation.html">RotationalEquation</a></li>
                                <li><a href="../classes/RotationalMotorEquation.html">RotationalMotorEquation</a></li>
                                <li><a href="../classes/SAPBroadphase.html">SAPBroadphase</a></li>
                                <li><a href="../classes/Shape.html">Shape</a></li>
                                <li><a href="../classes/Solver.html">Solver</a></li>
                                <li><a href="../classes/Sphere.html">Sphere</a></li>
                                <li><a href="../classes/SPHSystem.html">SPHSystem</a></li>
                                <li><a href="../classes/SplitSolver.html">SplitSolver</a></li>
                                <li><a href="../classes/Spring.html">Spring</a></li>
                                <li><a href="../classes/Transform.html">Transform</a></li>
                                <li><a href="../classes/Trimesh.html">Trimesh</a></li>
                                <li><a href="../classes/TupleDictionary.html">TupleDictionary</a></li>
                                <li><a href="../classes/Vec3.html">Vec3</a></li>
                                <li><a href="../classes/Vec3Pool.html">Vec3Pool</a></li>
                                <li><a href="../classes/WheelInfo.html">WheelInfo</a></li>
                                <li><a href="../classes/World.html">World</a></li>
                            </ul>
                
                            <ul id="api-modules" class="apis modules">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="yui3-u-3-4">
                <div id="api-options">
                    Show:
                    <label for="api-show-inherited">
                        <input type="checkbox" id="api-show-inherited" checked>
                        Inherited
                    </label>
            
                    <label for="api-show-protected">
                        <input type="checkbox" id="api-show-protected">
                        Protected
                    </label>
            
                    <label for="api-show-private">
                        <input type="checkbox" id="api-show-private">
                        Private
                    </label>
                    <label for="api-show-deprecated">
                        <input type="checkbox" id="api-show-deprecated">
                        Deprecated
                    </label>
            
                </div>
            
            <div class="apidocs">
                <div id="docs-main">
                    <div class="content">
<h1>GSSolver Class</h1>
<div class="box meta">

        <div class="extends">
            Extends <a href="../classes/Solver.html" class="crosslink">Solver</a>
        </div>

        <div class="foundat">
            Defined in: <a href="../files/src_solver_GSSolver.js.html#l7"><code>src&#x2F;solver&#x2F;GSSolver.js:7</code></a>
        </div>


</div>


<div class="box intro">
    <p>Constraint equation Gauss-Seidel solver.</p>

</div>

    <div class="constructor">
        <h2>Constructor</h2>
        <div id="method_GSSolver" class="method item">
            <h3 class="name"><code>GSSolver</code></h3>
        
                <span class="paren">()</span>
        
        
        
        
        
        
        
        
            <div class="meta">
                        <p>
                        Defined in
                <a href="../files/src_solver_GSSolver.js.html#l7"><code>src&#x2F;solver&#x2F;GSSolver.js:7</code></a>
                </p>
        
        
        
            </div>
        
            <div class="description">
                
            </div>
        
        
        
        
        </div>
    </div>

<div id="classdocs" class="tabview">
    <ul class="api-class-tabs">
        <li class="api-class-tab index"><a href="#index">Index</a></li>

            <li class="api-class-tab methods"><a href="#methods">Methods</a></li>
            <li class="api-class-tab properties"><a href="#properties">Properties</a></li>
    </ul>

    <div>
        <div id="index" class="api-class-tabpanel index">
            <h2 class="off-left">Item Index</h2>

                <div class="index-section methods">
                    <h3>Methods</h3>

                    <ul class="index-list methods extends">
                            <li class="index-item method inherited">
                                <a href="#method_addEquation">addEquation</a>

                            </li>
                            <li class="index-item method inherited">
                                <a href="#method_removeAllEquations">removeAllEquations</a>

                            </li>
                            <li class="index-item method inherited">
                                <a href="#method_removeEquation">removeEquation</a>

                            </li>
                            <li class="index-item method inherited">
                                <a href="#method_solve">solve</a>

                            </li>
                    </ul>
                </div>

                <div class="index-section properties">
                    <h3>Properties</h3>

                    <ul class="index-list properties extends">
                            <li class="index-item property inherited">
                                <a href="#property_equations">equations</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_iterations">iterations</a>

                            </li>
                            <li class="index-item property">
                                <a href="#property_tolerance">tolerance</a>

                            </li>
                    </ul>
                </div>


        </div>

            <div id="methods" class="api-class-tabpanel">
                <h2 class="off-left">Methods</h2>

<div id="method_addEquation" class="method item inherited">
    <h3 class="name"><code>addEquation</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>eq</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>Inherited from
                <a href="../classes/Solver.html#method_addEquation">Solver</a>:
        <a href="../files/src_solver_Solver.js.html#l28"><code>src&#x2F;solver&#x2F;Solver.js:28</code></a>
        </p>



    </div>

    <div class="description">
        <p>Add an equation</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">eq</code>
                        <span class="type"><a href="../classes/Equation.html" class="crosslink">Equation</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_removeAllEquations" class="method item inherited">
    <h3 class="name"><code>removeAllEquations</code></h3>

        <span class="paren">()</span>








    <div class="meta">
                <p>Inherited from
                <a href="../classes/Solver.html#method_removeAllEquations">Solver</a>:
        <a href="../files/src_solver_Solver.js.html#l52"><code>src&#x2F;solver&#x2F;Solver.js:52</code></a>
        </p>



    </div>

    <div class="description">
        <p>Add all equations</p>

    </div>




</div>
<div id="method_removeEquation" class="method item inherited">
    <h3 class="name"><code>removeEquation</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>eq</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>Inherited from
                <a href="../classes/Solver.html#method_removeEquation">Solver</a>:
        <a href="../files/src_solver_Solver.js.html#l39"><code>src&#x2F;solver&#x2F;Solver.js:39</code></a>
        </p>



    </div>

    <div class="description">
        <p>Remove an equation</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">eq</code>
                        <span class="type"><a href="../classes/Equation.html" class="crosslink">Equation</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
<div id="method_solve" class="method item inherited">
    <h3 class="name"><code>solve</code></h3>

        <div class="args">
            <span class="paren">(</span><ul class="args-list inline commas">
                <li class="arg">
                        <code>dt</code>
                </li>
                <li class="arg">
                        <code>world</code>
                </li>
            </ul><span class="paren">)</span>
        </div>








    <div class="meta">
                <p>Inherited from
                <a href="../classes/Solver.html#method_solve">Solver</a>:
        <a href="../files/src_solver_Solver.js.html#l17"><code>src&#x2F;solver&#x2F;Solver.js:17</code></a>
        </p>



    </div>

    <div class="description">
        <p>Should be implemented in subclasses!</p>

    </div>

        <div class="params">
            <h4>Parameters:</h4>

            <ul class="params-list">
                <li class="param">
                        <code class="param-name">dt</code>
                        <span class="type">Number</span>


                    <div class="param-description">
                         
                    </div>

                </li>
                <li class="param">
                        <code class="param-name">world</code>
                        <span class="type"><a href="../classes/World.html" class="crosslink">World</a></span>


                    <div class="param-description">
                         
                    </div>

                </li>
            </ul>
        </div>



</div>
            </div>

            <div id="properties" class="api-class-tabpanel">
                <h2 class="off-left">Properties</h2>

                    <div id="property_equations" class="property item inherited">
                        <h3 class="name"><code>equations</code></h3>
                        <span class="type">Array</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>Inherited from
                                    <a href="../classes/Solver.html#property_equations">Solver</a>:
                            <a href="../files/src_solver_Solver.js.html#l10"><code>src&#x2F;solver&#x2F;Solver.js:10</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>All equations to be solved</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_iterations" class="property item">
                        <h3 class="name"><code>iterations</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_solver_GSSolver.js.html#l19"><code>src&#x2F;solver&#x2F;GSSolver.js:19</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>The number of solver iterations determines quality of the constraints in the world. The more iterations, the more correct simulation. More iterations need more computations though. If you have a large gravity force in your world, you will need more iterations.</p>
                    
                        </div>
                    
                    
                    
                    </div>
                    <div id="property_tolerance" class="property item">
                        <h3 class="name"><code>tolerance</code></h3>
                        <span class="type">Number</span>
                    
                    
                    
                    
                    
                        <div class="meta">
                                    <p>
                                    Defined in
                            <a href="../files/src_solver_GSSolver.js.html#l27"><code>src&#x2F;solver&#x2F;GSSolver.js:27</code></a>
                            </p>
                    
                    
                        </div>
                    
                        <div class="description">
                            <p>When tolerance is reached, the system is assumed to be converged.</p>
                    
                        </div>
                    
                    
                    
                    </div>
            </div>


    </div>
</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="../assets/vendor/prettify/prettify-min.js"></script>
<script>prettyPrint();</script>
<script src="../assets/js/yui-prettify.js"></script>
<script src="../assets/../api.js"></script>
<script src="../assets/js/api-filter.js"></script>
<script src="../assets/js/api-list.js"></script>
<script src="../assets/js/api-search.js"></script>
<script src="../assets/js/apidocs.js"></script>
</body>
</html>
