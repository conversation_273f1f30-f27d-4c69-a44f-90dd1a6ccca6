<!DOCTYPE html>
<html>
<head>
  <meta charset='utf-8'>
  <title>schteppe/cannon.js @ GitHub</title>
  <meta name="description" content="A lightweight and simple 3D physics engine for the web." />
  <meta name="keywords" content="javascript,three.js,physics,ammo.js,engine" />
  <meta name="author" content="<PERSON> (schteppe)" />
  <style type="text/css">
    body {
      margin-top: 1.0em;
      background-color: white;
      font-family: Helvetica, Arial, FreeSans, san-serif;
      color: black;
    }
    #container {
      margin: 0 auto;
      width: 700px;
    }
    h1 { font-size: 3.8em; color: #a1925e; margin-bottom: 3px; }
    h1 .small { font-size: 0.4em; }
    h1 a { text-decoration: none }
    h2 { font-size: 1.5em; color: #a1925e; }
    h3 { text-align: center; color: #a1925e; }
    a { color: #a1925e; }
    .description { font-size: 1.2em; margin-bottom: 30px; margin-top: 30px; font-style: italic;}
    .download { float: right; }
    pre { background: #000; color: #fff; padding: 15px;}
    hr { border: 0; width: 80%; border-bottom: 1px solid #aaa}
    .footer { text-align:center; padding-top:10px; font-style: italic; }
    #examples img {
      max-height:150px;
      box-shadow:0px 0px 10px rgba(0,0,0,1);
      margin:10px;
    }
    .twitter-share-button {
        float:right;

    }
  </style>
</head>

<body>
  <a href="https://github.com/schteppe/cannon.js"><img style="position: absolute; top: 0; right: 0; border: 0;" src="http://s3.amazonaws.com/github/ribbons/forkme_right_darkblue_121621.png" alt="Fork me on GitHub" /></a>

  <div id="container">

    <div class="download">
      <a href="https://github.com/schteppe/cannon.js/zipball/master">
        <img border="0" width="90" src="https://github.com/images/modules/download/zip.png"></a>
      <a href="https://github.com/schteppe/cannon.js/tarball/master">
        <img border="0" width="90" src="https://github.com/images/modules/download/tar.png"></a>
    </div>

    <h1><a href="https://github.com/schteppe/cannon.js">cannon.js</a>
      <span class="small">by <a href="https://github.com/schteppe">schteppe</a></span></h1>

    <div class="description">
      A lightweight and simple 3D physics engine for the web.

        <a href="https://twitter.com/share" class="twitter-share-button" data-url="http://schteppe.github.com/cannon.js/" data-text="Cannon.js - Lightweight 3D javascript physics" data-via="schteppe" data-size="large" data-hashtags="cannonjs">Tweet</a>
        <script>!function(d,s,id){var js,fjs=d.getElementsByTagName(s)[0];if(!d.getElementById(id)){js=d.createElement(s);js.id=id;js.src="http://platform.twitter.com/widgets.js";fjs.parentNode.insertBefore(js,fjs);}}(document,"script","twitter-wjs");</script>
    </div>

    <h2>Documentation</h2>
    <p><a href="docs">Go to the documentation</a>.</p>

    <h2>Examples</h2>
    <p>
      Click on an image to show the corresponding example.
    </p>
    <div id="examples">
      <a href="examples/threejs_cloth.html"><img src="images/threejs_cloth.png" alt="Cloth"></a>
      <a href="examples/threejs_fps.html"><img src="images/fps.png" alt="Shooter"></a>
      <a href="examples/threejs_mousepick.html"><img src="images/threejs_mousepick.png" alt="Mousepick"></a>
      <a href="examples/threejs_voxel_fps.html"><img src="images/threejs_voxel_fps.png" alt="Voxel landscape"></a>
      <a href="examples/worker.html"><img src="images/worker.png" alt="Worker"></a>

      <a href="demos/bodyTypes.html"><img src="images/motionstates.png" alt="Body types"></a>
      <a href="demos/bounce.html"><img src="images/bounce.png" alt="Bounce"></a>
      <a href="demos/bunny.html"><img src="images/bunny.png" alt="Bunny"></a>
      <a href="demos/callbacks.html"><img src="images/callbacks.png" alt="Callbacks"></a>
      <a href="demos/collisionFilter.html"><img src="images/collisionFilter.png" alt="Collision filters"></a>
      <a href="demos/collisions.html"><img src="images/collisions.png" alt="Pair collisions"></a>
      <a href="demos/compound.html"><img src="images/compound.png" alt="Compound shapes"></a>
      <a href="demos/constraints.html"><img src="images/constraints.png" alt="Constraints"></a>
      <a href="demos/container.html"><img src="images/container2.png" alt="Container"></a>
      <a href="demos/convex.html"><img src="images/convexhull.png" alt="Convex"></a>
      <a href="demos/events.html"><img src="images/events.png" alt="Events"></a>
      <a href="demos/fixedRotation.html"><img src="images/fixedRotation.png" alt="Fixed rotation"></a>
      <a href="demos/friction.html"><img src="images/friction.png" alt="Friction"></a>
      <a href="demos/heightfield.html"><img src="images/heightfield.png" alt="Hinge"></a>
      <a href="demos/hinge.html"><img src="images/hinge.png" alt="Hinge"></a>
      <a href="demos/impulses.html"><img src="images/impulses.png" alt="Impulses"></a>
      <a href="demos/pile.html"><img src="images/pile.png" alt="Pile"></a>
      <a href="demos/raycastVehicle.html"><img src="images/raycastVehicle.png" alt="RaycastVehicle"></a>
      <a href="demos/rigidVehicle.html"><img src="images/rigidVehicle.png" alt="RigidVehicle"></a>
      <a href="demos/shapes.html"><img src="images/shapes.png" alt="Shapes"></a>
      <a href="demos/singleBodyOnPlane.html"><img src="images/single.png" alt="Single shape on a plane."></a>
      <a href="demos/sleep.html"><img src="images/sleep.png" alt="Sleep"></a>
      <a href="demos/sph.html"><img src="images/sph.png" alt="SPH fluid simulation"></a>
      <a href="demos/splitSolver.html"><img src="images/splitSolver.png" alt="SplitSolver"></a>
      <a href="demos/spring.html"><img src="images/spring.png" alt="Spring"></a>
      <a href="demos/stacks.html"><img src="images/stack.png" alt="Different types of stacks"></a>
    </div>

    <div class="footer">
        Read more and download the code at <a href="https://github.com/schteppe/cannon.js">github.com/schteppe/cannon.js</a>
    </div>

    <h2>What is cannon.js?</h2>
    <p>Inspired by <a href="https://github.com/mrdoob/three.js">three.js</a> and <a href="https://github.com/kripken/ammo.js">ammo.js</a>, and driven by the fact that the web lacks a physics engine, here comes <b>cannon.js</b>.</p>

    <h3>Features</h3>
    <ul>
      <li>Lightweight, smaller file size than many ported physics engines.</li>
      <li>100% open source JavaScript, written from scratch.</li>
      <li>Uses an iterative Gauss-Seidel solver to solve constraints.</li>
      <li>Uses the <a href="https://www8.cs.umu.se/kurser/5DV058/VT09/lectures/spooknotes.pdf">SPOOK</a> stepper.</li>
    </ul>

  </div>

</body>
</html>
