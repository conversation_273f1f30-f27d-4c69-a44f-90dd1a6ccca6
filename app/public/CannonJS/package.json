{"name": "cannon", "version": "0.6.2", "description": "A lightweight 3D physics engine written in JavaScript.", "homepage": "https://github.com/schteppe/cannon.js", "author": "<PERSON> <<EMAIL>> (http://steffe.se)", "keywords": ["cannon.js", "cannon", "physics", "engine", "3d"], "main": "./src/Cannon.js", "engines": {"node": "*"}, "repository": {"type": "git", "url": "https://github.com/schteppe/cannon.js.git"}, "bugs": {"url": "https://github.com/schteppe/cannon.js/issues"}, "licenses": [{"type": "MIT"}], "devDependencies": {"jshint": "latest", "uglify-js": "latest", "nodeunit": "^0.9.0", "grunt": "~0.4.0", "grunt-contrib-jshint": "~0.1.1", "grunt-contrib-nodeunit": "^0.4.1", "grunt-contrib-concat": "~0.1.3", "grunt-contrib-uglify": "^0.5.1", "grunt-browserify": "^2.1.4", "grunt-contrib-yuidoc": "^0.5.2", "browserify": "*"}, "dependencies": {}}