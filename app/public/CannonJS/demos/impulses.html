<!DOCTYPE html>
<html>
  <head>
    <title>cannon.js - impulse demo</title>
    <meta charset="utf-8">
    <link rel="stylesheet" href="css/style.css" type="text/css"/>
    <meta name="viewport" content="width=device-width, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0">
  </head>
  <body>
    <script src="../build/cannon.js"></script>
    <script src="../build/cannon.demo.js"></script>
    <script src="../libs/dat.gui.js"></script>
    <script src="../libs/Three.js"></script>
    <script src="../libs/TrackballControls.js"></script>
    <script src="../libs/Detector.js"></script>
    <script src="../libs/Stats.js"></script>
    <script src="../libs/smoothie.js"></script>
    <script>

    /**
     * Demonstrates how to add impulses and forces to a body. You can add the impulses and forces to any point on the body.
     * Adding a force to the body will add to Body.force and Body.torque.
     * An impulse is a force added to a body during a short period of time (impulse = force * time). Impulses will be added to Body.velocity and Body.angularVelocity.
     */

        var demo = new CANNON.Demo();

        var radius=1, mass=2, f=500;
        var dt=1/60, damping=0.5;

        // Add impulse to the body center
        demo.addScene("center impulse",function(){
            var world = setupWorld(demo);
            var shape = new CANNON.Sphere(radius);
            var body = new CANNON.Body({
                mass: mass,
                position: new CANNON.Vec3(0,0,1)
            });
            body.addShape(shape);
            body.linearDamping = body.angularDamping = damping;
            world.addBody(body);
            demo.addVisual(body);

            // Add an impulse to the center
            var worldPoint = new CANNON.Vec3(0,0,0);
            var impulse = new CANNON.Vec3(f*dt,0,0);
            body.applyImpulse(impulse,worldPoint);
        });

        // Add impulse to the top of the sphere
        demo.addScene("top impulse",function(){
            var world = setupWorld(demo);
            var shape = new CANNON.Sphere(radius);
            var body = new CANNON.Body({
                mass: mass,
                position: new CANNON.Vec3(0,0,1)
            });
            body.addShape(shape);
            body.linearDamping = body.angularDamping = damping;
            world.addBody(body);
            demo.addVisual(body);

            // Add an impulse to the center
            var worldPoint = new CANNON.Vec3(0,0,radius);
            var impulse = new CANNON.Vec3(f*dt,0,0);
            body.applyImpulse(impulse,worldPoint);
        });


        // Add force to the body center
        demo.addScene("center force",function(){
            var world = setupWorld(demo);
            var shape = new CANNON.Sphere(radius);
            var body = new CANNON.Body({
                mass: mass,
                position: new CANNON.Vec3(0,0,1)
            });
            body.addShape(shape);
            body.linearDamping = body.angularDamping = damping;
            world.addBody(body);
            demo.addVisual(body);

            // Add an force to the center
            var worldPoint = new CANNON.Vec3(0,0,0);
            var force = new CANNON.Vec3(f,0,0);
            body.applyForce(force,worldPoint);
        });

        // Add force to the top of the sphere
        demo.addScene("top force",function(){
            var world = setupWorld(demo);
            var shape = new CANNON.Sphere(radius);
            var body = new CANNON.Body({
                mass: mass,
                position: new CANNON.Vec3(0,0,1)
            });
            body.addShape(shape);
            body.linearDamping = body.angularDamping = damping;
            world.addBody(body);
            demo.addVisual(body);

            // Add an force to the center
            var worldPoint = new CANNON.Vec3(0,0,radius);
            var force = new CANNON.Vec3(f,0,0);
            body.applyForce(force,worldPoint);
        });

        function setupWorld(demo){
            var world = demo.getWorld();
            world.broadphase = new CANNON.NaiveBroadphase();
            return world;
        };

      demo.start();

    </script>
  </body>
</html>
