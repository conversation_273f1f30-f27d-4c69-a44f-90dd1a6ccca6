<!DOCTYPE html>
<html>
  <head>
    <title>cannon.js - collisions demo</title>
    <meta charset="utf-8">
    <link rel="stylesheet" href="css/style.css" type="text/css"/>
    <meta name="viewport" content="width=device-width, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0">
  </head>
  <body>
    <script src="../build/cannon.js"></script>
    <script src="../build/cannon.demo.js"></script>
    <script src="../libs/dat.gui.js"></script>
    <script src="../libs/Three.js"></script>
    <script src="../libs/TrackballControls.js"></script>
    <script src="../libs/Detector.js"></script>
    <script src="../libs/Stats.js"></script>
    <script src="../libs/smoothie.js"></script>
    <script>

      /**
       * For debugging different kinds of pair collisions
       */
      var demo = new CANNON.Demo();

      demo.addScene("Sphere / sphere",function(){
          var world = setupWorld(demo);

          // Sphere 1
          var sphereShape = new CANNON.Sphere(1);
          var b1 = new CANNON.Body({ mass: 5 });
          b1.addShape(sphereShape);
          b1.position.set(5,0,0);
          b1.velocity.set(-5,0,0);
          b1.linearDamping = 0;
          world.addBody(b1);
          demo.addVisual(b1);

          // Sphere 2
          var b2 = new CANNON.Body({ mass: 5 });
          b2.addShape(sphereShape);
          b2.linearDamping = 0;
          b2.position.set(-5,0,0);
          b2.velocity.set(5,0,0);
          world.addBody(b2);
          demo.addVisual(b2);
        });

      // Sphere / box side
      demo.addScene("Sphere / box side",function(){
          var world = setupWorld(demo);

          var boxShape = new CANNON.Box(new CANNON.Vec3(1,1,1));
          var sphereShape = new CANNON.Sphere(1);

          // Box
          var b1 = new CANNON.Body({ mass: 5 });
          b1.addShape(boxShape);
          b1.position.set(5,0,0);
          b1.velocity.set(-5,0,0);
          b1.linearDamping = 0;
          world.addBody(b1);
          demo.addVisual(b1);

          // Sphere
          var b2 = new CANNON.Body({ mass: 5 });
          b2.addShape(sphereShape);
          b2.position.set(-5,0,0);
          b2.velocity.set(5,0,0);
          b2.linearDamping = 0;
          world.addBody(b2);
          demo.addVisual(b2);
        });

      demo.addScene("Sphere / box edge",function(){
          var world = setupWorld(demo);

          var boxShape = new CANNON.Box(new CANNON.Vec3(1,1,1));
          var sphereShape = new CANNON.Sphere(1);

          // Box
          var b1 = new CANNON.Body({ mass: 5 });
          b1.addShape(boxShape);
          b1.position.set(5,0,0);
          b1.velocity.set(-5,0,0);
          b1.linearDamping = 0;
          var q = new CANNON.Quaternion();
          q.setFromAxisAngle(new CANNON.Vec3(0,0,1),Math.PI*0.25);
          b1.quaternion.set(q.x,q.y,q.z,q.w);
          world.addBody(b1);
          demo.addVisual(b1);

          // Sphere
          var b2 = new CANNON.Body({ mass: 5 });
          b2.addShape(sphereShape);
          b2.position.set(-5,0,0);
          b2.velocity.set(5,0,0);
          b2.linearDamping = 0;
          world.addBody(b2);
          demo.addVisual(b2);
        });

      demo.addScene("Sphere / box corner",function(){
          var world = setupWorld(demo);

          var boxShape = new CANNON.Box(new CANNON.Vec3(1,1,1));
          var sphereShape = new CANNON.Sphere(1);

          // Box
          var b1 = new CANNON.Body({ mass: 5 });
          b1.addShape(boxShape);
          b1.position.set(5,0,0);
          b1.velocity.set(-5,0,0);
          b1.linearDamping = 0;
          var q1 = new CANNON.Quaternion();
          q1.setFromAxisAngle(new CANNON.Vec3(0,0,1),Math.PI*0.25);
          var q2 = new CANNON.Quaternion();
          q2.setFromAxisAngle(new CANNON.Vec3(0,1,0),Math.PI*0.25);
          var q = q1.mult(q2);
          b1.quaternion.set(q.x,q.y,q.z,q.w);
          world.addBody(b1);
          demo.addVisual(b1);

          // Sphere
          var b2 = new CANNON.Body({ mass: 5 });
          b2.addShape(sphereShape);
          b2.position.set(-5,0,0);
          b2.velocity.set(5,0,0);
          b2.linearDamping = 0;
          world.addBody(b2);
          demo.addVisual(b2);
        });

      demo.start();

      function setupWorld(demo){
        // Create world
        var world = demo.getWorld();
        world.gravity.set(0,0,0); // no gravity
        world.broadphase = new CANNON.NaiveBroadphase();
        world.solver.iterations = 5;
        world.defaultContactMaterial.contactEquationStiffness = 1e6;
        world.defaultContactMaterial.contactEquationRelaxation = 10;
        return world;
      }

    </script>
  </body>
</html>
