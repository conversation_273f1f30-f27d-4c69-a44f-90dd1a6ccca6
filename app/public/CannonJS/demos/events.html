<!DOCTYPE html>
<html>
  <head>
    <title>cannon.js - events demo</title>
    <meta charset="utf-8">
    <link rel="stylesheet" href="css/style.css" type="text/css"/>
    <meta name="viewport" content="width=device-width, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0">
  </head>
  <body>
    <script src="../build/cannon.js"></script>
    <script src="../build/cannon.demo.js"></script>
    <script src="../libs/dat.gui.js"></script>
    <script src="../libs/Three.js"></script>
    <script src="../libs/TrackballControls.js"></script>
    <script src="../libs/Detector.js"></script>
    <script src="../libs/Stats.js"></script>
    <script src="../libs/smoothie.js"></script>
    <script>

    /**
     * For demonstrating events.
     */

      var demo = new CANNON.Demo();
      var size = 1;

      demo.addScene("'collide' event",function(){

          // Create world
          var world = demo.getWorld();

          world.defaultContactMaterial.contactEquationStiffness = 5e7;
          world.defaultContactMaterial.contactEquationRelaxation = 4;

          world.gravity.set(0,0,-20);

          // ground plane
          var groundShape = new CANNON.Plane();
          var groundBody = new CANNON.Body({ mass: 0 });
          groundBody.addShape(groundShape);
          world.addBody(groundBody);
          demo.addVisual(groundBody);

          // Sphere
          var sphere = new CANNON.Sphere(size);
          var sphereBody = new CANNON.Body({ mass: 30 });
          sphereBody.addShape(sphere);
          var pos = new CANNON.Vec3(0,0,size);
          sphereBody.position.set(0,0,size*6);
          world.addBody(sphereBody);
          demo.addVisual(sphereBody);

          // When a body collides with another body, they both dispatch the "collide" event.
          sphereBody.addEventListener("collide",function(e){
              console.log("The sphere just collided with the ground!");
              console.log("Collided with body:",e.body);
              console.log("Contact between bodies:",e.contact);
          });

        });

      demo.start();

    </script>
  </body>
</html>
