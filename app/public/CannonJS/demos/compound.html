<!DOCTYPE html>
<html>
  <head>
    <title>cannon.js - compound demo</title>
    <meta charset="utf-8">
    <link rel="stylesheet" href="css/style.css" type="text/css"/>
    <meta name="viewport" content="width=device-width, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0">
  </head>
  <body>
    <script src="../build/cannon.js"></script>
    <script src="../build/cannon.demo.js"></script>
    <script src="../libs/dat.gui.js"></script>
    <script src="../libs/Three.js"></script>
    <script src="../libs/TrackballControls.js"></script>
    <script src="../libs/Detector.js"></script>
    <script src="../libs/Stats.js"></script>
    <script src="../libs/smoothie.js"></script>
    <script>

    /**
     * A Compound shape is a shape built out of other shapes called child-shapes.
     * You can see it as a holder of a group of other shapes.
     * Use the compound shape to build rigid bodies that have more complex geometry.
     * For example, you can build concave shapes. When a child shape is added to
     * the Compound shape, a transform consisting of a position and a quaternion is
     * needed. This enables you to add child shapes at any position, rotated however
     * you like inside the local coordinate system of the Compound shape.
     */

      var demo = new CANNON.Demo();

      // A compound shape consisting of a number of boxes.
      demo.addScene("Boxes",function(){
          var world = setupWorld(demo);

          // Create the compound shape
          //var compoundShape = new CANNON.Compound();
          var s = 1.5;

          // Now create a Body for our Compound
          var mass = 10;
          var body = new CANNON.Body({ mass: mass });
          body.position.set(0,0,6);
          body.quaternion.setFromAxisAngle(new CANNON.Vec3(0, 1, 0), Math.PI / 32);

          // Use a box shape as child shape
          var shape = new CANNON.Box(new CANNON.Vec3(0.5*s,0.5*s,0.5*s));

          // We can add the same shape several times to position child shapes within the Compound.
          body.addShape(shape, new CANNON.Vec3( s, 0,-s));
          body.addShape(shape, new CANNON.Vec3( s, 0, s));
          body.addShape(shape, new CANNON.Vec3(-s, 0,-s));
          body.addShape(shape, new CANNON.Vec3(-s, 0, s));
          // Note: we only use translational offsets. The third argument could be a CANNON.Quaternion to rotate the child shape.
          body.addShape(shape, new CANNON.Vec3(-s, 0, 0));
          body.addShape(shape, new CANNON.Vec3( 0, 0,-s));
          body.addShape(shape, new CANNON.Vec3( 0, 0, s));

          world.addBody(body);
          demo.addVisual(body);

        });

      // Here we create a compound made out of spheres
      demo.addScene("Spheres",function(){
          var world = setupWorld(demo);

          var mass = 10;
          var body = new CANNON.Body({ mass: mass });

          // Compound shape
          var sphereShape = new CANNON.Sphere(1);
          body.addShape(sphereShape, new CANNON.Vec3( 1, 0,-1));
          body.addShape(sphereShape, new CANNON.Vec3( 1, 0, 1));
          body.addShape(sphereShape, new CANNON.Vec3(-1, 0,-1));
          body.addShape(sphereShape, new CANNON.Vec3(-1, 0, 1));

          body.position.set(0,0,6);
          body.quaternion.set(0,1,0,0.1);
          world.addBody(body);
          demo.addVisual(body);

        });

      function setupWorld(demo){
        var world = demo.getWorld();
        world.gravity.set(0,0,-30);
        world.broadphase = new CANNON.NaiveBroadphase();
        world.solver.iterations = 10;

        // ground plane
        var groundShape = new CANNON.Plane();
        var groundBody = new CANNON.Body({ mass: 0 });
        groundBody.addShape(groundShape);
        world.addBody(groundBody);
        demo.addVisual(groundBody);

        return world;
      };

      demo.start();

    </script>
  </body>
</html>
