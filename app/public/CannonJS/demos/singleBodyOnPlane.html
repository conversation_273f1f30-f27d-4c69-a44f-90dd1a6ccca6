<!DOCTYPE html>
<html>
  <head>
    <title>cannon.js - single body on plane demo</title>
    <meta charset="utf-8">
    <link rel="stylesheet" href="css/style.css" type="text/css"/>
    <meta name="viewport" content="width=device-width, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0">
  </head>
  <body>
    <script src="../build/cannon.js"></script>
    <script src="../build/cannon.demo.js"></script>
    <script src="../libs/dat.gui.js"></script>
    <script src="../libs/Three.js"></script>
    <script src="../libs/TrackballControls.js"></script>
    <script src="../libs/Detector.js"></script>
    <script src="../libs/Stats.js"></script>
    <script src="../libs/smoothie.js"></script>
    <script>

      var demo = new CANNON.Demo();
      var size = 2;

      demo.addScene("Sphere",function(){
          var sphereShape = new CANNON.Sphere(size);
          createBodyOnPlane(demo,sphereShape);
        });

      demo.addScene("Box",function(){
          var boxShape = new CANNON.Box(new CANNON.Vec3(size,size,size));
          createBodyOnPlane(demo,boxShape);
        });

      demo.start();

      function createBodyOnPlane(demo,shape){

        // Create world
        var world = demo.getWorld();
        world.gravity.set(0,0,-10);
        world.broadphase = new CANNON.NaiveBroadphase();
        world.solver.iterations = 10;

        world.defaultContactMaterial.contactEquationStiffness = 1e7;
        world.defaultContactMaterial.contactEquationRelaxation = 4;

        // ground plane
        var groundShape = new CANNON.Plane();
        var groundBody = new CANNON.Body({ mass: 0 });
        groundBody.addShape(groundShape);
        world.addBody(groundBody);
        demo.addVisual(groundBody);

        // Shape on plane
        var shapeBody = new CANNON.Body({ mass: 30 });
        shapeBody.addShape(shape);
        var pos = new CANNON.Vec3(0,0,size);
        shapeBody.position.set(0,size,size*2);
        shapeBody.velocity.set(0,0,0);
        shapeBody.angularVelocity.set(0,0,0);
        world.addBody(shapeBody);
        demo.addVisual(shapeBody);
      }

    </script>
  </body>
</html>
