<!DOCTYPE html>
<html>
  <head>
    <title>cannon.js - shapes demo</title>
    <meta charset="utf-8">
    <link rel="stylesheet" href="css/style.css" type="text/css"/>
    <meta name="viewport" content="width=device-width, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0">
  </head>
  <body>
    <script src="../build/cannon.js"></script>
    <script src="../build/cannon.demo.js"></script>
    <script src="../libs/dat.gui.js"></script>
    <script src="../libs/Three.js"></script>
    <script src="../libs/TrackballControls.js"></script>
    <script src="../libs/Detector.js"></script>
    <script src="../libs/Stats.js"></script>
    <script src="../libs/smoothie.js"></script>
    <script>

        var demo = new CANNON.Demo();
        var mass = 1, size = 1;


        function createTetra(){
            var verts = [new CANNON.Vec3(0,0,0),
                         new CANNON.Vec3(2,0,0),
                         new CANNON.Vec3(0,2,0),
                         new CANNON.Vec3(0,0,2)];
            var offset = -0.35;
            for(var i=0; i<verts.length; i++){
                var v = verts[i];
                v.x += offset;
                v.y += offset;
                v.z += offset;
            }
            return new CANNON.ConvexPolyhedron(verts,
                                                [
                                                    [0,3,2], // -x
                                                    [0,1,3], // -y
                                                    [0,2,1], // -z
                                                    [1,2,3], // +xyz
                                                ]);
        }

        demo.addScene("all shapes",function(){
            var world = setupWorld(demo);

            // Sphere shape
            var sphereShape = new CANNON.Sphere(size);
            var sphereBody = new CANNON.Body({ mass: mass });
            sphereBody.addShape(sphereShape);
            sphereBody.position.set(size*2,size*2,size+1);
            world.addBody(sphereBody);
            demo.addVisual(sphereBody);


            // Cylinder shape
            var cylinderShape = new CANNON.Cylinder(size,size,2*size,10);
            var cylinderBody = new CANNON.Body({ mass: mass });
            cylinderBody.addShape(cylinderShape);
            cylinderBody.position.set(-size*2,size*2,size+1);
            world.addBody(cylinderBody);
            demo.addVisual(cylinderBody);

            // Cylinder shape 2
            var cylinderShape2 = new CANNON.Cylinder(size,size,2*size,10);
            var q = new CANNON.Quaternion();
            q.setFromAxisAngle(new CANNON.Vec3(1,0,0),Math.PI / 2);
            cylinderShape2.transformAllPoints(new CANNON.Vec3(),q);
            var cylinderBody2 = new CANNON.Body({ mass: mass });
            cylinderBody2.addShape(cylinderShape2);
            cylinderBody2.position.set(-size*2,size*2,4*size+1);
            world.addBody(cylinderBody2);
            demo.addVisual(cylinderBody2);


            // Box shape
            var boxShape = new CANNON.Box(new CANNON.Vec3(size,size,size));
            var boxBody = new CANNON.Body({ mass: mass });
            boxBody.addShape(boxShape);
            boxBody.position.set(-size*2,-size*2,size+1);
            world.addBody(boxBody);
            demo.addVisual(boxBody);




            // Particle - not a shape but still here to show how to use it.
            var particle = new CANNON.Body({ mass: mass });
            particle.addShape(new CANNON.Particle());
            particle.position.set(-size*2,size*4,size+1);
            world.addBody(particle);
            demo.addVisual(particle);




            // Compound
            var compoundBody = new CANNON.Body({ mass: mass });
            var s = size;
            var shape = new CANNON.Box(new CANNON.Vec3(0.5*s,0.5*s,0.5*s));
            compoundBody.addShape(shape, new CANNON.Vec3( 0, 0, s));
            compoundBody.addShape(shape, new CANNON.Vec3( 0, 0, 0));
            compoundBody.addShape(shape, new CANNON.Vec3( 0, 0,-s));
            compoundBody.addShape(shape, new CANNON.Vec3(-s, 0,-s));
            compoundBody.position.set(-size*4,size*4,size+1);
            world.addBody(compoundBody);
            demo.addVisual(compoundBody);


            // ConvexPolyhedron tetra shape
            var verts = [new CANNON.Vec3(0,0,0),
                         new CANNON.Vec3(2,0,0),
                         new CANNON.Vec3(0,2,0),
                         new CANNON.Vec3(0,0,2)];
            var offset = -0.35;
            for(var i=0; i<verts.length; i++){
                var v = verts[i];
                v.x += offset;
                v.y += offset;
                v.z += offset;
            }
            var polyhedronShape = createTetra();
            var polyhedronBody = new CANNON.Body({ mass: mass });
            polyhedronBody.addShape(polyhedronShape);
            polyhedronBody.position.set(size*2,-size*2,size+1);
            world.addBody(polyhedronBody);
            demo.addVisual(polyhedronBody);
        });

      function setupWorld(demo){
        var world = demo.getWorld();
        world.gravity.set(0,0,-30);
        world.broadphase = new CANNON.NaiveBroadphase();
        world.solver.iterations = 17;

        world.defaultContactMaterial.contactEquationStiffness = 1e6;
        world.defaultContactMaterial.contactEquationRelaxation = 3;

        // ground plane
        var groundShape = new CANNON.Plane();
        var groundBody = new CANNON.Body({ mass: 0 });
        groundBody.addShape(groundShape);
        groundBody.position.set(-10,0,0);
        world.addBody(groundBody);
        demo.addVisual(groundBody);
        return world;
      };

      demo.start();

    </script>
  </body>
</html>
