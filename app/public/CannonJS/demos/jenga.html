<!DOCTYPE html>
<html>
	<head>
		<title>cannon.js - jenga demo</title>
		<meta charset="utf-8">
		<link rel="stylesheet" href="css/style.css" type="text/css"/>
		<meta name="viewport" content="width=device-width, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0">
	</head>
	<body>
		<script src="../build/cannon.js"></script>
		<script src="../build/cannon.demo.js"></script>
		<script src="../libs/dat.gui.js"></script>
		<script src="../libs/Three.js"></script>
		<script src="../libs/TrackballControls.js"></script>
		<script src="../libs/Detector.js"></script>
		<script src="../libs/Stats.js"></script>
		<script src="../libs/smoothie.js"></script>
		<script>

			var demo = new CANNON.Demo();

			demo.addScene("jenga",function(){

				var world = demo.getWorld();
				//world.allowSleep = true;
				world.gravity.set(0,0,-5);
				world.broadphase = new CANNON.NaiveBroadphase();
				world.solver.iterations = 50;

				world.defaultContactMaterial.contactEquationStiffness = 5e6;
				world.defaultContactMaterial.contactEquationRelaxation = 3;

				// ground plane
				var groundShape = new CANNON.Plane();
				var groundBody = new CANNON.Body({
					mass: 0
				});
				groundBody.addShape(groundShape);
				groundBody.position.set(-10,0,0);
				world.addBody(groundBody);
				demo.addVisual(groundBody);

				var size = 0.5;
				var mass = 1;
				var gap = 0.02;
				for(var i=0; i<10; i++){ // Layers
					for(var j=0; j<3; j++){
						var body = new CANNON.Body({ mass: mass });

						var he = new CANNON.Vec3(size * 3, size, size);
						var dx = 0;
						var dy = 1;
						if(i % 2 === 0){
							he.set(size, size * 3, size);
							dx = 1;
							dy = 0;
						}
						var shape = new CANNON.Box(he);
						body.addShape(shape);
						body.position.set(
							2 * (size + gap) * (j - 1) * dx,
							2 * (size + gap) * (j - 1) * dy,
							2 * (size + gap) * (i + 1)
						);

						world.addBody(body);
						demo.addVisual(body);
					}
				}
			});

			demo.start();

		</script>
	</body>
</html>
