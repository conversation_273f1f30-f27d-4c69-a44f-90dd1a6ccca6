<!DOCTYPE html>
<html>
  <head>
    <title>cannon.js - collisionfilter demo</title>
    <meta charset="utf-8">
    <link rel="stylesheet" href="css/style.css" type="text/css"/>
    <meta name="viewport" content="width=device-width, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0">
  </head>
  <body>
    <script src="../build/cannon.js"></script>
    <script src="../build/cannon.demo.js"></script>
    <script src="../libs/dat.gui.js"></script>
    <script src="../libs/Three.js"></script>
    <script src="../libs/TrackballControls.js"></script>
    <script src="../libs/Detector.js"></script>
    <script src="../libs/Stats.js"></script>
    <script src="../libs/smoothie.js"></script>
    <script>

        /**
         * Demonstrates how to filter collisions using Body.collisionFilterGroup and Body.collisionFilterMask.
         * The filters are applied inside the broadphase. It can be used to allow or disallow collisions between bodies.
         *
         * A collision is allowed if
         *   (bodyA.collisionFilterGroup & bodyB.collisionFilterMask) && (bodyB.collisionFilterGroup & bodyA.collisionFilterMask)
         *
         * These are indeed bitwise operations. Learn more about that here: http://en.wikipedia.org/wiki/Bitwise_operation
         */
        var demo = new CANNON.Demo();
        var size = 1;
        var mass = 1;

        // Collision filter groups - must be powers of 2!
        var GROUP1 = 1;
        var GROUP2 = 2;
        var GROUP3 = 4;

        demo.addScene("filter",function(){
            var world = demo.getWorld();
            world.gravity.set(0,0,0); // no gravity
            world.broadphase = new CANNON.NaiveBroadphase();
            world.solver.iterations = 5;

            // Sphere
            var sphereShape = new CANNON.Sphere(size);
            var sphereBody = new CANNON.Body({
                mass: mass,
                position: new CANNON.Vec3(5,0,0),
                velocity: new CANNON.Vec3(-5,0,0),
                collisionFilterGroup: GROUP1, // Put the sphere in group 1
                collisionFilterMask: GROUP2 | GROUP3, // It can only collide with group 2 and 3
                shape: sphereShape
            });

            // Box
            var boxBody = new CANNON.Body({
                mass: mass,
                shape: new CANNON.Box(new CANNON.Vec3(size,size,size)),
                collisionFilterGroup: GROUP2, // Put the box in group 2
                collisionFilterMask:  GROUP1 // It can only collide with group 1 (the sphere)
            });

            // Cylinder
            var cylinderShape = new CANNON.Cylinder(size,size,size*2.2,10);
            var cylinderBody = new CANNON.Body({
                mass: mass,
                shape: cylinderShape,
                position: new CANNON.Vec3(-5,0,0),
                collisionFilterGroup: GROUP3, // Put the cylinder in group 3
                collisionFilterMask:  GROUP1 // It can only collide with group 1 (the sphere)
            });

            // Add everything to the world and demo
            world.addBody(sphereBody);
            world.addBody(boxBody);
            world.addBody(cylinderBody);
            demo.addVisual(sphereBody);
            demo.addVisual(boxBody);
            demo.addVisual(cylinderBody);
        });

        demo.start();

    </script>
  </body>
</html>
