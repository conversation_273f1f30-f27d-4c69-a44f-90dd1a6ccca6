<!DOCTYPE html>
<html>
  <head>
    <title>cannon.js - body types demo</title>
    <meta charset="utf-8">
    <link rel="stylesheet" href="css/style.css" type="text/css"/>
    <meta name="viewport" content="width=device-width, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0">
  </head>
  <body>
    <script src="../build/cannon.js"></script>
    <script src="../build/cannon.demo.js"></script>
    <script src="../libs/dat.gui.js"></script>
    <script src="../libs/Three.js"></script>
    <script src="../libs/TrackballControls.js"></script>
    <script src="../libs/Detector.js"></script>
    <script src="../libs/Stats.js"></script>
    <script src="../libs/smoothie.js"></script>
    <script>

      /**
       * Demos of the Body.type types.
       */
      var demo = new CANNON.Demo();
      var size = 2;

      demo.addScene("Moving box",function(){
          var world = setupWorld(demo);

          var boxShape = new CANNON.Box(new CANNON.Vec3(size,size,size));
          var sphereShape = new CANNON.Sphere(size);

          var mass = 5, boxMass = 0;

          // Kinematic Box
          // Does only collide with dynamic bodies, but does not respond to any force.
          // Its movement can be controlled by setting its velocity.
          var b1 = new CANNON.Body({
            mass: boxMass,
            type: CANNON.Body.KINEMATIC,
            position: new CANNON.Vec3(0, 0, 0.5*size)
          });
          b1.addShape(boxShape);
          world.addBody(b1);
          demo.addVisual(b1);

          // To control the box movement we must set its velocity
          b1.velocity.set(0,0,5);
          setInterval(function(){
              if(b1.velocity.z<0)
                  b1.velocity.set(0,0,5);
              else
                  b1.velocity.set(0,0,-5);
            },1000);

          // Dynamic Sphere
          // Dynamic bodies can collide with bodies of all other types.
          var b2 = new CANNON.Body({
            mass: mass,
            position: new CANNON.Vec3(0,0,3*size)
          });
          b2.addShape(sphereShape);
          world.addBody(b2);
          demo.addVisual(b2);
        });

      demo.start();

      function setupWorld(demo){
        var world = demo.getWorld();
        world.gravity.set(0,0,-40);
        world.broadphase = new CANNON.NaiveBroadphase();
        world.solver.iterations = 10;

        world.defaultContactMaterial.contactEquationStiffness = 1e8;
        world.defaultContactMaterial.contactEquationRelaxation = 10;

        // Static ground plane
        // Static bodies only interacts with dynamic bodies. Velocity is always zero.
        var groundShape = new CANNON.Plane();
        var mass = 0; // mass=0 will produce a static body automatically
        var groundBody = new CANNON.Body({
          mass: mass,
        });
        groundBody.addShape(groundShape);
        world.addBody(groundBody);
        demo.addVisual(groundBody);

        return world;
      }

    </script>
  </body>
</html>
