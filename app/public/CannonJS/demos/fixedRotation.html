<!DOCTYPE html>
<html>
  <head>
    <title>cannon.js - fixedRotation demo</title>
    <meta charset="utf-8">
    <link rel="stylesheet" href="css/style.css" type="text/css"/>
    <meta name="viewport" content="width=device-width, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0">
  </head>
  <body>
    <script src="../build/cannon.js"></script>
    <script src="../build/cannon.demo.js"></script>
    <script src="../libs/dat.gui.js"></script>
    <script src="../libs/Three.js"></script>
    <script src="../libs/TrackballControls.js"></script>
    <script src="../libs/Detector.js"></script>
    <script src="../libs/Stats.js"></script>
    <script src="../libs/smoothie.js"></script>
    <script>

        var demo = new CANNON.Demo(),
            size = 1.0;

        demo.addScene("Fixed rotation", function(){

            // Create world
            var world = demo.getWorld();
            world.broadphase = new CANNON.NaiveBroadphase();

            // ground plane
            var groundShape = new CANNON.Plane();
            var groundBody = new CANNON.Body({ mass: 0 });
            groundBody.addShape(groundShape);
            world.addBody(groundBody);
            demo.addVisual(groundBody);

            // Create a box with fixed rotation
            var shape = new CANNON.Box(new CANNON.Vec3(size,size,size));
            var boxBody = new CANNON.Body({ mass: 1 });
            boxBody.addShape(shape);
            boxBody.position.set(0,0,size);
            boxBody.fixedRotation = true;
            boxBody.updateMassProperties();
            world.addBody(boxBody);
            demo.addVisual(boxBody);

            // Another one
            var shape2 = new CANNON.Box(new CANNON.Vec3(size, size, size));
            var boxBody2 = new CANNON.Body({ mass: 1, });
            boxBody2.addShape(shape2);
            boxBody2.position.set(size*3/2, 0, size*4);
            boxBody2.fixedRotation = true;
            boxBody2.updateMassProperties();
            world.addBody(boxBody2);
            demo.addVisual(boxBody2);

            // Change gravity so the boxes will slide along x axis
            world.gravity.set(0, 0, -10);
        });

        demo.start();

    </script>
  </body>
</html>
