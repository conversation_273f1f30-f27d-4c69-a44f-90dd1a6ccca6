<!DOCTYPE html>
<html>
  <head>
    <title>cannon.js - Heightfield demo</title>
    <meta charset="utf-8">
    <link rel="stylesheet" href="css/style.css" type="text/css"/>
    <meta name="viewport" content="width=device-width, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0">
  </head>
  <body>
    <script src="../build/cannon.js"></script>
    <script src="../build/cannon.demo.js"></script>
    <script src="../libs/dat.gui.js"></script>
    <script src="../libs/Three.js"></script>
    <script src="../libs/TrackballControls.js"></script>
    <script src="../libs/Detector.js"></script>
    <script src="../libs/Stats.js"></script>
    <script src="../libs/smoothie.js"></script>
    <script>

        var demo = new CANNON.Demo();

        demo.addScene("Heightfield", function () {

            // Init world
            var world = demo.getWorld();
            world.gravity.set(0, 0, -10);
            world.broadphase = new CANNON.NaiveBroadphase();

            // Create a matrix of height values
            var matrix = [];
            var sizeX = 15,
                sizeY = 15;
            for (var i = 0; i < sizeX; i++) {
                matrix.push([]);
                for (var j = 0; j < sizeY; j++) {
                    var height = Math.cos(i/sizeX * Math.PI * 2)*Math.cos(j/sizeY * Math.PI * 2) + 2;
                    if(i===0 || i === sizeX-1 || j===0 || j === sizeY-1)
                        height = 3;
                    matrix[i].push(height);
                }
            }

            // Create the heightfield
            var hfShape = new CANNON.Heightfield(matrix, {
                elementSize: 1
            });
            var hfBody = new CANNON.Body({ mass: 0 });
            hfBody.addShape(hfShape);
            hfBody.position.set(-sizeX * hfShape.elementSize / 2, -20, -10);
            world.addBody(hfBody);
            demo.addVisual(hfBody);

            // Add spheres
            var mass = 1;
            for(var i=0; i<sizeX - 1; i++){
                for (var j = 0; j < sizeY - 1; j++) {
                    if(i===0 || i >= sizeX-2 || j===0 || j >= sizeY-2)
                        continue;
                    var sphereShape = new CANNON.Sphere(0.1);
                    var sphereBody = new CANNON.Body({ mass: mass });
                    sphereBody.addShape(sphereShape);
                    sphereBody.position.set(0.25 + i, 0.25 + j, 3);
                    sphereBody.position.vadd(hfBody.position, sphereBody.position);
                    world.addBody(sphereBody);
                    demo.addVisual(sphereBody);
                }
            }
        });

      demo.start();

    </script>
  </body>
</html>
