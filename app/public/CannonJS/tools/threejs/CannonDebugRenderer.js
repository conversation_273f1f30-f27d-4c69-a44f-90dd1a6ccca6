export default class CannonDebugRenderer {
    constructor(scene, world, options) {
        options = options || {};

        this.scene = scene;
        this.world = world;

        this._meshes = [];

        this._material = new THREE.MeshBasicMaterial({
            color: 0x00ff00,
            wireframe: true
        });

        this._sphereGeometry = new THREE.SphereGeometry(1);
        this._boxGeometry = new THREE.BoxGeometry(1, 1, 1);
        this._planeGeometry = new THREE.PlaneGeometry(10, 10, 10, 10);
        this._cylinderGeometry = new THREE.CylinderGeometry(1, 1, 10, 10);
    }

    update() {
        const bodies = this.world.bodies;
        const meshes = this._meshes;
        const shapeWorldPosition = new CANNON.Vec3();
        const shapeWorldQuaternion = new CANNON.Quaternion();

        let meshIndex = 0;

        for (let i = 0; i < bodies.length; i++) {
            const body = bodies[i];

            for (let j = 0; j < body.shapes.length; j++) {
                const shape = body.shapes[j];

                this._updateMesh(meshIndex, body, shape);

                const mesh = meshes[meshIndex];

                if (mesh) {
                    // Get world position
                    body.quaternion.vmult(body.shapeOffsets[j], shapeWorldPosition);
                    body.position.vadd(shapeWorldPosition, shapeWorldPosition);

                    // Get world quaternion
                    body.quaternion.mult(body.shapeOrientations[j], shapeWorldQuaternion);

                    // Copy to meshes
                    mesh.position.copy(shapeWorldPosition);
                    mesh.quaternion.copy(shapeWorldQuaternion);
                }

                meshIndex++;
            }
        }

        for (let i = meshIndex; i < meshes.length; i++) {
            const mesh = meshes[i];
            if (mesh) {
                this.scene.remove(mesh);
            }
        }

        meshes.length = meshIndex;
    }

    _createMesh(shape) {
        let mesh;
        const material = this._material;

        switch (shape.type) {
            case CANNON.Shape.types.SPHERE:
                mesh = new THREE.Mesh(this._sphereGeometry, material);
                break;

            case CANNON.Shape.types.BOX:
                mesh = new THREE.Mesh(this._boxGeometry, material);
                break;

            case CANNON.Shape.types.PLANE:
                mesh = new THREE.Mesh(this._planeGeometry, material);
                break;

            case CANNON.Shape.types.CYLINDER:
                mesh = new THREE.Mesh(this._cylinderGeometry, material);
                break;
        }

        if (mesh) {
            this.scene.add(mesh);
        }

        return mesh;
    }

    _updateMesh(index, body, shape) {
        let mesh = this._meshes[index];
        if (!mesh) {
            mesh = this._createMesh(shape);
            this._meshes[index] = mesh;
        }

        if (mesh) {
            switch (shape.type) {
                case CANNON.Shape.types.SPHERE:
                    mesh.scale.set(shape.radius * 2, shape.radius * 2, shape.radius * 2);
                    break;

                case CANNON.Shape.types.BOX:
                    mesh.scale.copy(shape.halfExtents);
                    mesh.scale.multiplyScalar(2);
                    break;

                case CANNON.Shape.types.PLANE:
                    break;

                case CANNON.Shape.types.CYLINDER:
                    mesh.scale.set(shape.radiusTop * 2, shape.height, shape.radiusTop * 2);
                    break;
            }
        }
    }
}