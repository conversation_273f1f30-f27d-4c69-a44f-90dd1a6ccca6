<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <link rel="stylesheet" href="../../css/styles.css">
    <link rel="stylesheet" href="../../css/user-details.css">
    <script type='module' src="/src/translation.js"></script>
</head>

<body>
    <header>
        <div class="header-content">
            <label class="title">TIANYEN</label>
            <nav class="header-nav">
                <button id="themeToggle" class="theme-toggle" aria-label="Toggle theme">
                    <span class="theme-icon">🌙</span>
                </button>

                <div class="nav-item dropdown">
                    <a id="languageBtn" data-i18n="language" href="#" class="nav-link">Language</a>
                    <div class="dropdown-menu">
                        <a id="ENBtn" data-i18n="english" href="#" class="dropdown-item">English</a>
                        <a id="ZHTWBtn" data-i18n="zh-tw" href="#" class="dropdown-item">zh-TW</a>
                    </div>
                </div>

                <div class="nav-item dropdown">
                    <a id="openUserDialog" data-i18n="user" href="#" class="nav-link active">User</a>
                    <div class="dropdown-menu">
                        <a id="informationBtn" data-i18n="information" href="/html/user/details.html"
                            class="dropdown-item active">Information</a>
                        <a id="settingsBtn" data-i18n="settings" href="/html/user/settings.html"
                            class="dropdown-item">Settings</a>
                        <a id="signInAndOutBtn" data-i18n="sign_out" href="#" class="dropdown-item">Sign Out</a>
                    </div>
                </div>

                <div class="nav-item">
                    <a id="projectsBtn" data-i18n="projects" href="/html/project/list.html"
                        class="nav-link">Projects</a>
                </div>
            </nav>
        </div>
        <script type="module" src="/src/user/userDialog.js"></script>
    </header>
    <div class="details">
        <div class="detailbox">
            <img class="loginimg" src="../assets/profile_big_blue.png">
            <div class="databox">
                <div class="details-item">
                    <label><span data-i18n="name">Name</label>
                    <label id="nameText" class="details-content"></label>
                </div>
                <div class="details-item">
                    <label><span data-i18n="email">Email</label>
                    <label id="emailText" class="details-content"></label>
                </div>
                <div class="details-item">
                    <label><span data-i18n="phone">Phone</label>
                    <label id="phoneText" class="details-content"></label>
                </div>
                <div class="details-item">
                    <label><span data-i18n="role">Role</label>
                    <label id="roleText" class="details-content"></label>
                </div>
            </div>

        </div>

    </div>


    <script type="module">
        import * as tokenOps from '../../src/user/tokenOps.js';
        import { handleError } from '../../src/handle_error.js';
        import { linkApiRoot } from '../../src/utils/env.js';
        var token = tokenOps.getToken();
        var nameText = document.getElementById('nameText');
        var emailText = document.getElementById('emailText');
        var phoneText = document.getElementById('phoneText');
        var roleText = document.getElementById('roleText');
        var account = localStorage.getItem('account')
        window.onload = load;
        function load() {
            fetch(linkApiRoot('user/') + account,
                {
                    headers: { Authorization: 'Bearer ' + token }
                }
            )
                .then(handleError)
                .then(response => response.json())
                .then(userData => {
                    console.log(userData)
                    nameText.textContent = userData.name;
                    emailText.textContent = userData.email;
                    phoneText.textContent = userData.phone;
                    roleText.textContent = userData.role;
                })
                .catch(err => {
                    console.error(err);
                })
        }
    </script>
    <script>
        // Theme toggle functionality
        const themeToggle = document.getElementById('themeToggle');
        const themeIcon = document.querySelector('.theme-icon');
        const body = document.body;

        // Check for saved theme preference or default to 'dark'
        const currentTheme = localStorage.getItem('theme') || 'dark';
        body.setAttribute('data-theme', currentTheme);
        updateThemeIcon(currentTheme);

        themeToggle.addEventListener('click', () => {
            const currentTheme = body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            body.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
            updateThemeIcon(newTheme);
        });

        function updateThemeIcon(theme) {
            themeIcon.textContent = theme === 'dark' ? '☀️' : '🌙';
        }
    </script>
</body>

</html>