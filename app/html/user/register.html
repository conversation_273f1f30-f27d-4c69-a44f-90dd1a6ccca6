<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <title>Login</title>
        <link rel="stylesheet" href="../../css/styles.css">
        <link rel="stylesheet" href="../../css/register.css">
    <script type='module' src="/src/translation.js"></script>
    </head>
    <body>
        <header>
            <label>TIANYEN</label>
            <a id="projectsBtn" data-i18n="projects" href="/html/project/list.html">Projects</a>
            <a id="openUserDialog" data-i18n="user" href="">User</a>
            <div class="dialog-container">
                <dialog id="userDialog">
                    <ul>
                        <li><a id="informationBtn" data-i18n="information" href="/html/user/details.html">Information</a></li>
                        <li><a id="settingsBtn" data-i18n="settings" href="/html/user/settings.html">Settings</a></li>
                        <li><a id="signInAndOutBtn" data-18n="sign_in" href="">SignOut</a></li>
                    </ul>   
                </dialog>
            </div>
                <a id="languageBtn" data-i18n="language" href="">Language</a>
                <div class="dialog-container">
                    <dialog id="langDialog">
                        <ul>
                            <li><a id="ENBtn" data-i18n="english" href="">English</a></li>
                            <li><a id="ZHTWBtn" data-i18n="zh-tw" href="">zh-TW</a></li>
                        </ul>
                    </dialog>
                </div>
            <script type="module" src="/src/user/userDialog.js"></script>
        </header>
        <h1>Register</h1>
        <form id = "register_form">
            <img class="loginimg" src="../assets/editProflie_big.png">
            <p class="imagename">Profile Image</p>
            <div class="form-item">
                <label data-i18n="name" for="name">Name</label>
                <input type="text" name="name" id="name" required />
            </div>
            <div class="form-item">
                <label data-i18n="account" for="account">Account</label>
                <input type="text" name="account" id="account" required />
            </div>
           
            <div class="form-item">
                <label data-i18n="password" for="password">Password</label>
                <input type="password" name="password" id="password" required />
            </div>
            <div class="form-item">
                <label data-i18n="email" for="email">Email</label>
                <input type="text" name="email" id="email" />
            </div>
            <div class="form-item">
                <label data-i18n="phone" for="phone">Phone</label>
                <input type="text" name="phone" id="phone" />
            </div>
            <div class="form-item">
                <label data-i18n="role">Role</label>
                <select for="role" name="role" id="role">
                    <option data-i18n="admin" value="ADMIN">admin</option>
                    <option data-i18n="user" value="USER">user</option>
                </select>
            </div>
            
            

            <label id="error_message" style="color:red"></label>
            <input data-i18n="submit" type="submit" value="submit" id="submit">
        </form>

    
        <script type="module">
            import {linkApiRoot} from '../../src/utils/env.js';

            const form = document.getElementById('register_form');
          
            form.addEventListener('submit', async function(event) {
                const formData = new FormData(form);
                event.preventDefault(); // 阻止表單的默認提交行為
                const errorMessage = document.getElementById('error_message');
        
                // 建立表單數據
                var object = {};
                formData.forEach(function(value, key){
                    object[key] = value;
                });
                var json = JSON.stringify(object);
                console.log(json)
                // 使用 Fetch 提交表單
                fetch(linkApiRoot('user'),{
                    method: 'POST',
                    headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                    },
                    body: json
                })
                .then(async function(response) {
                    if (response.status !== 200) {
                        throw new Error(await response.text());
                    }
                    return response.json();
                })
                .then(data => {
                    window.location.href = "../../index.html";
                })
                .catch(error => {
                    errorMessage.innerText = error;
                });
        });
          </script>
    </body>
</html>