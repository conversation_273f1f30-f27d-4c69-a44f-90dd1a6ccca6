<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <title>User List</title>
        <link rel="stylesheet" href="../../css/styles.css">
        <link rel="stylesheet" href="../../css/game-user-list.css">
        <script src="https://code.jquery.com/jquery-3.5.1.js"></script>
        <script src="https://cdn.datatables.net/1.11.3/js/jquery.dataTables.min.js"></script>
        <script src="https://cdn.datatables.net/responsive/2.2.6/js/dataTables.responsive.min.js"></script>
    </head>
    <body>
        <header>
            <label class="logo">TIANYEN</label>
            <a id="projectsBtn" data-i18n="projects" href="/html/project/list.html">Projects</a>
            <a id="openUserDialog" data-i18n="user" href="">User</a>
            <div class="dialog-container">
                <dialog id="userDialog">
                    <ul>
                        <li><a id="informationBtn" data-i18n="information" href="/html/user/details.html">Information</a></li>
                        <li><a id="settingsBtn" data-i18n="settings" href="/html/user/settings.html">Settings</a></li>
                        <li><a id="signInAndOutBtn" data-18n="sign_in" href="">SignOut</a></li>
                    </ul>   
                </dialog>
            </div>
                <a id="languageBtn" data-i18n="language" href="">Language</a>
                <div class="dialog-container">
                    <dialog id="langDialog">
                        <ul>
                            <li><a id="ENBtn" data-i18n="english" href="">English</a></li>
                            <li><a id="ZHTWBtn" data-i18n="zh-tw" href="">zh-TW</a></li>
                        </ul>
                    </dialog>
                </div>
            <script type="module" src="/src/user/userDialog.js"></script>
        </header>
        <div>
            <div class="btn-list">
                <button onclick="window.location.href='../exhibition/view.html';">Back To Menu</button>
                <button onclick="window.location.href='../exhibition/details.html';">詳細資訊</button>
                <button onclick="window.location.href='../video/list.html';">資源配置</button>
                <button onclick="window.location.href='../exhibition/monitoring.html';">運行狀態監控</button>
                <button onclick="window.location.href='../exhibition/statistics.html';">數據統計</button>
                <button onclick="window.location.href='../game/product-list.html';">產品列表</button>
                <button class="thistime" onclick="window.location.href='../game/user-list.html';">使用者列表</button>
                <button onclick="window.location.href='../game/voucher-list.html';">兌換券列表</button>
                <button onclick="window.location.href='../exhibition/settings.html';">設定</button>
            </div>
            <p class="exhibitionName">［展覽館名稱］</p>
            <div class="game-user-list-maincontent">
                <div class="top-title">
                    <p class="list-title">使用者列表</p>
                </div>
                <table id="table">
                    <thead>
                        <tr  id="titles">
                            <th>編號</th>
                            <th>活動編號</th>
                            <th>開始時間</th>
                            <th>結束時間</th>
                            <th>分數</th>
                        </tr>
                        
                    </thead>
                </table>
                <script type="module" src="../../src/exhibition/title.js"></script>
                <script type="module" src="../../src/game/user-list.js"></script>
            </div>
        </div>
    </body>
</html>

