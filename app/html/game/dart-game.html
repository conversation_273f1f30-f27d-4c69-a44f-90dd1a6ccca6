<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>飛鏢遊戲</title>
    <link rel="stylesheet" href="../../css/style.css">
    <style>
        .game-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .game-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }
        
        .game-header h1 {
            margin: 0 0 10px 0;
            font-size: 2.5em;
        }
        
        .room-info {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .game-content {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 30px;
        }
        
        .game-area {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .connection-status {
            padding: 15px;
            border-radius: 6px;
            text-align: center;
            font-weight: bold;
        }
        
        .connection-status.connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .connection-status.disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .connection-status.connecting {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .qr-input-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            border: 1px solid #dee2e6;
        }
        
        .qr-input-section h3 {
            margin: 0 0 15px 0;
            color: #495057;
        }
        
        .qr-input-form {
            display: flex;
            gap: 10px;
        }
        
        .qr-input-form input {
            flex: 1;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
        }
        
        .qr-input-form button {
            background: #007bff;
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
        }
        
        .qr-input-form button:hover {
            background: #0056b3;
        }
        
        .qr-input-form button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .game-controls {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            border: 1px solid #dee2e6;
        }
        
        .dart-board {
            width: 100%;
            max-width: 400px;
            margin: 0 auto;
            position: relative;
        }
        
        .dart-board svg {
            width: 100%;
            height: auto;
            border: 3px solid #333;
            border-radius: 50%;
            background: #000;
        }
        
        .score-input {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin-top: 20px;
        }
        
        .score-btn {
            padding: 15px;
            border: 2px solid #007bff;
            background: white;
            color: #007bff;
            border-radius: 6px;
            cursor: pointer;
            font-size: 18px;
            font-weight: bold;
            transition: all 0.3s;
        }
        
        .score-btn:hover {
            background: #007bff;
            color: white;
        }
        
        .score-btn.selected {
            background: #007bff;
            color: white;
        }
        
        .throw-controls {
            margin-top: 20px;
            text-align: center;
        }
        
        .throw-btn {
            background: #28a745;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 18px;
            font-weight: bold;
            margin: 0 10px;
        }
        
        .throw-btn:hover {
            background: #218838;
        }
        
        .throw-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .end-game-btn {
            background: #dc3545;
        }
        
        .end-game-btn:hover {
            background: #c82333;
        }
        
        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .player-info {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 6px;
            border: 1px solid #bbdefb;
        }
        
        .player-info h3 {
            margin: 0 0 15px 0;
            color: #1976d2;
        }
        
        .player-stats {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .stat-item:last-child {
            border-bottom: none;
        }
        
        .stat-label {
            font-weight: bold;
            color: #666;
        }
        
        .stat-value {
            color: #333;
            font-weight: bold;
        }
        
        .current-score {
            font-size: 2em;
            color: #007bff;
        }
        
        .room-players {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            border: 1px solid #dee2e6;
        }
        
        .room-players h3 {
            margin: 0 0 15px 0;
            color: #495057;
        }
        
        .player-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .player-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            background: white;
            border-radius: 4px;
            border: 1px solid #dee2e6;
        }
        
        .player-name {
            font-weight: bold;
            color: #495057;
        }
        
        .player-score {
            color: #007bff;
            font-weight: bold;
        }
        
        .player-status {
            font-size: 0.8em;
            padding: 2px 8px;
            border-radius: 12px;
            color: white;
        }
        
        .player-status.playing {
            background: #28a745;
        }
        
        .player-status.waiting {
            background: #6c757d;
        }
        
        .leaderboard {
            background: #fff3cd;
            padding: 20px;
            border-radius: 6px;
            border: 1px solid #ffeaa7;
        }
        
        .leaderboard h3 {
            margin: 0 0 15px 0;
            color: #856404;
        }
        
        .leaderboard-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .leaderboard-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            background: white;
            border-radius: 4px;
            border: 1px solid #ffeaa7;
        }
        
        .rank {
            font-weight: bold;
            color: #856404;
            min-width: 30px;
        }
        
        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #f5c6cb;
            margin: 20px 0;
            display: none;
        }
        
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #c3e6cb;
            margin: 20px 0;
            display: none;
        }
        
        @media (max-width: 768px) {
            .game-content {
                grid-template-columns: 1fr;
            }
            
            .score-input {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="game-container">
        <!-- 遊戲標題 -->
        <div class="game-header">
            <h1>🎯 飛鏢遊戲</h1>
            <div class="room-info">
                房間: <span id="room-name">載入中...</span> | 
                房間 ID: <span id="room-id">-</span>
            </div>
        </div>
        
        <!-- 錯誤和成功訊息 -->
        <div class="error-message" id="error-message"></div>
        <div class="success-message" id="success-message"></div>
        
        <div class="game-content">
            <!-- 主遊戲區域 -->
            <div class="game-area">
                <!-- 連接狀態 -->
                <div class="connection-status disconnected" id="connection-status">
                    🔴 未連接
                </div>
                
                <!-- QR Code 輸入 -->
                <div class="qr-input-section" id="qr-input-section">
                    <h3>🎫 掃描或輸入您的報名 QR Code</h3>
                    <div class="qr-input-form">
                        <input type="text" id="qr-code-input" placeholder="請輸入或掃描 QR Code">
                        <button id="join-game-btn">加入遊戲</button>
                    </div>
                </div>
                
                <!-- 遊戲控制 -->
                <div class="game-controls" id="game-controls" style="display: none;">
                    <h3>🎯 飛鏢投擲</h3>
                    
                    <!-- 簡化的飛鏢盤 -->
                    <div class="dart-board">
                        <svg viewBox="0 0 200 200">
                            <!-- 外圈 -->
                            <circle cx="100" cy="100" r="95" fill="#000" stroke="#fff" stroke-width="2"/>
                            <!-- 分數區域 -->
                            <circle cx="100" cy="100" r="80" fill="#ff0000" stroke="#fff" stroke-width="1"/>
                            <circle cx="100" cy="100" r="60" fill="#00ff00" stroke="#fff" stroke-width="1"/>
                            <circle cx="100" cy="100" r="40" fill="#ffff00" stroke="#fff" stroke-width="1"/>
                            <circle cx="100" cy="100" r="20" fill="#ff0000" stroke="#fff" stroke-width="1"/>
                            <circle cx="100" cy="100" r="5" fill="#000" stroke="#fff" stroke-width="1"/>
                            
                            <!-- 分數標示 -->
                            <text x="100" y="25" text-anchor="middle" fill="white" font-size="12">20</text>
                            <text x="175" y="105" text-anchor="middle" fill="white" font-size="12">6</text>
                            <text x="100" y="185" text-anchor="middle" fill="white" font-size="12">3</text>
                            <text x="25" y="105" text-anchor="middle" fill="white" font-size="12">11</text>
                        </svg>
                    </div>
                    
                    <!-- 分數選擇 -->
                    <div class="score-input">
                        <button class="score-btn" data-score="1">1分</button>
                        <button class="score-btn" data-score="3">3分</button>
                        <button class="score-btn" data-score="6">6分</button>
                        <button class="score-btn" data-score="11">11分</button>
                        <button class="score-btn" data-score="20">20分</button>
                        <button class="score-btn" data-score="50">50分 (紅心)</button>
                    </div>
                    
                    <!-- 投擲控制 -->
                    <div class="throw-controls">
                        <button class="throw-btn" id="throw-dart-btn" disabled>投擲飛鏢</button>
                        <button class="throw-btn" id="start-game-btn">開始遊戲</button>
                        <button class="throw-btn end-game-btn" id="end-game-btn">結束遊戲</button>
                    </div>
                </div>
            </div>
            
            <!-- 側邊欄 -->
            <div class="sidebar">
                <!-- 玩家資訊 -->
                <div class="player-info">
                    <h3>👤 我的資訊</h3>
                    <div class="player-stats">
                        <div class="stat-item">
                            <span class="stat-label">姓名:</span>
                            <span class="stat-value" id="player-name">-</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">目前分數:</span>
                            <span class="stat-value current-score" id="current-score">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">投擲次數:</span>
                            <span class="stat-value" id="throw-count">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">遊戲時間:</span>
                            <span class="stat-value" id="game-time">00:00</span>
                        </div>
                    </div>
                </div>
                
                <!-- 房間玩家 -->
                <div class="room-players">
                    <h3>👥 房間玩家</h3>
                    <div class="player-list" id="player-list">
                        <div class="player-item">
                            <span class="player-name">載入中...</span>
                            <span class="player-score">-</span>
                        </div>
                    </div>
                </div>
                
                <!-- 排行榜 -->
                <div class="leaderboard">
                    <h3>🏆 排行榜</h3>
                    <div class="leaderboard-list" id="leaderboard-list">
                        <div class="leaderboard-item">
                            <span class="rank">載入中...</span>
                            <span>-</span>
                            <span>-</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script type="module" src="../../src/game/dart-game.js"></script>
</body>
</html>
