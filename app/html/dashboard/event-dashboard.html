<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>活動儀表板</title>
    <link rel="stylesheet" href="../../css/style.css">
    <style>
        .dashboard-container {
            max-width: 1400px;
            margin: 20px auto;
            padding: 20px;
            background: #f8f9fa;
            min-height: 100vh;
        }
        
        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .dashboard-header h1 {
            margin: 0 0 10px 0;
            font-size: 2.5em;
        }
        
        .dashboard-header p {
            margin: 0;
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .stats-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            text-align: center;
            border-left: 5px solid #007bff;
        }
        
        .stat-card.registration {
            border-left-color: #28a745;
        }
        
        .stat-card.checkin {
            border-left-color: #ffc107;
        }
        
        .stat-card.game {
            border-left-color: #dc3545;
        }
        
        .stat-card.room {
            border-left-color: #6f42c1;
        }
        
        .stat-card h3 {
            margin: 0 0 15px 0;
            color: #495057;
            font-size: 1.1em;
        }
        
        .stat-number {
            font-size: 3em;
            font-weight: bold;
            color: #007bff;
            margin: 10px 0;
        }
        
        .stat-card.registration .stat-number {
            color: #28a745;
        }
        
        .stat-card.checkin .stat-number {
            color: #ffc107;
        }
        
        .stat-card.game .stat-number {
            color: #dc3545;
        }
        
        .stat-card.room .stat-number {
            color: #6f42c1;
        }
        
        .stat-subtitle {
            color: #6c757d;
            font-size: 0.9em;
        }
        
        .dashboard-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
        }
        
        .main-content {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }
        
        .sidebar-content {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .section-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .section-card h2 {
            margin: 0 0 20px 0;
            color: #495057;
            font-size: 1.5em;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
        }
        
        .room-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .room-card {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            transition: all 0.3s;
        }
        
        .room-card:hover {
            border-color: #007bff;
            box-shadow: 0 4px 8px rgba(0, 123, 255, 0.2);
        }
        
        .room-card.active {
            border-color: #28a745;
            background: #d4edda;
        }
        
        .room-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .room-name {
            font-weight: bold;
            color: #495057;
            font-size: 1.1em;
        }
        
        .room-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
            color: white;
        }
        
        .room-status.active {
            background: #28a745;
        }
        
        .room-status.inactive {
            background: #6c757d;
        }
        
        .room-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
        }
        
        .room-stat {
            text-align: center;
            padding: 10px;
            background: white;
            border-radius: 4px;
            border: 1px solid #dee2e6;
        }
        
        .room-stat-number {
            font-size: 1.5em;
            font-weight: bold;
            color: #007bff;
        }
        
        .room-stat-label {
            font-size: 0.8em;
            color: #6c757d;
            margin-top: 5px;
        }
        
        .recent-activity {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .activity-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .activity-item {
            display: flex;
            align-items: center;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #007bff;
        }
        
        .activity-item.checkin {
            border-left-color: #28a745;
        }
        
        .activity-item.game {
            border-left-color: #dc3545;
        }
        
        .activity-item.registration {
            border-left-color: #ffc107;
        }
        
        .activity-icon {
            font-size: 1.5em;
            margin-right: 15px;
        }
        
        .activity-content {
            flex: 1;
        }
        
        .activity-title {
            font-weight: bold;
            color: #495057;
            margin-bottom: 5px;
        }
        
        .activity-details {
            font-size: 0.9em;
            color: #6c757d;
        }
        
        .activity-time {
            font-size: 0.8em;
            color: #adb5bd;
            margin-left: 15px;
        }
        
        .leaderboard-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .leaderboard-table th,
        .leaderboard-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        
        .leaderboard-table th {
            background: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }
        
        .leaderboard-table tr:hover {
            background: #f8f9fa;
        }
        
        .rank-badge {
            display: inline-block;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #007bff;
            color: white;
            text-align: center;
            line-height: 30px;
            font-weight: bold;
            font-size: 0.9em;
        }
        
        .rank-badge.gold {
            background: #ffd700;
            color: #333;
        }
        
        .rank-badge.silver {
            background: #c0c0c0;
            color: #333;
        }
        
        .rank-badge.bronze {
            background: #cd7f32;
            color: white;
        }
        
        .refresh-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin-bottom: 20px;
        }
        
        .refresh-btn:hover {
            background: #0056b3;
        }
        
        .auto-refresh {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
            font-size: 0.9em;
            color: #6c757d;
        }
        
        .auto-refresh input[type="checkbox"] {
            transform: scale(1.2);
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #6c757d;
        }
        
        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @media (max-width: 1024px) {
            .dashboard-content {
                grid-template-columns: 1fr;
            }
            
            .stats-overview {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }
            
            .room-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- 儀表板標題 -->
        <div class="dashboard-header">
            <h1>📊 活動管理儀表板</h1>
            <p id="exhibition-info">載入展覽資訊中...</p>
        </div>
        
        <!-- 統計概覽 -->
        <div class="stats-overview">
            <div class="stat-card registration">
                <h3>📝 總報名人數</h3>
                <div class="stat-number" id="total-registrations">0</div>
                <div class="stat-subtitle">今日新增: <span id="today-registrations">0</span></div>
            </div>
            
            <div class="stat-card checkin">
                <h3>✅ 簽到人數</h3>
                <div class="stat-number" id="total-checkins">0</div>
                <div class="stat-subtitle">今日簽到: <span id="today-checkins">0</span></div>
            </div>
            
            <div class="stat-card game">
                <h3>🎮 遊戲參與</h3>
                <div class="stat-number" id="total-games">0</div>
                <div class="stat-subtitle">完成率: <span id="completion-rate">0%</span></div>
            </div>
            
            <div class="stat-card room">
                <h3>🏠 活躍房間</h3>
                <div class="stat-number" id="active-rooms">0</div>
                <div class="stat-subtitle">總房間數: <span id="total-rooms">0</span></div>
            </div>
        </div>
        
        <div class="dashboard-content">
            <!-- 主要內容 -->
            <div class="main-content">
                <!-- 房間狀態 -->
                <div class="section-card">
                    <h2>🏠 房間狀態監控</h2>
                    <div class="auto-refresh">
                        <input type="checkbox" id="auto-refresh-rooms" checked>
                        <label for="auto-refresh-rooms">自動刷新 (30秒)</label>
                        <button class="refresh-btn" id="refresh-rooms-btn">立即刷新</button>
                    </div>
                    <div class="room-grid" id="room-grid">
                        <div class="loading">載入房間資訊中...</div>
                    </div>
                </div>
                
                <!-- 排行榜 -->
                <div class="section-card">
                    <h2>🏆 遊戲排行榜</h2>
                    <button class="refresh-btn" id="refresh-leaderboard-btn">刷新排行榜</button>
                    <table class="leaderboard-table">
                        <thead>
                            <tr>
                                <th>排名</th>
                                <th>玩家姓名</th>
                                <th>分數</th>
                                <th>房間</th>
                                <th>遊戲時間</th>
                                <th>完成時間</th>
                            </tr>
                        </thead>
                        <tbody id="leaderboard-body">
                            <tr>
                                <td colspan="6" class="loading">載入排行榜中...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- 側邊欄 -->
            <div class="sidebar-content">
                <!-- 即時活動 -->
                <div class="section-card">
                    <h2>⚡ 即時活動</h2>
                    <div class="auto-refresh">
                        <input type="checkbox" id="auto-refresh-activity" checked>
                        <label for="auto-refresh-activity">自動刷新 (10秒)</label>
                    </div>
                    <div class="recent-activity">
                        <div class="activity-list" id="activity-list">
                            <div class="loading">載入活動記錄中...</div>
                        </div>
                    </div>
                </div>
                
                <!-- 快速操作 -->
                <div class="section-card">
                    <h2>⚙️ 快速操作</h2>
                    <div style="display: flex; flex-direction: column; gap: 10px;">
                        <button class="refresh-btn" id="export-data-btn">📊 匯出統計數據</button>
                        <button class="refresh-btn" id="view-registrations-btn">👥 查看報名列表</button>
                        <button class="refresh-btn" id="manage-rooms-btn">🏠 管理房間</button>
                        <button class="refresh-btn" id="system-settings-btn">⚙️ 系統設定</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="module" src="../../src/dashboard/event-dashboard.js"></script>
</body>
</html>
