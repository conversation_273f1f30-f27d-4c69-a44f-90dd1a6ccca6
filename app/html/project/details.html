<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>Project Details</title>
    <link rel="stylesheet" href="../../css/styles.css">
    <link rel="stylesheet" href="../../css/project-details.css">
</head>

<body>
    <header>
        <div class="header-content">
            <label class="title">TIANYEN</label>
            <nav class="header-nav">
                <button id="themeToggle" class="theme-toggle" aria-label="Toggle theme">
                    <span class="theme-icon">🌙</span>
                </button>

                <div class="nav-item dropdown">
                    <a id="languageBtn" data-i18n="language" href="#" class="nav-link">Language</a>
                    <div class="dropdown-menu">
                        <a id="ENBtn" data-i18n="english" href="#" class="dropdown-item">English</a>
                        <a id="ZHTWBtn" data-i18n="zh-tw" href="#" class="dropdown-item">zh-TW</a>
                    </div>
                </div>

                <div class="nav-item dropdown">
                    <a id="openUserDialog" data-i18n="user" href="#" class="nav-link">User</a>
                    <div class="dropdown-menu">
                        <a id="informationBtn" data-i18n="information" href="/html/user/details.html"
                            class="dropdown-item">Information</a>
                        <a id="settingsBtn" data-i18n="settings" href="/html/user/settings.html"
                            class="dropdown-item">Settings</a>
                        <a id="signInAndOutBtn" data-i18n="sign_out" href="#" class="dropdown-item">Sign Out</a>
                    </div>
                </div>

                <div class="nav-item">
                    <a id="projectsBtn" data-i18n="projects" href="/html/project/list.html"
                        class="nav-link">Projects</a>
                </div>
            </nav>
        </div>
        <script type="module" src="/src/user/userDialog.js"></script>
        <script type="module" src="/src/utils/theme.js"></script>
    </header>
    <div>
        <div class="btn-list">
            <button onclick="window.location.href='../dashboard.html';">Back To Menu</button>
            <button class="thistime" onclick="window.location.href='../project/details.html';">詳細資訊</button>
            <button onclick="window.location.href='../project/statistics.html';">數據統計</button>
            <button onclick="window.location.href='../project/monitoring.html';">運行狀態監控</button>
            <button onclick="window.location.href='../exhibition/list.html';">展覽館管理</button>
            <button onclick="window.location.href='../group/list.html';">人員與群組管理</button>
        </div>
        <p class="projectName">［專案名稱］</p>
        <div class="project-details-maincontent">
            <h2 class="title-name">詳細資訊</h2>
            <div class="details-header">
                <div id="descriptionText"></div>
            </div>

            <!-- <div>
                    <label>expiration time:</label>
                    <label id="expirationTimeText"></label>
                </div>
                <div>
                    <b>Daily opening and closing times</b>
                    <br>
                    <label id="opening-closing-times"></label>
                    <label for="startTimeText">Start Time:</label>
                    <label id="startTimeText">123</label>   
                    <br>
                    <label for="endTimeText">End Time:</label>
                    <label  id="endTimeText"></label>
                </div> -->
            <!-- <canvas id="qr-container"></canvas> -->
            <!-- <script src="../../src/bundle.js"></script> -->
            <script type="module" src="../../src/project/title.js"></script>
            <script type="module" src="../../src/project/details.js"></script>

        </div>
    </div>
</body>

</html>