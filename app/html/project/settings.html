<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <title>Project Details</title>
        <link rel="stylesheet" href="../../css/styles.css">
        <link rel="stylesheet" href="../../css/project-statistics.css">
        <script src="https://code.jquery.com/jquery-3.5.1.js"></script>
        <script src="https://cdn.datatables.net/1.11.3/js/jquery.dataTables.min.js"></script>
        <script src="https://cdn.datatables.net/responsive/2.2.6/js/dataTables.responsive.min.js"></script>
    </head>
    <body>
        <header>
            <label>TIANYEN</label>
            <a id="projectsBtn" data-i18n="projects" href="/html/project/list.html">Projects</a>
            <a id="openUserDialog" data-i18n="user" href="">User</a>
            <div class="dialog-container">
                <dialog id="userDialog">
                    <ul>
                        <li><a id="informationBtn" data-i18n="information" href="/html/user/details.html">Information</a></li>
                        <li><a id="settingsBtn" data-i18n="settings" href="/html/user/settings.html">Settings</a></li>
                        <li><a id="signInAndOutBtn" data-18n="sign_in" href="">SignOut</a></li>
                    </ul>   
                </dialog>
            </div>
                <a id="languageBtn" data-i18n="language" href="">Language</a>
                <div class="dialog-container">
                    <dialog id="langDialog">
                        <ul>
                            <li><a id="ENBtn" data-i18n="english" href="">English</a></li>
                            <li><a id="ZHTWBtn" data-i18n="zh-tw" href="">zh-TW</a></li>
                        </ul>
                    </dialog>
                </div>
            <script type="module" src="/src/user/userDialog.js"></script>
        </header>
        <div>
            <div class="btn-list">
                <button onclick="window.location.href='../dashboard.html';">Back To Menu</button>
                <button onclick="window.location.href='../project/details.html';">詳細資訊</button>
                <button class="thistime" onclick="window.location.href='../project/statistics.html';">數據統計</button>
                <button onclick="window.location.href='../project/monitoring.html';">運行狀態監控</button>
                <button onclick="window.location.href='../exhibition/list.html';">展覽館管理</button>
                <button onclick="window.location.href='../group/list.html';">人員與群組管理</button>
            </div>
            <p class="projectName">［專案名稱］</p>
            <div class="project-statistics-maincontent">
                <h2 class="title-name">設定</h2>
                <form id="edit-form">
                    <div>
                        <label id="title_text">Title</label>
                        <input type="text" id="title" name="name"/>
                    </div>
                    <div>
                        <label id="description_text">Description</label>
                        <input type="text" id="description" name="description"/>
                    </div>
                    <!-- <label data-i18n="daily_times">Daily opening and closing times  <input type="checkbox" id="switch"/></label>
                    <div id="opening-closing-times" style="display:none">
                        <br>
                        <label for="startTime"><span data-i18n="start_time">Start Time</span>:</label>
                        <input type="time" id="startTime" name="start_time">
                        
                        <label for="endTime"><span data-i18n="end_time">End Time</span>:</label>
                        <input type="time" id="endTime" name="end_time">
                    </div> -->
                    <label id="error" style="color:red"></label>
                    <button id="submit" type="submit">Submit</button>
                </form>
                
                <script type="module" src="../../src/project/settings.js"></script>                
                <script type="module" src="../../src/project/title.js"></script>
            </div>
        </div>
    </body>
</html>



