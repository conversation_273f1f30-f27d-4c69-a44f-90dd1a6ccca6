<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard</title>
    <link rel="stylesheet" href="../../css/styles.css">
    <script type='module' src="/src/translation.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <link rel="stylesheet" href="../../css/project-list.css">
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.2.6/js/dataTables.responsive.min.js"></script>
</head>

<body>
    <header>
        <div class="header-content">
            <label class="title">TIANYEN</label>
            <nav class="header-nav">
                <button id="themeToggle" class="theme-toggle" aria-label="Toggle theme">
                    <span class="theme-icon">🌙</span>
                </button>

                <div class="nav-item dropdown">
                    <a id="languageBtn" data-i18n="language" href="#" class="nav-link">Language</a>
                    <div class="dropdown-menu">
                        <a id="ENBtn" data-i18n="english" href="#" class="dropdown-item">English</a>
                        <a id="ZHTWBtn" data-i18n="zh-tw" href="#" class="dropdown-item">zh-TW</a>
                    </div>
                </div>

                <div class="nav-item dropdown">
                    <a id="openUserDialog" data-i18n="user" href="#" class="nav-link">User</a>
                    <div class="dropdown-menu">
                        <a id="informationBtn" data-i18n="information" href="/html/user/details.html"
                            class="dropdown-item">Information</a>
                        <a id="settingsBtn" data-i18n="settings" href="/html/user/settings.html"
                            class="dropdown-item">Settings</a>
                        <a id="signInAndOutBtn" data-i18n="sign_out" href="#" class="dropdown-item">Sign Out</a>
                    </div>
                </div>

                <div class="nav-item">
                    <a id="projectsBtn" data-i18n="projects" href="/html/project/list.html"
                        class="nav-link active">Projects</a>
                </div>
            </nav>
        </div>
        <script type="module" src="/src/user/userDialog.js"></script>
    </header>
    <div class="project-list-place">
        <div class="top-title">
            <p class="list-title">專案列表</p>
            <input type="button" id="create_project_btn"></input>
            <input type="button" id="delete_project_btn"></input>
            <input type="button" value="Join" id="join_group_btn"></input>
        </div>

        <div class="table-hint">
            <span class="hint-icon">💡</span>
            <span data-i18n="double_click_hint">雙擊項目列表中的任一行即可進入該項目</span>
        </div>

        <table id="projects-table">
            <thead id="title">
                <tr>
                    <th data-i18n="id">ID</th>
                    <th data-i18n="name">Name</th>
                    <th data-i18n="status">Status</th>
                    <th data-i18n="created_by">CreatedBy</th>
                </tr>
            </thead>
            <tbody id="content"></tbody>
        </table>
    </div>


    <script src="../../src/project/projects.js" type="module"></script>
    <script>
        // Theme toggle functionality
        const themeToggle = document.getElementById('themeToggle');
        const themeIcon = document.querySelector('.theme-icon');
        const body = document.body;

        // Check for saved theme preference or default to 'dark'
        const currentTheme = localStorage.getItem('theme') || 'dark';
        body.setAttribute('data-theme', currentTheme);
        updateThemeIcon(currentTheme);

        themeToggle.addEventListener('click', () => {
            const currentTheme = body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            body.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
            updateThemeIcon(newTheme);
        });

        function updateThemeIcon(theme) {
            themeIcon.textContent = theme === 'dark' ? '☀️' : '🌙';
        }
    </script>
</body>

</html>