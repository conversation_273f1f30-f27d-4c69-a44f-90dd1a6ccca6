<!DOCTYPE html>
<html lang="zh-tw">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Project</title>
    <link rel="stylesheet" href="../../css/styles.css">
    <link rel="stylesheet" href="../../css/create.css">
    <script type='module' src="/src/translation.js"></script>
</head>

<body>
    <header>
        <div class="header-content">
            <label class="title">TIANYEN</label>
            <nav class="header-nav">
                <button id="themeToggle" class="theme-toggle" aria-label="Toggle theme">
                    <span class="theme-icon">🌙</span>
                </button>

                <div class="nav-item dropdown">
                    <a id="languageBtn" data-i18n="language" href="#" class="nav-link">Language</a>
                    <div class="dropdown-menu">
                        <a id="ENBtn" data-i18n="english" href="#" class="dropdown-item">English</a>
                        <a id="ZHTWBtn" data-i18n="zh-tw" href="#" class="dropdown-item">zh-TW</a>
                    </div>
                </div>

                <div class="nav-item dropdown">
                    <a id="openUserDialog" data-i18n="user" href="#" class="nav-link">User</a>
                    <div class="dropdown-menu">
                        <a id="informationBtn" data-i18n="information" href="/html/user/details.html"
                            class="dropdown-item">Information</a>
                        <a id="settingsBtn" data-i18n="settings" href="/html/user/settings.html"
                            class="dropdown-item">Settings</a>
                        <a id="signInAndOutBtn" data-i18n="sign_out" href="#" class="dropdown-item">Sign Out</a>
                    </div>
                </div>

                <div class="nav-item">
                    <a id="projectsBtn" data-i18n="projects" href="/html/project/list.html"
                        class="nav-link">Projects</a>
                </div>
            </nav>
        </div>
        <script type="module" src="/src/user/userDialog.js"></script>
    </header>
    <div>
        <!-- <div class="btn-list">
            <button onclick="window.location.href='../project/details.html';">詳細資訊</button>
            <button onclick="window.location.href='../project/statistics.html';">數據統計</button>
            <button class="thistime" onclick="window.location.href='../project/monitoring.html';">運行狀態監控</button>
            <button onclick="window.location.href='../exhibition/list.html';">展覽館管理</button>
            <button onclick="window.location.href='../group/list.html';">人員與群組管理</button>
        </div>
        <p class="projectName">［專案名稱］</p> -->
        <div class="maincontent">
            <form id="project_form">
                <label data-i18n="name">Project Name</label>
                <input type="text" name="name"></input>
                <label data-i18n="description">Description</label>
                <input type="text" name="description"></input>
                <label id="error_message" style="color:red"></label>
                <input type="submit" value="submit"></input>
            </form>
        </div>
    </div>
    <script type="module" src="../../src/project/create.js"></script>
    <script>
        // Theme toggle functionality
        const themeToggle = document.getElementById('themeToggle');
        const themeIcon = document.querySelector('.theme-icon');
        const body = document.body;

        // Check for saved theme preference or default to 'dark'
        const currentTheme = localStorage.getItem('theme') || 'dark';
        body.setAttribute('data-theme', currentTheme);
        updateThemeIcon(currentTheme);

        themeToggle.addEventListener('click', () => {
            const currentTheme = body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            body.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
            updateThemeIcon(newTheme);
        });

        function updateThemeIcon(theme) {
            themeIcon.textContent = theme === 'dark' ? '☀️' : '🌙';
        }
    </script>
</body>

</html>