<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <title>List</title>
        <link rel="stylesheet" href="css/styles.css">
        <script type='module' src="/src/translation.js"></script>
    </head>
    <body>
        <header>
            <label>IAMM</label>
            <a id="projectsBtn" data-i18n="projects" href="/html/project/list.html">Projects</a>
            <a id="openUserDialog" data-i18n="user" href="">User</a>
            <div class="dialog-container">
                <dialog id="userDialog">
                    <ul>
                        <li><a id="informationBtn" data-i18n="information" href="/html/user/details.html">Information</a></li>
                        <li><a id="settingsBtn" data-i18n="settings" href="/html/user/settings.html">Settings</a></li>
                        <li><a id="signInAndOutBtn" data-18n="sign_in" href="">SignOut</a></li>
                    </ul>   
                </dialog>
            </div>
                <a id="languageBtn" data-i18n="language" href="">Language</a>
                <div class="dialog-container">
                    <dialog id="langDialog">
                        <ul>
                            <li><a id="ENBtn" data-i18n="english" href="">English</a></li>
                            <li><a id="ZHTWBtn" data-i18n="zh-tw" href="">zh-TW</a></li>
                        </ul>
                    </dialog>
                </div>
            <script type="module" src="/src/user/userDialog.js"></script>
        </header>
        <div>
            <button data-i18n="create" id="create_btn">Create</button>
            <button data-i18n="delete" id="delete_btn">Delete</button>
        </div>
        <table id="table">
            <thead>
                <tr id="titles">
                    <th>id</th>
                    <th>product id</th>
                    <th>user id</th>
                    <th>count</th>
                    <th>timestamp</th>
                    <th>used[0:unused, 1:used]</th>
                </tr>
            </thead>
        </table>
        <script type="module" src="/frontend/public/src/voucher/list.js"></script>
    </body>
</html>