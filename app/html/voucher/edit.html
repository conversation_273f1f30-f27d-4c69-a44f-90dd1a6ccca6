<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <title>Edit</title>
        <link rel="stylesheet" href="css/styles.css">
        <script type='module' src="/src/translation.js"></script>
    </head>
    <body>
        <header>
            <label>IAMM</label>
            <a id="projectsBtn" data-i18n="projects" href="/html/project/list.html">Projects</a>
            <a id="openUserDialog" data-i18n="user" href="">User</a>
            <div class="dialog-container">
                <dialog id="userDialog">
                    <ul>
                        <li><a id="informationBtn" data-i18n="information" href="/html/user/details.html">Information</a></li>
                        <li><a id="settingsBtn" data-i18n="settings" href="/html/user/settings.html">Settings</a></li>
                        <li><a id="signInAndOutBtn" data-18n="sign_in" href="">SignOut</a></li>
                    </ul>   
                </dialog>
            </div>
                <a id="languageBtn" data-i18n="language" href="">Language</a>
                <div class="dialog-container">
                    <dialog id="langDialog">
                        <ul>
                            <li><a id="ENBtn" data-i18n="english" href="">English</a></li>
                            <li><a id="ZHTWBtn" data-i18n="zh-tw" href="">zh-TW</a></li>
                        </ul>
                    </dialog>
                </div>
            <script type="module" src="/src/user/userDialog.js"></script>
        </header>
        <form id = "voucher_form">
            <div>
                <label><span data-i18n="name">Name</span>:</label>
                <input type="text" name="name" for="name" id="name" required />
            </div>
           
            <div>
                <label><span data-i18n="count">Count</span>:</label>
                <input type="count" name="count" for="count" id="count" required />
            </div>
            <label id="error_message" style="color:red"></label>
            <input data-i18n="submit" type="submit" value="submit"/>
        </form>
        <script src="/frontend/public/src/voucher/edit.js" type="module"></script>
    </body>
</html>