<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <title>View</title>
        <link rel="stylesheet" href="css/styles.css">
        <script type='module' src="/src/translation.js"></script>
    </head>
    <body>
        <header>
            <label>IAMM</label>
            <a id="projectsBtn" data-i18n="projects" href="/html/project/list.html">Projects</a>
            <a id="openUserDialog" data-i18n="user" href="">User</a>
            <div class="dialog-container">
                <dialog id="userDialog">
                    <ul>
                        <li><a id="informationBtn" data-i18n="information" href="/html/user/details.html">Information</a></li>
                        <li><a id="settingsBtn" data-i18n="settings" href="/html/user/settings.html">Settings</a></li>
                        <li><a id="signInAndOutBtn" data-18n="sign_in" href="">SignOut</a></li>
                    </ul>   
                </dialog>
            </div>
                <a id="languageBtn" data-i18n="language" href="">Language</a>
                <div class="dialog-container">
                    <dialog id="langDialog">
                        <ul>
                            <li><a id="ENBtn" data-i18n="english" href="">English</a></li>
                            <li><a id="ZHTWBtn" data-i18n="zh-tw" href="">zh-TW</a></li>
                        </ul>
                    </dialog>
                </div>
            <script type="module" src="/src/user/userDialog.js"></script>
        </header>
        <div>
            <button id="edit_btn">Edit</button>
            <label><span data-i18n="name">Name</span>:</label>
            <label id="nameText"></label>
            <label><span data-i18n="count">Count</span>:</label>
            <label id="countText"></label>
            <script src="/frontend/public/src/voucher/view.js" type="module"></script>
        </div>
    </body>
</html>