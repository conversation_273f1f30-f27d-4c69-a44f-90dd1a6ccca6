<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>活動簽到</title>
    <link rel="stylesheet" href="../../css/style.css">
    <style>
        .checkin-container {
            max-width: 800px;
            margin: 20px auto;
            padding: 30px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .room-info {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 6px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .room-info h1 {
            margin: 0 0 10px 0;
            color: #1976d2;
            font-size: 2em;
        }
        
        .room-info p {
            margin: 5px 0;
            color: #666;
            font-size: 1.1em;
        }
        
        .qr-scanner-container {
            text-align: center;
            margin: 30px 0;
        }
        
        .scanner-area {
            position: relative;
            max-width: 400px;
            margin: 0 auto;
            border: 3px dashed #007bff;
            border-radius: 10px;
            padding: 40px 20px;
            background: #f8f9fa;
        }
        
        .scanner-icon {
            font-size: 4em;
            color: #007bff;
            margin-bottom: 20px;
        }
        
        .scanner-text {
            font-size: 1.2em;
            color: #666;
            margin-bottom: 20px;
        }
        
        .manual-input-btn {
            background: #6c757d;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
        }
        
        .manual-input-btn:hover {
            background: #5a6268;
        }
        
        .manual-input {
            display: none;
            margin-top: 20px;
            gap: 10px;
        }
        
        .manual-input input {
            flex: 1;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
        }
        
        .manual-input button {
            background: #007bff;
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
        }
        
        .manual-input button:hover {
            background: #0056b3;
        }
        
        .welcome-message {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 20px;
            border-radius: 6px;
            margin: 20px 0;
            text-align: center;
            display: none;
        }
        
        .welcome-message h2 {
            margin: 0 0 10px 0;
            font-size: 2em;
        }
        
        .welcome-message p {
            margin: 5px 0;
            font-size: 1.1em;
        }
        
        .error-message {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 20px;
            border-radius: 6px;
            margin: 20px 0;
            text-align: center;
            display: none;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            display: none;
        }
        
        .loading::after {
            content: '';
            display: inline-block;
            width: 30px;
            height: 30px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .stat-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            text-align: center;
            border: 1px solid #dee2e6;
        }
        
        .stat-card h3 {
            margin: 0 0 10px 0;
            color: #495057;
            font-size: 1em;
        }
        
        .stat-card .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
        
        .recent-checkins {
            margin-top: 30px;
        }
        
        .recent-checkins h3 {
            margin-bottom: 15px;
            color: #495057;
        }
        
        .checkin-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 6px;
        }
        
        .checkin-item {
            padding: 15px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .checkin-item:last-child {
            border-bottom: none;
        }
        
        .checkin-item:nth-child(even) {
            background: #f8f9fa;
        }
        
        .checkin-name {
            font-weight: bold;
            color: #495057;
        }
        
        .checkin-time {
            color: #6c757d;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="checkin-container">
        <!-- 房間資訊 -->
        <div class="room-info">
            <h1 id="room-name">載入中...</h1>
            <p id="room-description">房間資訊載入中...</p>
            <p><strong>房間 ID:</strong> <span id="room-id">-</span></p>
        </div>
        
        <!-- 載入中 -->
        <div class="loading" id="loading">處理中，請稍候...</div>
        
        <!-- 錯誤訊息 -->
        <div class="error-message" id="error-message"></div>
        
        <!-- 歡迎訊息 -->
        <div class="welcome-message" id="welcome-message">
            <h2>🎉 歡迎報到！</h2>
            <p id="welcome-text"></p>
            <p id="welcome-details"></p>
        </div>
        
        <!-- QR Code 掃描區域 -->
        <div class="qr-scanner-container" id="scanner-container">
            <div class="scanner-area">
                <div class="scanner-icon">📱</div>
                <div class="scanner-text">請掃描報名 QR Code 進行簽到</div>
                <button class="manual-input-btn" id="manual-input-btn">手動輸入 QR Code</button>
                
                <div class="manual-input" id="manual-input">
                    <input type="text" id="qr-code-input" placeholder="請輸入 QR Code">
                    <button id="manual-checkin-btn">簽到</button>
                </div>
            </div>
        </div>
        
        <!-- 統計資訊 -->
        <div class="stats-container" id="stats-container">
            <div class="stat-card">
                <h3>今日簽到人數</h3>
                <div class="stat-number" id="today-checkins">0</div>
            </div>
            <div class="stat-card">
                <h3>總簽到人數</h3>
                <div class="stat-number" id="total-checkins">0</div>
            </div>
            <div class="stat-card">
                <h3>遊戲參與人數</h3>
                <div class="stat-number" id="game-participants">0</div>
            </div>
            <div class="stat-card">
                <h3>目前房間人數</h3>
                <div class="stat-number" id="current-players">0</div>
            </div>
        </div>
        
        <!-- 最近簽到記錄 -->
        <div class="recent-checkins">
            <h3>最近簽到記錄</h3>
            <div class="checkin-list" id="checkin-list">
                <div class="checkin-item">
                    <span class="checkin-name">載入中...</span>
                    <span class="checkin-time">-</span>
                </div>
            </div>
        </div>
    </div>

    <script type="module" src="../../src/event/checkin.js"></script>
</body>
</html>
