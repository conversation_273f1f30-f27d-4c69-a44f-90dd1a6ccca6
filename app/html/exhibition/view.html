<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Exhibition Dashboard</title>
  <link rel="stylesheet" href="../../css/styles.css">
  <link rel="stylesheet" href="../../css/dashboard.css">
  <link rel="stylesheet" href="https://cdn.datatables.net/1.11.3/css/jquery.dataTables.min.css">
  <link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.2.6/css/responsive.dataTables.min.css">
  <script src="https://code.jquery.com/jquery-3.5.1.js"></script>
  <script src="https://cdn.datatables.net/1.11.3/js/jquery.dataTables.min.js"></script>
  <script src="https://cdn.datatables.net/responsive/2.2.6/js/dataTables.responsive.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/socket.io-client@4.8.0/dist/socket.io.js"></script>
</head>
<body>
    <header>
        <label>TIANYEN</label>
        <a id="projectsBtn" data-i18n="projects" href="/html/project/list.html">Projects</a>
        <a id="openUserDialog" data-i18n="user" href="">User</a>
        <div class="dialog-container">
            <dialog id="userDialog">
                <ul>
                    <li><a id="informationBtn" data-i18n="information" href="/html/user/details.html">Information</a></li>
                    <li><a id="settingsBtn" data-i18n="settings" href="/html/user/settings.html">Settings</a></li>
                    <li><a id="signInAndOutBtn" data-18n="sign_in" href="">SignOut</a></li>
                </ul>   
            </dialog>
        </div>
            <a id="languageBtn" data-i18n="language" href="">Language</a>
            <div class="dialog-container">
                <dialog id="langDialog">
                    <ul>
                        <li><a id="ENBtn" data-i18n="english" href="">English</a></li>
                        <li><a id="ZHTWBtn" data-i18n="zh-tw" href="">zh-TW</a></li>
                    </ul>
                </dialog>
            </div>
        <script type="module" src="/src/user/userDialog.js"></script>
    </header>
    <p class="exhibitionName">［展示廳名稱］</p>
    <nav class="maincontent">
        <button class="mainbtn" data-i18n="back_to_list" id="backBtn">返回專案</button>
        <button class="mainbtn" data-i18n="details" id="detailsBtn">詳細資訊</button>
        <button class="mainbtn" data-i18n="resources" id="resourcesBtn">資源配置</button>
        <button class="mainbtn" data-i18n="monitoring" id="monitoringBtn">運行狀態監控</button>
        <button class="mainbtn" data-i18n="statistics" id="statisticsBtn">數據統計</button>
        <button class="mainbtn" data-i18n="products" id="productListBtn">products</button>
        <button class="mainbtn" data-i18n="users" id="userListBtn">users</button>
        <button class="mainbtn" data-i18n="vouchers" id="voucherListBtn">vouchers</button>
        <button class="mainbtn" data-i18n="settings" id="settingBtn">設定</button>
        <button class="mainbtn" id="wantToExecuteBtn">執行</button>
        <button class="mainbtn" data-i18n="service" id="serviceBtn">服務</button>
    </nav>

    <div class="popup" id="executePopup">
        <button id="closeBtn" class="close">&times;</button><br>
        <label data-i18n="select_expiration_time">select expiration time</label>
        <input type="datetime-local" id="expirationTimeInput">
        <button data-i18n="execute" id="executeBtn">執行</button>
    </div>

    <!-- 動態載入內容的區域 -->
    <section id="content-section">
    </section>
    <script type="module" src="../../src/exhibition/title.js"></script>
    <script type="module" src="../../src/exhibition/view.js"></script>
</body>
</html>