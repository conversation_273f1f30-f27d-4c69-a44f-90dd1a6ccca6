<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <title>Settings</title>
        <link rel="stylesheet" href="../../css/styles.css">
        <link rel="stylesheet" href="../../css/exhibition-settings.css">
    </head>
    <body>
        <header>
            <label class="logo">TIANYEN</label>
            <a id="projectsBtn" data-i18n="projects" href="/html/project/list.html">Projects</a>
            <a id="openUserDialog" data-i18n="user" href="">User</a>
            <div class="dialog-container">
                <dialog id="userDialog">
                    <ul>
                        <li><a id="informationBtn" data-i18n="information" href="/html/user/details.html">Information</a></li>
                        <li><a id="settingsBtn" data-i18n="settings" href="/html/user/settings.html">Settings</a></li>
                        <li><a id="signInAndOutBtn" data-18n="sign_in" href="">SignOut</a></li>
                    </ul>   
                </dialog>
            </div>
                <a id="languageBtn" data-i18n="language" href="">Language</a>
                <div class="dialog-container">
                    <dialog id="langDialog">
                        <ul>
                            <li><a id="ENBtn" data-i18n="english" href="">English</a></li>
                            <li><a id="ZHTWBtn" data-i18n="zh-tw" href="">zh-TW</a></li>
                        </ul>
                    </dialog>
                </div>
            <script type="module" src="/src/user/userDialog.js"></script>
        </header>
        <div>
            <div class="btn-list">
                <button onclick="window.location.href='../exhibition/view.html';">Back To Menu</button>
                <button onclick="window.location.href='../exhibition/details.html';">詳細資訊</button>
                <button onclick="window.location.href='../video/list.html';">資源配置</button>
                <button onclick="window.location.href='../exhibition/monitoring.html';">運行狀態監控</button>
                <button onclick="window.location.href='../exhibition/statistics.html';">數據統計</button>
                <button onclick="window.location.href='../game/product-list.html';">產品列表</button>
                <button onclick="window.location.href='../game/user-list.html';">使用者列表</button>
                <button onclick="window.location.href='../game/voucher-list.html';">兌換券列表</button>
                <button class="thistime" onclick="window.location.href='../exhibition/settings.html';">設定</button>
            </div>
            <p class="exhibitionName">［展覽館名稱］</p>
            <div class="exhibition-settings-maincontent">
                <div class="top-title">
                    <p class="list-title">設定</p>
                </div>

                <form id="edit-form">
                    <div>
                        <label id="title_label">Title</label>
                        <input type="text" id="title_text" name="name"/>
                    </div>
                    <div>
                        <label id="description_label">Description</label>
                        <input type="text" id="description_text" name="description"/>
                    </div>
                    <span data-i18n="daily_times"></span>
                    <input type="checkbox" id="switch"/>
                    <div id="opening-closing-times" style="display:none">
                        <br>
                        <label for="startTime"><span data-i18n="start_time">Start Time</span>:</label>
                        <input type="time" id="startTime" name="start_time">
                        
                        <label for="endTime"><span data-i18n="end_time">End Time</span>:</label>
                        <input type="time" id="endTime" name="end_time">
                    </div>
                    <label data-i18n="selected_game">選定遊戲</label>
                    <select id="selected-game" for="selectedGame">
                        <option data-i18n="dart_game" value="LuckyDart">射飛鏢</option>
                    </select>
                    <label id="error" style="color:red"></label>
                    <button type="submit" id="submit">Submit</button>
                </form>
                <script type="module" src="../../src/exhibition/title.js"></script>
                <script type="module" src="../../src/exhibition/settings.js"></script>
            </div>

        </div>
    </body>
</html>


