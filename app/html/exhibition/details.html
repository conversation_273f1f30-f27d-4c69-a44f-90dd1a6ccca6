<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <title>Exhibition Details</title>
        <link rel="stylesheet" href="../../css/styles.css">
        <link rel="stylesheet" href="../../css/exhibition-details.css">
    </head>
    <body>
        <header>
            <label class="logo">TIANYEN</label>
            <a id="projectsBtn" data-i18n="projects" href="/html/project/list.html">Projects</a>
            <a id="openUserDialog" data-i18n="user" href="">User</a>
            <div class="dialog-container">
                <dialog id="userDialog">
                    <ul>
                        <li><a id="informationBtn" data-i18n="information" href="/html/user/details.html">Information</a></li>
                        <li><a id="settingsBtn" data-i18n="settings" href="/html/user/settings.html">Settings</a></li>
                        <li><a id="signInAndOutBtn" data-18n="sign_in" href="">SignOut</a></li>
                    </ul>   
                </dialog>
            </div>
                <a id="languageBtn" data-i18n="language" href="">Language</a>
                <div class="dialog-container">
                    <dialog id="langDialog">
                        <ul>
                            <li><a id="ENBtn" data-i18n="english" href="">English</a></li>
                            <li><a id="ZHTWBtn" data-i18n="zh-tw" href="">zh-TW</a></li>
                        </ul>
                    </dialog>
                </div>
            <script type="module" src="/src/user/userDialog.js"></script>
        </header>
        <div>
            <div class="btn-list">
                <button onclick="window.location.href='../exhibition/view.html';">Back To Menu</button>
                <button class="thistime"  onclick="window.location.href='../exhibition/details.html';">詳細資訊</button>
                <button onclick="window.location.href='../video/list.html';">資源配置</button>
                <button onclick="window.location.href='../exhibition/monitoring.html';">運行狀態監控</button>
                <button onclick="window.location.href='../exhibition/statistics.html';">數據統計</button>
                <button onclick="window.location.href='../game/product-list.html';">產品列表</button>
                <button onclick="window.location.href='../game/user-list.html';">使用者列表</button>
                <button onclick="window.location.href='../game/voucher-list.html';">兌換券列表</button>
                <button onclick="window.location.href='../exhibition/settings.html';">設定</button>
            </div>
            <p class="exhibitionName">［展覽館名稱］</p>
            <div class="exhibition-details-maincontent">
                <div class="top-title">
                    <p class="list-title">詳細資訊</p>
                </div>
                <div class="details-header">
                    <div id="descriptionText"></div>
                </div>
                <div>
                    <label><span data-i18n="expiration_time">expiration time</span>:</label>
                    <label id="expirationTimeText"></label>
                </div>
                <div>
                    <label data-i18n="daily_times">Daily opening and closing times</label>
                    <label id="opening-closing-times"></label>
                    <div>
                        <label for="startTimeText"><span data-i18n="start_time">Start Time</span>:</label>
                        <label id="startTimeText">not set</label>
                    </div>
                    <div>
                        <label for="endTimeText"><span data-i18n="end_time">end time</span>:</label>
                        <label  id="endTimeText">not set</label>
                    </div>
                </div>
                <table>
                    <tr>
                        <td data-i18n="video_player_qr_code">Video Displayer QR Code</td>
                        <td><canvas id="qr-container"></canvas></td>
                    </tr>
                    <tr>
                        <td data-i18n="vendor_controller_qr_code">Vending Machine Controller QR Code</td>
                        <td><canvas id="qr-vending-controller-container"></canvas></td>
                    </tr>
                    <tr>
                        <td data-i18n="vendor_displayer_qr_code">Vending Machine Displayer QR Code</td>
                        <td><canvas id="qr-vending-displayer-container"></canvas></td>
                    </tr>
                </table>
                <script type="module" src="../../src/exhibition/title.js"></script>
                <script type="module" src="../../src/bundle.js"></script>
                <script type="module" src="../../src/vending.js"></script>
                <script type="module" src="../../src/exhibition/details.js"></script>
            </div>
        </div>
    </body>
</html>

