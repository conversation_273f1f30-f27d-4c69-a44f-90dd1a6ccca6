<!DOCTYPE html>
<html lang="zh-tw">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Project</title>
    <link rel="stylesheet" href="../../css/styles.css">
    <link rel="stylesheet" href="../../css/create.css">
    <script type='module' src="/src/translation.js"></script>
</head>
<body>
    <header>
        <label>TIANYEN</label>
        <a id="projectsBtn" data-i18n="projects" href="/html/project/list.html">Projects</a>
        <a id="openUserDialog" data-i18n="user" href="">User</a>
        <div class="dialog-container">
            <dialog id="userDialog">
                <ul>
                    <li><a id="informationBtn" data-i18n="information" href="/html/user/details.html">Information</a></li>
                    <li><a id="settingsBtn" data-i18n="settings" href="/html/user/settings.html">Settings</a></li>
                    <li><a id="signInAndOutBtn" data-18n="sign_in" href="">SignOut</a></li>
                </ul>   
            </dialog>
        </div>
            <a id="languageBtn" data-i18n="language" href="">Language</a>
            <div class="dialog-container">
                <dialog id="langDialog">
                    <ul>
                        <li><a id="ENBtn" data-i18n="english" href="">English</a></li>
                        <li><a id="ZHTWBtn" data-i18n="zh-tw" href="">zh-TW</a></li>
                    </ul>
                </dialog>
            </div>
        <script type="module" src="/src/user/userDialog.js"></script>
    </header>
    <div>
        <div class="btn-list">
            <button onclick="window.location.href='../project/details.html';">詳細資訊</button>
            <button onclick="window.location.href='../project/statistics.html';">數據統計</button>
            <button class="thistime" onclick="window.location.href='../project/monitoring.html';">運行狀態監控</button>
            <button onclick="window.location.href='../exhibition/list.html';">展覽館管理</button>
            <button onclick="window.location.href='../group/list.html';">人員與群組管理</button>
        </div>
        <p class="projectName">［專案名稱］</p>
        <div class="maincontent">
            <form id="exhibition_form">
                <label data-i18n="exhibition_name">Exhibition Name</label>
                <input type="text" name="name"></input>
                <label data-i18n="description">Description</label>
                <input type="text" name="description"></input>
                <label id="error_message" style="color:red"></label>
                <input type="submit" value="submit"></input>
            </form>
        </div>
    </div>  
    <script type="module" src="../../src/exhibition/create.js"></script>
</body>
</html>