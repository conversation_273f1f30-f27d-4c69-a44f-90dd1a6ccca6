<!-- displayer.html -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube 全螢幕播放示例</title>
    <style>
        html, body {
            height: 100%;
            background-color: black;
            margin: 0;
            padding: 0;
            overflow: hidden; /* 隱藏滾動條 */
        }       
        /* 使 iframe 自適應 */
        .player {
            width: 100vw;
            height: 100vh; /* 根據需要設置高度 */
            border: none; /* 去掉邊框 */
        }
        button {
            margin-top: 10px;
            padding: 10px;
        }
    </style>    
</head>
<body>
    <label id="error_message" style="color:red; z-index: 1000;"></label>
    <iframe id="online-player" class="player" allow=" accelerometer; clipboard-write; encrypted-media; gyroscope; picture-in-picture;"  allowfullscreen></iframe>
    <video id="video-player" class="player" controls autoplay muted></video>
    <script src="../../libraries/module.bundle.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/socket.io-client@4.8.0/dist/socket.io.js"></script>
    <script src="https://player.vimeo.com/api/player.js"></script>
    <script src="../../src/video/displayer.js" type="module"></script>
</body>
</html>
