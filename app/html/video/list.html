<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <title>Video List</title>
        <link rel="stylesheet" href="../../css/styles.css">
        <link rel="stylesheet" href="../../css/video-list.css">
        <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
        <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
        <script src="https://cdn.datatables.net/responsive/2.2.6/js/dataTables.responsive.min.js"></script>
    </head>
    <body>
        <header>
            <label class="logo">TIANYEN</label>
            <a id="projectsBtn" data-i18n="projects" href="/html/project/list.html">Projects</a>
            <a id="openUserDialog" data-i18n="user" href="">User</a>
            <div class="dialog-container">
                <dialog id="userDialog">
                    <ul>
                        <li><a id="informationBtn" data-i18n="information" href="/html/user/details.html">Information</a></li>
                        <li><a id="settingsBtn" data-i18n="settings" href="/html/user/settings.html">Settings</a></li>
                        <li><a id="signInAndOutBtn" data-18n="sign_in" href="">SignOut</a></li>
                    </ul>   
                </dialog>
            </div>
                <a id="languageBtn" data-i18n="language" href="">Language</a>
                <div class="dialog-container">
                    <dialog id="langDialog">
                        <ul>
                            <li><a id="ENBtn" data-i18n="english" href="">English</a></li>
                            <li><a id="ZHTWBtn" data-i18n="zh-tw" href="">zh-TW</a></li>
                        </ul>
                    </dialog>
                </div>
            <script type="module" src="/src/user/userDialog.js"></script>
        </header>
        <div>
            <div class="btn-list">
                <button onclick="window.location.href='../exhibition/view.html';">Back To Menu</button>
                <button onclick="window.location.href='../exhibition/details.html';">詳細資訊</button>
                <button class="thistime" onclick="window.location.href='../video/list.html';">資源配置</button>
                <button onclick="window.location.href='../exhibition/monitoring.html';">運行狀態監控</button>
                <button onclick="window.location.href='../exhibition/statistics.html';">數據統計</button>
                <button onclick="window.location.href='../game/product-list.html';">產品列表</button>
                <button onclick="window.location.href='../game/user-list.html';">使用者列表</button>
                <button onclick="window.location.href='../game/voucher-list.html';">兌換券列表</button>
                <button onclick="window.location.href='../exhibition/settings.html';">設定</button>
            </div>

            <p class="exhibitionName">［展覽館名稱］</p>
            <div class="video-list-maincontent">
                <div class="top-title">
                    <p class="list-title">資源配置</p>
                    <input type="button" id="upload_video_btn"></input>
                    <input type="button" id="delete_video_btn"></input>
                    <input type="button" id="switch" value="active/inactive"></input>
                    <input type="button" id="select_all_btn" value="select all"></input>
                    <input type="button" id="select_inverse_btn" value="select inverse"></input>
                </div>
                <nav>
                </nav>
                <table id="videos-table">
                    <thead>
                        <tr id="titles">
                            <th>ID</th>
                            <th>Name</th>
                            <th>url</th>
                            <th>Status</th>
                            <th>uploader</th>
                            <th>duration</th>
                        </tr>
                    </thead>
                    <tbody id="content"></tbody>
                </table>
                <script type="module" src="../../src/exhibition/title.js"></script>
                <script src="../../src/video/resources.js" type="module"></script>
            </div>
        </div>
    </body>
</html>
