<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Services</title>
  <link rel="stylesheet" href="../../css/styles.css">
  <link rel="stylesheet" href="../../css/dashboard.css">
  <link rel="stylesheet" href="https://cdn.datatables.net/1.11.3/css/jquery.dataTables.min.css">
  <link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.2.6/css/responsive.dataTables.min.css">
  <script src="https://code.jquery.com/jquery-3.5.1.js"></script>
  <script src="https://cdn.datatables.net/1.11.3/js/jquery.dataTables.min.js"></script>
  <script src="https://cdn.datatables.net/responsive/2.2.6/js/dataTables.responsive.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/socket.io-client@4.8.0/dist/socket.io.js"></script>
</head>
<body>
    <header>
        <label>TIANYEN</label>
        <a id="projectsBtn" data-i18n="projects" href="/html/project/list.html">Projects</a>
        <a id="openUserDialog" data-i18n="user" href="">User</a>
        <div class="dialog-container">
            <dialog id="userDialog">
                <ul>
                    <li><a id="informationBtn" data-i18n="information" href="/html/user/details.html">Information</a></li>
                    <li><a id="settingsBtn" data-i18n="settings" href="/html/user/settings.html">Settings</a></li>
                    <li><a id="signInAndOutBtn" data-18n="sign_in" href="">SignOut</a></li>
                </ul>   
            </dialog>
        </div>
            <a id="languageBtn" data-i18n="language" href="">Language</a>
            <div class="dialog-container">
                <dialog id="langDialog">
                    <ul>
                        <li><a id="ENBtn" data-i18n="english" href="">English</a></li>
                        <li><a id="ZHTWBtn" data-i18n="zh-tw" href="">zh-TW</a></li>
                    </ul>
                </dialog>
            </div>
        <script type="module" src="/src/user/userDialog.js"></script>
    </header>
    <nav class="maincontent">
        <button class="mainbtn" data-i18n="back_to_list" id="backBtn">返回展示廳</button>
        <button class="mainbtn" data-i18n="claw_controller" id="clawControllerBtn">Claw Controller</button>
        <button class="mainbtn" data-i18n="vending_displayer" id="vendingDisplayerBtn">Vending Displayer</button>
        <button class="mainbtn" data-i18n="vending_controller" id="vendingControllerBtn">Vending Controller</button>
        <button class="mainbtn" data-i18n="play_game" id="playGameBtn">玩遊戲</button>
        <button class="mainbtn" data-i18n="play" id="playBtn">播放</button>
    </nav>
    <script type="module" src="../../src/service/list.js"></script>
</body>
</html>