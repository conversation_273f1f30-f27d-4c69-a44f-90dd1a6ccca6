<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>Login</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/indexstyle.css">
    <script type='module' src="/src/translation.js"></script>
    <script type='module' src="/src/utils/errorMonitor.js"></script>
</head>

<body>
    <header>
        <div class="header-content">
            <label class="title">TIANYEN</label>
            <nav class="header-nav">
                <button id="themeToggle" class="theme-toggle" aria-label="Toggle theme">
                    <span class="theme-icon">🌙</span>
                </button>

                <div class="nav-item dropdown">
                    <a id="languageBtn" data-i18n="language" href="#" class="nav-link">Language</a>
                    <div class="dropdown-menu">
                        <a id="ENBtn" data-i18n="english" href="#" class="dropdown-item">English</a>
                        <a id="ZHTWBtn" data-i18n="zh-tw" href="#" class="dropdown-item">zh-TW</a>
                    </div>
                </div>

                <div class="nav-item dropdown">
                    <a id="openUserDialog" data-i18n="user" href="#" class="nav-link">User</a>
                    <div class="dropdown-menu">
                        <a id="informationBtn" data-i18n="information" href="/html/user/details.html"
                            class="dropdown-item">Information</a>
                        <a id="settingsBtn" data-i18n="settings" href="/html/user/settings.html"
                            class="dropdown-item">Settings</a>
                        <a id="signInAndOutBtn" data-i18n="sign_out" href="#" class="dropdown-item">Sign Out</a>
                    </div>
                </div>

                <div class="nav-item">
                    <a id="projectsBtn" data-i18n="projects" href="/html/project/list.html"
                        class="nav-link">Projects</a>
                </div>
            </nav>
        </div>
        <script type="module" src="/src/user/userDialog.js"></script>
        <script type="module" src="/src/utils/theme.js"></script>
    </header>
    <div class="loginBox">
        <img class="loginimg" src="html/assets/user_big_white.png">
        <h1 data-i18n="login" class="loginTitle">Login</h1>
        <form id="login_form">
            <div>
                <!--label data-i18n="account" for="account">Account</label-->
                <input type="text" name="account" id="account" placeholder="Account" required />
            </div>

            <div>
                <!--label data-i18n="password" for="password">Password</label-->
                <input type="password" name="password" id="password" placeholder="Password" required />
            </div>


            <label id="error_message" style="color:red"></label>
            <div class="button-container">
                <input data-i18n="submit" type="submit" value="submit" />
                <input data-i18n="register" type="button" value="register" id="registerBtn">
            </div>

        </form>
    </div>



    <script type="module">
        import { translationText, setLanguage } from "./src/translation.js";
        import { linkApiRoot } from './src/utils/env.js';

        const form = document.getElementById('login_form');

        document.getElementById('registerBtn').addEventListener('click', toRegisterPage);

        function toRegisterPage() {
            window.location.href = "./html/user/register.html";
        }

        form.addEventListener('submit', async function (event) {
            const formData = new FormData(form);
            event.preventDefault(); // 阻止表單的默認提交行為
            const errorMessage = document.getElementById('error_message');

            // 建立表單數據
            var object = {};
            formData.forEach(function (value, key) {
                object[key] = value;
            });
            var json = JSON.stringify(object);
            // 使用 Fetch 提交表單
            await fetch(linkApiRoot('user/login'), {
                method: 'POST',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                },
                body: json
            })
                .then(response => {
                    if (!response.ok) {
                        throw new Error("Invalid username or password.");
                    }
                    return response.json();
                })
                .then(data => {
                    // 顯示伺服器回應
                    localStorage.setItem('account', object['account']);
                    localStorage.setItem('token', data.token);
                    window.location.href = "./html/project/list.html";
                })
                .catch(error => {
                    errorMessage.innerText = error;
                });
        });
    </script>
</body>

</html>