import { linkApiRoot } from "../utils/env.js";

// const socket = io('https://localhost:3001');
var hashID = getQueryVariable("hashID");
var video_id = getQueryVariable("video_id");
var youtubeVideoPlayer = document.getElementById("online-player");
var videoPlayer = document.getElementById('video-player');
var error_message = document.getElementById('error_message');

var videos = [];
var connection = false;
var current = 0;
var display_id = -1;
var displayStatus = "PENDING";


 // 引入 YouTube API
 var tag = document.createElement('script');
tag.src = "https://www.youtube.com/iframe_api";
var firstScriptTag = document.getElementsByTagName('script')[0];
firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);

var youtubePlayer;
var vimeoPlayer;
var init = false;
let youtubeLastTime = 0;
let youtubeTimer;
var playedTime = 0;

const urlPath = window.location.pathname;
const userAgent = navigator.userAgent;

// socket.on('reply',function(event){
//     const uri = URL.createObjectURL(new Blob([event.data], { type: 'video/mp4' }));
//     videoPlayer.src =uri;
// });


videoPlayer.addEventListener('play', () => {
    localLastUpdateTime = Date.now();
    videoPlayer.timer = setInterval(() => {
        const now = Date.now();
        playedTime += (now - localLastUpdateTime) / 1000;
        localLastUpdateTime = now;
        console.log(`Local video played time: ${playedTime.toFixed(2)} seconds`);
    }, 1000);
  });

  videoPlayer.addEventListener('pause', () => {
    clearInterval(videoPlayer.timer);
  });

  videoPlayer.addEventListener('ended', () => {
    clearInterval(videoPlayer.timer);
  });

videoPlayer.addEventListener('ended', function(){
    videoPlayer.src = "";
    next();
    });

// 當影片狀態改變時觸發
function onYoutubePlayerStateChange(event) {
    if (event.data === YT.PlayerState.PLAYING) {
        youtubeLastTime = Date.now();
        youtubeTimer = setInterval(() => {
          const now = Date.now();
          playedTime += (now - youtubeLastTime) / 1000;
          youtubeLastTime = now;
        }, 1000);
    } else {
    clearInterval(youtubeTimer);
    }
    if (event.data == YT.PlayerState.ENDED) {
        next();
    }
}

function next(){
    createVideoPlayStat();
    current = current + 1 == videos.length? 0 : current + 1;
    while(videos[current].status != "ACTIVE"){
        current = current + 1 == videos.length? 0 : current + 1;
    }
    switchVideo(videos[current]);
}
setInterval(()=>{   
    if(vimeoPlayer){
        vimeoPlayer.getEnded().then(function(ended) {
            if(ended){
                next();
            }
        });
        vimeoPlayer.getPaused().then(function(state){
            if(!state){
                playedTime += 1;
            }
        })
    }
},1000)

async function createVideoPlayStat(){
    let json = JSON.stringify({
        videoId : videos[current].id,
        playTime: playedTime
    });
    console.log(json)
    logger.logEvent('info', "played video", {status:"ended",time: playedTime,video:videos[current]});
    fetch(linkApiRoot('videoPlayStat'), {
        method: 'POST',
        headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        },
        body: json
    })
    .then(response => response.json()) // 將回應轉換成 JSON 格式
    .then(data => {})
    .catch(error =>{
        console.error(error);
    });
}

function switchVideo(video){
    youtubePlayer = null;
    vimeoPlayer = null;
    youtubeVideoPlayer.style.display = 'none';
    videoPlayer.style.display = 'none';
    playedTime = 0;
    switch(video.sourceType){
        case "YOUTUBE":
            youtubeVideoPlayer.src = video.url + "&autoplay=1&mute=1&enablejsapi=1";
            youtubeVideoPlayer.style.display = 'block';
            youtubePlayer = new YT.Player('online-player', {
                    host: 'https://www.youtube.com',    
                    playerVars: { 
                        "autoplay":1,
                        'origin': 'https://127.0.0.1:5500' 
                    },
                    events: {
                        'onStateChange': onYoutubePlayerStateChange
                    }
                });
            break;
        case "VIMEO":
            youtubeVideoPlayer.remove();
            youtubeVideoPlayer = document.createElement('iframe');
            youtubeVideoPlayer.id = 'online-player';
            youtubeVideoPlayer.src = video.url + "&mute=1&autoplay=1";
            youtubeVideoPlayer.style.display = 'block';
            youtubeVideoPlayer.classList.add('player');
            youtubeVideoPlayer.allowfullscreen = true;
            document.body.appendChild(youtubeVideoPlayer);
            if(vimeoPlayer){
                vimeoPlayer.pause();
                vimeoPlayer.destroy();
            }
            vimeoPlayer = new Vimeo.Player('online-player');
            vimeoPlayer.setVolume(0); // 靜音
            vimeoPlayer.play(); // 自動播放
            break;
        case "S3":
            videoPlayer.src = `s3/video/${video.url}`;
            videoPlayer.style.display = 'block';
            break;
        case "LOCAL":
            videoPlayer.src = `local/video/${video.url}`;
            videoPlayer.style.display = 'block';
        default:
            break;
    }
    try{
        logger.logEvent('info', "playing video", {status:"playing",video:video});
    }catch(e){
        console.error("sent log error:" + e);
    }
    
}

async function connect() {
    let userInfo = null;
    try{
        var res = await fetch('https://ipapi.co/json/');
        userInfo = await res.json();
       
        userInfo = {
            ip: userInfo['ip'],
            city: userInfo['city'],
            country: userInfo['country']
        }
    }catch(e){
        userInfo = {
            ip: -1,
            city: "unknown",
            country: "unknown"
        }
    }
    if(!hashID){
        console.error("Missing hash id");
    }
    try{
        logger.initLogger("meta-tag-demosite","iamm","displayer",userInfo,userAgent,urlPath,display_id);
    }catch(e){
        console.error("init log error:" + e)
    }
    fetch(linkApiRoot(`connection`),
    {
        "method": "POST",
        headers:{
            "Accept": "application/json",
            "Content-Type": "application/json",
        },
        body: JSON.stringify({
            ip: userInfo.ip,
            hashID: hashID,
            status: displayStatus,
        })
    })
    .then(res => res.json())
    .then(data => {
        videos = data.data.videos;
        display_id = data.display_id;
        displayStatus = "CONNECTED";
        console.log("connected");
        connection = true;
        if(videos[current].status != "ACTIVE")
            current = current + 1 == videos.length? 0 : current + 1;
        switchVideo(videos[current]);
    })
    .catch(e => console.error(e));
}

function disconnect() {
    fetch(linkApiRoot(`connection`),
        {
         "method": "delete",
         headers:{
            "Accept": "application/json",
            "Content-Type": "application/json",
         },
         body: JSON.stringify({
            id: display_id,
            hashID: hashID
         })
        }
    )
        .then(res => res.json())
        .then(data => {
            videos = [];
            youtubeVideoPlayer.src = "";
            videoPlayer.src = "";
            youtubeVideoPlayer.style.display = 'none';
            videoPlayer.style.display = 'none';
            connection = false;
        })
        .catch(e => {
            displayStatus = "ERROR";    
            console.error(e);
        });
}

window.onload = function(){
    const loading = ()=>{
        setTimeout(function(){
        if(document.visibilityState === "visible"){
            connect();
            setInterval(function(){
                update();
            },1000);  
        }else{
            loading();
        }
    },100)
    }
    loading();
}

window.addEventListener('pagehide', (event) => {
    createVideoPlayStat();
    disconnect();
})

/**
 * Fetches project data from the server anad updates the display based on the project status.
 */
function update(){
    fetch(linkApiRoot(`connection`),{
        'method': 'PUT',
        headers:{ 
            "Accept": "application/json",
            "Content-Type": "application/json",
        },
        body:JSON.stringify({
            id: display_id,
            hashID: hashID,
            status: displayStatus   
        })
    })
    .then(res => res.json())
    .then(status=>{
        var status = status.status;
        if(status == "OPEN"){
            if(!connection)
                connect();
        }else if(status == "CLOSE"){
            error_message.value = "專案未啟動或資源加載錯誤";
            if(connection)
                disconnect();
        }
    })
    .catch(e=>{
        displayStatus = "ERROR";
        error_message.value = "連線錯誤";
        console.error(e);
    });
}

function getQueryVariable(variable)
{
    var query = window.location.search.substring(1);
    var vars = query.split("&");
    for (var i=0;i<vars.length;i++) {
        var pair = vars[i].split("=");
        if(pair[0] == variable){return pair[1];}
    }
    return(false);
}