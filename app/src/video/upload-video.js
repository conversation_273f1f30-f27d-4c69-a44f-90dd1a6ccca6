import * as tokenOps from '../user/tokenOps.js';
import {handleError} from '../handle_error.js';
import { linkApiRoot } from '../utils/env.js';
var token = tokenOps.getToken();
// const socket = new WebSocket('ws://localhost:8080');
const videoPlayer = document.getElementById('video-player');
const sourceSelect = document.getElementById('source-select');
const urlInput = document.getElementById('url-input');
const fileInput = document.getElementById('file-input');
const onlineVideoPlayer = document.getElementById("online-video-player")
const titleInput = document.getElementById('title');
const descriptionInput = document.getElementById('description');
const published_at = document.getElementById('published_at');
const duration = document.getElementById('duration');
const tagsInput = document.getElementById('tags');
var vimeoPlayer;
const elements = [urlInput,fileInput,videoPlayer,onlineVideoPlayer];
const selectToNone = ['none','none','none','none'];
const selectToOnline = ['block','none','none','block'];
const selectToOther = ['none','block','block','none'];

const apiYoutubeKey = "AIzaSyCu7UiISFIAevNrajsNYW3TlZFBbHBa9CE"; // 替換為你的 API 金鑰

// 監聽下拉選單變化
sourceSelect.addEventListener('change', function() {
    titleInput.value="";
    descriptionInput.value="";
    published_at.value="";
    tagsInput.value="";
    duration.value="";
    urlInput.value="";
    const selectedSource = this.value;
    var selectStyle = [];
    if (["YOUTUBE","VIMEO"].includes(selectedSource)) {
        selectStyle = selectToOnline;
    }else if(selectedSource == ''){
        selectStyle = selectToNone;
    }
    else{
    selectStyle = selectToOther;
    }
    console.log(selectStyle);
    elements.forEach((element, index)=>{
        element.style.display = selectStyle[index];
    });
    
});

// 監聽影片 URL 輸入
urlInput.addEventListener('change',async function() {
    url = this.value;
    if(sourceSelect.value == 'YOUTUBE'){
        const apiYoutubeUrl = `https://www.googleapis.com/youtube/v3/videos?id=${getYoutubeVideoId(url)}&key=${apiYoutubeKey}&part=snippet,contentDetails,statistics`;
        const metaData = await getVideoMetadata(apiYoutubeUrl, null, sourceSelect.value);
        titleInput.value = metaData.snippet.localized.title;
        descriptionInput.value = metaData.snippet.localized.description;
        published_at.value = metaData.snippet.publishedAt;
        duration.value = metaData.contentDetails.duration;
        tagsInput.value = metaData.snippet.tags;
    }
    loadStreamVideo(url);
});

// 監聽檔案上傳
fileInput.addEventListener('change', function() {
    const file = this.files[0];
    titleInput.value = fileInput.files[0].name;
    if (file) {
        const url = URL.createObjectURL(file);
        loadVideo(url);
    }
});

function loadStreamVideo(url){
    onlineVideoPlayer.src = url;
    vimeoPlayer = new Vimeo.Player('online-video-player');
    vimeoPlayer.getVideoTitle().then(title=>{
        titleInput.value = title;
    });
}
// 載入影片到播放器
function loadVideo(source) {
    videoPlayer.src = source;
}

var token = localStorage.getItem("token");
var exhibitionData =  JSON.parse(localStorage.getItem("exhibition_data").toString());
var form = document.getElementById('video_form');
var url = "";
const errorMessage = document.getElementById('error_message');
form.addEventListener('submit', async function(event) {
    event.preventDefault(); // 阻止表單的默認提交行為

    if(["S3","LOCAL"].includes(sourceSelect.value)){
        const file = fileInput.files[0];
        if (!file) {
            alert("Please select a video file.");
            return;
        }
        const formData = new FormData();
        formData.append('video', file);
        fetch(linkApiRoot(`${ sourceSelect.value.toLowerCase()}/video`), {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(async (data) => {
            console.group("file name");
            console.log(data.url);
            console.groupEnd();
            await send(data.url);
        })
        .catch((error) => {
            console.error('Error:', error);
        });
    }
    else{
        await send(url);
    }
});

// socket.onmessage = async function(e) {   
//     await send(e.data);
// };

async function send(url){
    if(["VIMEO","S3","LOCAL"].includes(sourceSelect.value)){
        const currentTime = new Date();
        const isoTime = currentTime.toISOString();
        published_at.value = isoTime;
    }
    var object = {
        name: titleInput.value,
        description: descriptionInput.value,
        sourceType: sourceSelect.value,
        eid: exhibitionData[0],
        url: url,
        published_at : published_at.value,
        duration     : duration.value,
        tags         : tagsInput.value
    }
    const json = JSON.stringify(object);
    //使用 Fetch 提交表單
    fetch(linkApiRoot('video'), {
        method: 'POST',
        headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            Authorization: 'Bearer '+ token 
        },
        body: json
    })
    .then(handleError)
    .then(response => response.json()) // 將回應轉換成 JSON 格式
    .then(data => {
        window.location.href = "../exhibition/view.html";
    })
    .catch(error =>{
        errorMessage.innerHTML = "Upload video failed.";
    });
}

function getYoutubeVideoId(embedUrl) {
    const regex = /embed\/([a-zA-Z0-9_-]+)/;
    const match = embedUrl.match(regex);
    return match ? match[1] : null;
}

function getVimeoVideoId(url){
    const regex = /\/video\/([^?]+)\?/;
    const match = url.match(regex);
    return match? match[1] : null;
}

async function getVideoMetadata(apiUrl, apiToken, type) {
    try {
        const header = {
            'Authorization': `Bearer ${apiToken}`,
            'Content-Type': 'application/json',
        }
        var response = null;
        switch(type){
            case 'YOUTUBE':
                response = await fetch(apiUrl);
                break;
            case 'VIMEO':
                response = await fetch(apiUrl, header);
                break;
            default:
                throw new Error('Not support');
        }
        const data = await response.json();
        if (data.items && data.items.length > 0) {
            const videoMetadata = data.items[0];
            return videoMetadata;
        } else {
            console.log('No video found.');
        }
    } catch (error) {
        console.error('Error fetching video metadata:', error);
    }
}