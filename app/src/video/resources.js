import * as tokenOps from '../user/tokenOps.js';
import {handleError} from '../handle_error.js';
import { translationText } from '../translation.js';
import { linkApiRoot } from '../utils/env.js';
var token = tokenOps.getToken();
// const socket = io('https://localhost:3001');
var videos_table = new DataTable("#videos-table");
var titles = document.getElementById("titles");
var content = document.getElementById("content");
var data = JSON.parse(localStorage.getItem("exhibition_data"));
var upload_btn = document.getElementById("upload_video_btn");
var delete_btn = document.getElementById("delete_video_btn");
var titleTranslationTexts;

document.onload = async()=>{
    titleTranslationTexts = [await translationText("id"), await translationText("name"), await translationText("url"), await translationText("status"), await translationText("uploader"), await translationText("duration")];

    for(let i=0;i < titles.children.length;i++){
        titles.children.item(i).textContent = titleTranslationTexts[i];
    }
}
// upload_btn.value = await translationText("upload");
// delete_btn.value = await translationText("delete");

upload_btn.addEventListener('click',function(){
    uploadPage();
});
delete_btn.addEventListener('click',function(){
    removeVideo();
});
document.getElementById('switch').addEventListener('click',function(){
    switchStatus();
});

document.getElementById('select_all_btn').addEventListener('click', function(){
    videos_table.rows({ page: 'current' }).nodes().each(function() {
        $(this).addClass('selected');
      });
});

document.getElementById('select_inverse_btn').addEventListener('click', function(){
    videos_table.rows({ page: 'current' }).nodes().each(function() {
        if ($(this).hasClass('selected')) {
            $(this).removeClass('selected');
        }
        else {
            $(this).addClass('selected');
        }
      });
});

$(document).ready( function ()  {
    $('#videos-table').DataTable(); 
    $('#videos-table tbody').on('click', 'tr', function () {
        // Check for 'selected' class and remove
        if ($(this).hasClass('selected')) {
            $(this).removeClass('selected');
        }
        else {
            $(this).addClass('selected');
        }
        });
} );

// socket.on('delete',(event)=>{
//     console.error(event.data);
// })

function uploadPage(){
    window.location.href = "../video/upload.html";
}

function removeVideo(){
    $('#videos-table tbody tr.selected').each(function () {
        var rowData = videos_table.row(this).data();  // 獲取該行的數據
        fetch(linkApiRoot('video/') + rowData[0],
        {
            method: 'DELETE',
            headers:{ Authorization: 'Bearer '+ token },
            body:{
                id: rowData.id
            }
        }
        )
        .then(handleError)
        .then(response => response.json()) 
        .then(videoData =>{
            if(["S3", "LOCAL"].includes(videoData.sourceType)){
                fetch(linkApiRoot(`${ videoData.sourceType.toLowerCase()}/video`), {
                    method: 'DELETE',
                    headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        filename: videoData.url
                    })
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(async (data) => {})
                .catch((error) => {
                    console.error('Error:', error);
                });
            }
            var row = videos_table.row($(this));
            row.remove().draw();  // 刪除該行並重新繪製表格
            console.log("deleted");
        })
        .catch(error => {
            console.log("remove video failed");
        });
    });
}
function switchStatus(){
    $('#videos-table tbody tr.selected').each(function () {
        var rowData = videos_table.row(this).data();  // 獲取該行的數據
        var status = rowData[3];
        if(status == "PENDING")
            status = "INACTIVE";
        else if(status == "INACTIVE")
            status = "ACTIVE";
        else
            status = "INACTIVE";
        fetch(linkApiRoot('video/') + rowData[0],
        {
            method: 'PUT',
            headers:{
            'Content-Type': 'application/json' ,
            Authorization: 'Bearer '+ token },
            body:JSON.stringify({
                status: status
            })
        }
        )
        .then(handleError)
        .then(response => response.json()) 
        .then(projectData =>{
            var row = videos_table.row($(this));
            row.remove().draw();
            videos_table.row.add([projectData.id, projectData.name, projectData.url, projectData.status, projectData.uploader, projectData.duration]).draw();
            console.log("status changed");
        })
        .catch(error => {
            console.log("remove video failed");
        });
    });
}

updateVideos();
function updateVideos(){
    fetch(linkApiRoot('exhibition/' + data[0]+'/videos'),
    {
        method: 'GET',
        headers:{ Authorization: 'Bearer '+ token }
    }
    )
    .then(handleError)
    .then(response => response.json()) 
    .then(videoData =>{
        videoData.forEach(element => {
            videos_table.row.add([element.id, element.name, element.url, element.status, element.uploader, element.duration]).draw();
        });
    })
    .catch(error =>{
        console.log("load error");
    })
}