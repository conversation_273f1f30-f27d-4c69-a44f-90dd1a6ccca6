import { linkApiRoot } from '../utils/env.js';
import { handleError } from '../handle_error.js';
import * as tokenOps from '../user/tokenOps.js';

// DOM 元素
const exhibitionInfo = document.getElementById('exhibition-info');

// 統計數據元素
const totalRegistrations = document.getElementById('total-registrations');
const todayRegistrations = document.getElementById('today-registrations');
const totalCheckins = document.getElementById('total-checkins');
const todayCheckins = document.getElementById('today-checkins');
const totalGames = document.getElementById('total-games');
const completionRate = document.getElementById('completion-rate');
const activeRooms = document.getElementById('active-rooms');
const totalRooms = document.getElementById('total-rooms');

// 房間和活動元素
const roomGrid = document.getElementById('room-grid');
const activityList = document.getElementById('activity-list');
const leaderboardBody = document.getElementById('leaderboard-body');

// 控制元素
const autoRefreshRooms = document.getElementById('auto-refresh-rooms');
const autoRefreshActivity = document.getElementById('auto-refresh-activity');
const refreshRoomsBtn = document.getElementById('refresh-rooms-btn');
const refreshLeaderboardBtn = document.getElementById('refresh-leaderboard-btn');

// 快速操作按鈕
const exportDataBtn = document.getElementById('export-data-btn');
const viewRegistrationsBtn = document.getElementById('view-registrations-btn');
const manageRoomsBtn = document.getElementById('manage-rooms-btn');
const systemSettingsBtn = document.getElementById('system-settings-btn');

// 全局變數
let currentExhibitionId = null;
let refreshIntervals = {};
let token = null;

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    token = tokenOps.getToken();
    
    if (!token) {
        window.location.href = '/html/user/login.html';
        return;
    }
    
    initializeDashboard();
    setupEventListeners();
    startAutoRefresh();
});

// 初始化儀表板
async function initializeDashboard() {
    // 從 localStorage 獲取展覽資訊
    const exhibitionData = JSON.parse(localStorage.getItem("exhibition_data"));
    
    if (!exhibitionData || !exhibitionData[0]) {
        exhibitionInfo.textContent = '請先選擇一個展覽';
        return;
    }
    
    currentExhibitionId = exhibitionData[0];
    
    try {
        // 載入展覽基本資訊
        await loadExhibitionInfo();
        
        // 載入所有數據
        await Promise.all([
            loadStatistics(),
            loadRoomStatus(),
            loadRecentActivity(),
            loadLeaderboard()
        ]);
        
    } catch (error) {
        console.error('初始化儀表板失敗:', error);
        exhibitionInfo.textContent = '載入失敗，請重新整理頁面';
    }
}

// 載入展覽資訊
async function loadExhibitionInfo() {
    try {
        const response = await fetch(linkApiRoot(`exhibition/${currentExhibitionId}`), {
            headers: { Authorization: 'Bearer ' + token }
        });
        
        if (!response.ok) throw new Error('載入展覽資訊失敗');
        
        const exhibition = await response.json();
        exhibitionInfo.textContent = `${exhibition.name} - ${exhibition.status === 'OPEN' ? '進行中' : '未開始'}`;
        
    } catch (error) {
        console.error('載入展覽資訊失敗:', error);
        exhibitionInfo.textContent = '載入展覽資訊失敗';
    }
}

// 載入統計數據
async function loadStatistics() {
    try {
        const response = await fetch(linkApiRoot(`exhibition/${currentExhibitionId}/complete-stats`), {
            headers: { Authorization: 'Bearer ' + token }
        });
        
        if (!response.ok) throw new Error('載入統計數據失敗');
        
        const result = await response.json();
        
        if (result.success) {
            updateStatistics(result.data);
        }
        
    } catch (error) {
        console.error('載入統計數據失敗:', error);
    }
}

// 更新統計數據
function updateStatistics(data) {
    // 報名統計
    if (data.registration && data.registration.data) {
        const regStats = data.registration.data;
        totalRegistrations.textContent = regStats.totalRegistrations || 0;
        // 今日報名數需要額外計算，這裡先設為 0
        todayRegistrations.textContent = '0';
    }
    
    // 簽到統計
    if (data.checkIn && data.checkIn.data) {
        const checkStats = data.checkIn.data;
        totalCheckins.textContent = checkStats.totalCheckIns || 0;
        todayCheckins.textContent = checkStats.todayCheckIns || 0;
    }
    
    // 遊戲統計
    if (data.game && data.game.data) {
        const gameStats = data.game.data;
        totalGames.textContent = gameStats.totalGames || 0;
        completionRate.textContent = gameStats.completionRate + '%' || '0%';
    }
}

// 載入房間狀態
async function loadRoomStatus() {
    try {
        const response = await fetch(linkApiRoot(`exhibition/${currentExhibitionId}/game-rooms`), {
            headers: { Authorization: 'Bearer ' + token }
        });
        
        if (!response.ok) throw new Error('載入房間狀態失敗');
        
        const result = await response.json();
        
        if (result.success) {
            updateRoomGrid(result.data);
            
            // 更新房間統計
            const activeRoomCount = result.data.filter(room => room.status === 'ACTIVE').length;
            activeRooms.textContent = activeRoomCount;
            totalRooms.textContent = result.data.length;
        }
        
    } catch (error) {
        console.error('載入房間狀態失敗:', error);
        roomGrid.innerHTML = '<div class="loading">載入房間狀態失敗</div>';
    }
}

// 更新房間網格
function updateRoomGrid(rooms) {
    if (!rooms || rooms.length === 0) {
        roomGrid.innerHTML = '<div class="loading">暫無房間資料</div>';
        return;
    }
    
    roomGrid.innerHTML = '';
    
    rooms.forEach(room => {
        const roomCard = createRoomCard(room);
        roomGrid.appendChild(roomCard);
    });
}

// 創建房間卡片
function createRoomCard(room) {
    const card = document.createElement('div');
    card.className = `room-card ${room.status.toLowerCase()}`;
    
    // 獲取房間統計（如果有的話）
    const stats = room.stats || {};
    
    card.innerHTML = `
        <div class="room-header">
            <div class="room-name">${room.name}</div>
            <div class="room-status ${room.status.toLowerCase()}">${room.status === 'ACTIVE' ? '活躍' : '非活躍'}</div>
        </div>
        <div class="room-stats">
            <div class="room-stat">
                <div class="room-stat-number">${room.currentPlayers || 0}</div>
                <div class="room-stat-label">目前玩家</div>
            </div>
            <div class="room-stat">
                <div class="room-stat-number">${stats.checkInCount || 0}</div>
                <div class="room-stat-label">簽到次數</div>
            </div>
            <div class="room-stat">
                <div class="room-stat-number">${stats.gameCount || 0}</div>
                <div class="room-stat-label">遊戲次數</div>
            </div>
            <div class="room-stat">
                <div class="room-stat-number">${room.maxPlayers || 4}</div>
                <div class="room-stat-label">最大容量</div>
            </div>
        </div>
    `;
    
    // 添加點擊事件
    card.addEventListener('click', () => {
        showRoomDetails(room);
    });
    
    return card;
}

// 載入最近活動
async function loadRecentActivity() {
    try {
        // 這裡可以載入最近的簽到、遊戲等活動
        // 暫時顯示模擬數據
        const activities = [
            {
                type: 'checkin',
                icon: '✅',
                title: '用戶簽到',
                details: '張三 在 飛鏢房間A 簽到',
                time: '2分鐘前'
            },
            {
                type: 'game',
                icon: '🎯',
                title: '遊戲完成',
                details: '李四 完成飛鏢遊戲，得分 85',
                time: '5分鐘前'
            },
            {
                type: 'registration',
                icon: '📝',
                title: '新用戶報名',
                details: '王五 完成活動報名',
                time: '10分鐘前'
            }
        ];
        
        updateActivityList(activities);
        
    } catch (error) {
        console.error('載入最近活動失敗:', error);
        activityList.innerHTML = '<div class="loading">載入活動記錄失敗</div>';
    }
}

// 更新活動列表
function updateActivityList(activities) {
    if (!activities || activities.length === 0) {
        activityList.innerHTML = '<div class="loading">暫無活動記錄</div>';
        return;
    }
    
    activityList.innerHTML = '';
    
    activities.forEach(activity => {
        const item = document.createElement('div');
        item.className = `activity-item ${activity.type}`;
        
        item.innerHTML = `
            <div class="activity-icon">${activity.icon}</div>
            <div class="activity-content">
                <div class="activity-title">${activity.title}</div>
                <div class="activity-details">${activity.details}</div>
            </div>
            <div class="activity-time">${activity.time}</div>
        `;
        
        activityList.appendChild(item);
    });
}

// 載入排行榜
async function loadLeaderboard() {
    try {
        // 這裡應該載入所有房間的排行榜數據
        // 暫時顯示模擬數據
        const leaderboard = [
            { rank: 1, userName: '張三', score: 95, room: '飛鏢房間A', playTime: 180, completedAt: '2024-01-15 14:30' },
            { rank: 2, userName: '李四', score: 85, room: '飛鏢房間B', playTime: 165, completedAt: '2024-01-15 14:25' },
            { rank: 3, userName: '王五', score: 78, room: '飛鏢房間A', playTime: 200, completedAt: '2024-01-15 14:20' }
        ];
        
        updateLeaderboard(leaderboard);
        
    } catch (error) {
        console.error('載入排行榜失敗:', error);
        leaderboardBody.innerHTML = '<tr><td colspan="6" class="loading">載入排行榜失敗</td></tr>';
    }
}

// 更新排行榜
function updateLeaderboard(leaderboard) {
    if (!leaderboard || leaderboard.length === 0) {
        leaderboardBody.innerHTML = '<tr><td colspan="6" class="loading">暫無排行榜數據</td></tr>';
        return;
    }
    
    leaderboardBody.innerHTML = '';
    
    leaderboard.forEach(item => {
        const row = document.createElement('tr');
        
        let rankBadgeClass = '';
        if (item.rank === 1) rankBadgeClass = 'gold';
        else if (item.rank === 2) rankBadgeClass = 'silver';
        else if (item.rank === 3) rankBadgeClass = 'bronze';
        
        const playTimeMinutes = Math.floor(item.playTime / 60);
        const playTimeSeconds = item.playTime % 60;
        const playTimeText = `${playTimeMinutes}:${playTimeSeconds.toString().padStart(2, '0')}`;
        
        row.innerHTML = `
            <td><span class="rank-badge ${rankBadgeClass}">${item.rank}</span></td>
            <td>${item.userName}</td>
            <td><strong>${item.score}</strong></td>
            <td>${item.room}</td>
            <td>${playTimeText}</td>
            <td>${item.completedAt}</td>
        `;
        
        leaderboardBody.appendChild(row);
    });
}

// 設置事件監聽器
function setupEventListeners() {
    refreshRoomsBtn.addEventListener('click', loadRoomStatus);
    refreshLeaderboardBtn.addEventListener('click', loadLeaderboard);
    
    // 快速操作按鈕
    exportDataBtn.addEventListener('click', handleExportData);
    viewRegistrationsBtn.addEventListener('click', () => {
        window.open(`/html/exhibition/monitoring.html`, '_blank');
    });
    manageRoomsBtn.addEventListener('click', handleManageRooms);
    systemSettingsBtn.addEventListener('click', () => {
        window.open(`/html/exhibition/settings.html`, '_blank');
    });
}

// 開始自動刷新
function startAutoRefresh() {
    // 房間狀態自動刷新 (30秒)
    if (autoRefreshRooms.checked) {
        refreshIntervals.rooms = setInterval(() => {
            if (autoRefreshRooms.checked) {
                loadRoomStatus();
                loadStatistics();
            }
        }, 30000);
    }
    
    // 活動記錄自動刷新 (10秒)
    if (autoRefreshActivity.checked) {
        refreshIntervals.activity = setInterval(() => {
            if (autoRefreshActivity.checked) {
                loadRecentActivity();
            }
        }, 10000);
    }
    
    // 監聽自動刷新設置變化
    autoRefreshRooms.addEventListener('change', () => {
        if (autoRefreshRooms.checked) {
            refreshIntervals.rooms = setInterval(() => {
                loadRoomStatus();
                loadStatistics();
            }, 30000);
        } else {
            clearInterval(refreshIntervals.rooms);
        }
    });
    
    autoRefreshActivity.addEventListener('change', () => {
        if (autoRefreshActivity.checked) {
            refreshIntervals.activity = setInterval(() => {
                loadRecentActivity();
            }, 10000);
        } else {
            clearInterval(refreshIntervals.activity);
        }
    });
}

// 顯示房間詳情
function showRoomDetails(room) {
    // 這裡可以打開房間詳情頁面或模態框
    const url = `/html/game/dart-game.html?roomId=${room.id}&roomName=${encodeURIComponent(room.name)}`;
    window.open(url, '_blank');
}

// 處理匯出數據
function handleExportData() {
    // 這裡可以實現數據匯出功能
    alert('數據匯出功能開發中...');
}

// 處理管理房間
function handleManageRooms() {
    // 這裡可以打開房間管理頁面
    alert('房間管理功能開發中...');
}

// 頁面卸載時清理
window.addEventListener('beforeunload', () => {
    Object.values(refreshIntervals).forEach(interval => {
        clearInterval(interval);
    });
});

// 導出函數供其他模組使用
export {
    loadStatistics,
    loadRoomStatus,
    loadRecentActivity,
    loadLeaderboard
};
