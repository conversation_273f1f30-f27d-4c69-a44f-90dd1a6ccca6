export function saveToken(token) {
  localStorage.setItem('token', token);
}

export function getToken() {
  return localStorage.getItem('token');
}

export function validateToken() {
  const token = localStorage.getItem('token');

  // 檢查 token 是否存在
  if (!token) {
    return false;
  }

  try {
    const decodedToken = JSON.parse(atob(token.split('.')[1]));
    const now = Math.floor(Date.now() / 1000);
    // 修復邏輯：token 未過期才是有效的
    return decodedToken.exp > now;
  } catch (error) {
    // 如果解析失敗，視為無效 token
    console.error('Token parsing error:', error);
    return false;
  }
}