import * as tokenOps from './tokenOps.js';
import {translationText, setLanguage} from "../translation.js";

// Get elements - some may not exist in new dropdown structure
var openDialogButton = document.getElementById('openUserDialog');
var userDialog = document.getElementById('userDialog'); // May be null in new structure
var langDialog = document.getElementById('langDialog'); // May be null in new structure
var signBtn = document.getElementById('signInAndOutBtn');
var informationBtn = document.getElementById('informationBtn');
var projectsBtn = document.getElementById('projectsBtn');
var settingsBtn = document.getElementById('settingsBtn');
var langBtn = document.getElementById("languageBtn");
var enBtn = document.getElementById('ENBtn');
var zhtwBtn = document.getElementById("ZHTWBtn");
var token = tokenOps.getToken();
window.onload = async ()=>{
    if(signBtn) {
        if(token){
            signBtn.textContent = await translationText("sign_out");
            if(informationBtn) informationBtn.style.display = 'block';
            if(projectsBtn) projectsBtn.style.display = 'block';
            if(settingsBtn) settingsBtn.style.display = 'block';
        }
        else{
            signBtn.textContent = await translationText("sign_in");
            if(informationBtn) informationBtn.style.display = 'none';
            if(projectsBtn) projectsBtn.style.display = 'none';
            if(settingsBtn) settingsBtn.style.display = 'none';
        }
    }
}


// Handle dropdown interactions - only if elements exist (for backward compatibility)
if(openDialogButton && userDialog) {
    openDialogButton.addEventListener('click',(event)=>{
        event.preventDefault();
        if(userDialog.hasAttribute('open')){
            userDialog.close();
            return;
        }
        const rect = openDialogButton.getBoundingClientRect();
        if(token){
            userDialog.style.left = `${rect.left - 237}px`;
        }else{
            userDialog.style.left = `${rect.left - 85}px`;
        }
        userDialog.show();
    });

    userDialog.addEventListener('focusout',(event)=>{
        if(!userDialog.contains(event.relatedTarget)){
            userDialog.close();
        }
    });
}

if(langDialog) {
    langDialog.addEventListener('focusout', (event)=>{
        if(!langDialog.contains(event.relatedTarget)){
            langDialog.close();
        }
    });
}

// Sign in/out button
if(signBtn) {
    signBtn.addEventListener('click', (event)=>{
        event.preventDefault();
        let backToLogin = true;
        if(signBtn.textContent == "Sign Out"){
            backToLogin = confirm('Are you sure you want to log out?');
        }
        if(backToLogin){
            localStorage.clear();
            window.location.href = '../../index.html';
        }
    });
}

// Language button and dialog
if(langBtn) {
    langBtn.addEventListener('click',(event)=>{
        event.preventDefault();
        if(langDialog && langDialog.hasAttribute('open')){
            langDialog.close();
            return;
        }
        if(langDialog) {
            const rect = langBtn.getBoundingClientRect();
            langDialog.style.left = `${rect.left - 50}px`;
            langDialog.show();
        }
    });
}

// Language selection buttons
if(enBtn) {
    enBtn.addEventListener('click', (event)=>{
        event.preventDefault();
        setLanguage('en');
    });
}

if(zhtwBtn) {
    zhtwBtn.addEventListener('click', (event)=>{
        event.preventDefault();
        setLanguage('tw');
    });
}

// Dialog position update (only if dialogs exist)
function updateDialogPosition(){
    if(userDialog && langDialog) {
        const rect = userDialog.getBoundingClientRect();
        const rect2 = langDialog.getBoundingClientRect();

        const offsetRight = 16;
        const offsetRight2 = 155;

        const leftPosition = window.innerWidth - rect.width - offsetRight;
        const leftPosition2 = window.innerWidth - rect2.width - offsetRight2;

        userDialog.style.left = `${leftPosition}px`;
        langDialog.style.left = `${leftPosition2}px`;
    }
}

// Only add resize listener if dialogs exist
if(userDialog && langDialog) {
    window.addEventListener('resize', updateDialogPosition);
}