import { gameStatus } from '../health.js';
import * as Table from '../table.js';
import {translationText} from '../translation.js';

var account = localStorage.getItem("account");
var table;
const title = document.getElementById("titleColumns");
const content = document.getElementById("content");
var projectData = JSON.parse(localStorage.getItem('project_data'));
var create_btn = document.getElementById("create_btn");
var delete_btn = document.getElementById("delete_btn");
var titleTranslationTexts;

// create_btn.value = await translationText("create");
// delete_btn.value = await translationText("delete");

$(document).ready(async function ()  {
    titleTranslationTexts= [await translationText("id"),await translationText("name"), await translationText("status"), await translationText("game")];
    for(let i=0;i < title.children.length;i++){
        title.children.item(i).textContent = titleTranslationTexts[i];
    }
    table = Table.initTable("#table",[]);
    Table.updateTable(table, ["project",projectData[0],"exhibitions"],['id','name','status','game']);
    Table.doubleClickTable(table, "#table",(data)=>{
        localStorage.setItem('exhibition_data', JSON.stringify(data));
        window.location.href = "./view.html";
    })
} );

create_btn.addEventListener('click',async function(event){
    window.location.href = "./create.html";
});

delete_btn.addEventListener('click', async function(event){ 
    let data = Table.remove(table, '#table', 'exhibition');
    const {status} = await gameStatus();
    if(status == "ok"){
        data.forEach(async element => {
            console.log(element)
            await fetch('http://localhost:4000/api/activity/delete',
                {
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                        },
                    body: JSON.stringify({
                        id: element[0]
                    })
                })    
        });
        
    }
});
