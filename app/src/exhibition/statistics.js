import * as tableOps from '../table.js';
import { exportData } from '../export.js';
import { translationText } from '../translation.js';
var exportDataBtn = document.getElementById('export_data_btn');
var exhibitionData = JSON.parse(localStorage.getItem('exhibition_data'));
var titles = document.getElementById("titles");
var table = tableOps.initTable('#statistics-table');
var where = encodeURIComponent(JSON.stringify({ eid:exhibitionData[0] }));
var titleTranslationTexts;

document.onload = async () =>{
    var titleTranslationTexts = [await translationText("id"), await translationText("name"), await translationText("impressions"), await translationText("time_spent"), await translationText("duration")];

    for(let i=0;i < titles.children.length;i++){
        titles.children.item(i).textContent = titleTranslationTexts[i];
    }
}

tableOps.updateTable(table,['videos','videoPlayStats'],['id','name','impressions', "TimeSpent","duration"], `eid=${exhibitionData[0]}`);
setInterval(()=>{
    tableOps.updateTable(table,['videos','videoPlayStats'],['id','name','impressions', "TimeSpent","duration"], `eid=${exhibitionData[0]}`);
},3000);

exportDataBtn.addEventListener('click',function(){
    exportData('videos/videoPlayStats',`eid=${exhibitionData[0]}`);
});

