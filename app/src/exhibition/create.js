import {handleError} from '../handle_error.js';
import { gameStatus } from '../health.js';
import { linkApiRoot } from '../utils/env.js';
var token = localStorage.getItem("token");
var account = localStorage.getItem('account');
var form = document.getElementById('exhibition_form');
var projectData = JSON.parse(localStorage.getItem('project_data'))
form.addEventListener('submit', async function(event) {
    const formData = new FormData(form);
    event.preventDefault(); // 阻止表單的默認提交行為
    const errorMessage = document.getElementById('error_message');
    // 建立表單數據
    var object = {};
    formData.append("pid", parseInt(projectData[0]));
    formData.forEach(function(value, key){
        object[key] = value;
    });
    var json = JSON.stringify(object);
    // 使用 Fetch 提交表單
    await fetch(linkApiRoot('exhibition'), {
        method: 'POST',
        headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        Authorization: 'Bearer '+ token 
        },
        body: json
    })
    .then(handleError)
    .then(response => response.json()) // 將回應轉換成 JSON 格式
    .then(async data => {
        let {status} = await gameStatus();
        if(status == "ok"){
            await fetch('http://127.0.0.1:4000/api/activity/create', {
                method: 'POST',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    id: data.id,
                    name:data.name,
                    project_id:projectData[0]
                })
            });
        }
        window.location.href = "../dashboard.html";
    })
    .catch(error =>{
        errorMessage.innerHTML = error;
    })
});