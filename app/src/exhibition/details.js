import * as tokenOps from '../user/tokenOps.js';
import {handleError} from '../handle_error.js';
import { linkApiRoot } from '../utils/env.js';
var token = tokenOps.getToken();
//var title = document.getElementById("titleText");
var description = document.getElementById("descriptionText");
var expiration = document.getElementById("expirationTimeText");
var startTime = document.getElementById("startTimeText");
var endTime =document.getElementById("endTimeText");
var data = JSON.parse(localStorage.getItem("exhibition_data"));
console.log(data)
load();
function load(){
    fetch(linkApiRoot('exhibition/') + data[0],
    {
        headers:{ Authorization: 'Bearer '+ token }
    }
    )
    .then(handleError)
    .then(response => response.json()) 
    .then(exhibitionData =>{
        var expirationDataTime = exhibitionData.expiration? new Date(exhibitionData.expiration).toLocaleString(): null;
        //title.innerText = exhibitionData.name;
        description.innerText = exhibitionData.description;
        expiration.textContent = expirationDataTime;
        startTime.innerText = exhibitionData.start_time;
        endTime.innerText = exhibitionData.end_time;
    })
    .catch(error => {
        console.error(error);
    });
}