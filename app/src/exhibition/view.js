import {back, displayerPage, vendingControllerPage, vendingDisplayerPage, executeExhibition, getExhibition, stopExhibition, clawControllerPage} from './operations.js';
import {loadContent} from '../utils/dynamicLoading.js';
import { gameStatus } from '../health.js';
import { translationText } from '../translation.js';
var data = JSON.parse(localStorage.getItem("exhibition_data"));
var popup = document.getElementById("executePopup");
var expirationTime = document.getElementById('expirationTimeInput');
var wantToExecuteBtn = document.getElementById('wantToExecuteBtn');

getExhibition().then(async (projectData)=>{
    if(projectData.status == 'OPEN')
        wantToExecuteBtn.textContent = await translationText("stop");
    else
        wantToExecuteBtn.textContent = await translationText("execute");
});

document.getElementById('backBtn').addEventListener('click', function() {
    back();
});
document.getElementById('detailsBtn').addEventListener('click', function() {
  //loadContent('../exhibition/details.html');
  window.location.href='../exhibition/details.html';
});
document.getElementById('resourcesBtn').addEventListener('click', function() {
  //loadContent('../video/list.html');
  window.location.href='../video/list.html';
});
document.getElementById('monitoringBtn').addEventListener('click', function() {
  //loadContent('../exhibition/monitoring.html');
  window.location.href='../exhibition/monitoring.html';
});
document.getElementById('statisticsBtn').addEventListener('click', function(){
  //loadContent('../exhibition/statistics.html')
  window.location.href='../exhibition/statistics.html';
});
document.getElementById('settingBtn').addEventListener('click', function() {
  //loadContent('../exhibition/settings.html');
  window.location.href='../exhibition/settings.html';
});
document.getElementById('userListBtn').addEventListener('click', function(){
  //loadContent('../game/user-list.html');
  window.location.href='../game/user-list.html';
});
document.getElementById('voucherListBtn').addEventListener('click', function(){
  //loadContent('../game/voucher-list.html');
  window.location.href='../game/voucher-list.html';
});
document.getElementById('productListBtn').addEventListener('click', function(){
  //loadContent('../game/product-list.html');
  window.location.href='../game/product-list.html';
});
document.getElementById('serviceBtn').addEventListener('click', function(){
  window.location.href='../service/list.html';
});

wantToExecuteBtn.addEventListener('click', function() {
  if( wantToExecuteBtn.textContent == "執行"){
    showExecuteWindow();
  }else{
    if(showStopWindow()){
      stopExhibition().then((data)=>{
        window.location.reload();
      });
      wantToExecuteBtn.textContent = "執行";
    }
  }
});
document.getElementById('closeBtn').addEventListener('click', function() {
  closeExecuteWindow();
});
document.getElementById('executeBtn').addEventListener('click',async function() {
  const expiration = expirationTime.value? new Date(expirationTime.value).toISOString() : null;
  executeExhibition(expiration).then((data)=>{
    wantToExecuteBtn.textContent = "中止";
    closeExecuteWindow();
    window.location.reload();
  });
});

function showExecuteWindow() {
  popup.classList.add("show");
}

function closeExecuteWindow(){
  popup.classList.remove("show");
}

function showStopWindow(){
  return confirm("Are you sure you want to stop the exhibition?");
}
