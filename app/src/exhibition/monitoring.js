import * as tokenOps from '../user/tokenOps.js';
import {handleError} from '../handle_error.js';
import * as tableOps from '../table.js';
import { translationText } from '../translation.js';
import { linkApiRoot } from '../utils/env.js';
import { exportData } from '../export.js';

var token = tokenOps.getToken();
var exhibition_status = document.getElementById("exhibition-status");
var titles = document.getElementById("titles");
var connects = document.getElementById("connect-amount");
var data = JSON.parse(localStorage.getItem("exhibition_data"));
var hashID;
var columns = {
    "#displayers-table": ["id","ip","status"],
};
var table = tableOps.initTable("#displayers-table");
var titleTranslationTexts;

document.onload = async () =>{
    titleTranslationTexts = [await translationText("id"), await translationText("ip"), await translationText("status")];

    for(let i=0;i < titles.children.length;i++){
        titles.children.item(i).textContent = titleTranslationTexts[i];
    }   
}

setInterval(loadData, 3000);
loadData();
function loadData() {
    fetch(linkApiRoot(`exhibition/${data[0]}`),
    {
        headers:{ Authorization: 'Bearer '+ token },
    })
    .then(handleError)
    .then(res => res.json())
    .then(exhibitionData =>{
        var status = exhibitionData.status;
        exhibition_status.textContent = status;
        hashID = exhibitionData.hashID;
        connects.textContent = tableOps.updateTable(table, ["connections?hashID="+ hashID], ["id","ip","status"]);
    })
    .catch(e=>{
        console.error(e);
    });
}

document.getElementById('export_data_btn').addEventListener('click',function(){
    if(hashID)
        exportData('connections',`hashID=${hashID}`);
});