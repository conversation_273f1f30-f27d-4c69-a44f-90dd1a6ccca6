import * as tokenOps from './user/tokenOps.js';
import {handleError} from './handle_error.js';
import { linkApiRoot } from './utils/env.js';

const token = tokenOps.getToken();

export var exportData = function(objects, query){
    fetch(linkApiRoot(`${objects}${query?`?${query}`:""}`),
        {
            method: 'GET',
            headers:{ Authorization: 'Bearer '+ token }
        }
        )
        .then(handleError)
        .then(response => response.json()) 
        .then(data =>{
            if(typeof(data.items) == 'string')
                data.items = JSON.parse(data.items);
            if(isDictionary(data.items))
              data.items = Object.values(data.items);
            downloadCSV(arrayToCSV(data.items));
        })
        .catch(error =>{
            console.error(error);
        })
}

function arrayToCSV(arr) {
    if(arr.length === 0) return '';
    console.log(arr)
    let headers = Object.keys(arr[0]);
    let csv = headers.join(',') + '\n';
    csv += arr.map(item => Object.values(item).join(',')).join('\n');
    return csv;
  }

  function convertToCSV(arr) {
    // 檢查資料是否為空
    if (arr.length === 0) return '';
  
    // 將每個物件扁平化
    const flattenedData = arr.map(flattenObject);
  
    // 提取所有的鍵，這將是 CSV 的欄位
    const headers = Array.from(new Set(flattenedData.flatMap(Object.keys)));
    
    // 扁平化資料，將陣列展開為多行
    let rows = [];
    for (const obj of flattenedData) {
      // 如果有陣列，我們將陣列中的每個元素展開為一行
      const maxArrayLength = Math.max(...Object.values(obj).filter(val => Array.isArray(val)).map(arr => arr.length), 1);
      
      for (let i = 0; i < maxArrayLength; i++) {
        const row = headers.map(header => {
          const value = obj[header];
          if (Array.isArray(value)) {
            return value[i] || '';  // 當陣列長度不足時填充空白
          } else {
            return value || '';  // 普通值直接返回
          }
        });
        rows.push(row);
      }
    }
  
    // 生成 CSV 內容
    const csvContent = [headers.join(','), ...rows.map(row => row.join(','))].join('\n');
    return csvContent;
  }
function downloadCSV(csvContent, filename = 'data.csv') {
    // 創建一個隱藏的 <a> 標籤來觸發下載
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = filename;
    link.click();
  }

function isArray(obj) {
    return obj && typeof obj === 'object' && 'length' in obj;
  }
  
function isDictionary(obj) {
return obj && typeof obj === 'object' && !('length' in obj);
}