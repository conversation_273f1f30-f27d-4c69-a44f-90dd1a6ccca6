import * as Table from '../table.js';
import { translationText } from '../translation.js';

var account = localStorage.getItem("account");
var table;
const titles = document.getElementById("titles");
const content = document.getElementById("content");
var exhibitionData = JSON.parse(localStorage.getItem('exhibition_data'));



$(document).ready(async function ()  {
    var titleTranslationTexts = [await translationText("id"), await translationText("activity_id"), await translationText("start_time"), await translationText("end_time"), await translationText("score")];

    table = Table.initTable("#table",[]);

    for(let i=0;i < titles.children.length;i++){
        titles.children.item(i).textContent = titleTranslationTexts[i];
    }
    
    var where = encodeURIComponent(JSON.stringify({ activity_id:exhibitionData[0] }));
    Table.updateTable(table, ["playlist/list"],['id','activity_id','time_start','time_end','score'], `where=${where}`, "pd0001");
} );