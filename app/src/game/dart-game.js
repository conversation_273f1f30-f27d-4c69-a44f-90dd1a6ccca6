import { linkApiRoot } from '../utils/env.js';

// DOM 元素
const roomName = document.getElementById('room-name');
const roomId = document.getElementById('room-id');
const connectionStatus = document.getElementById('connection-status');
const errorMessage = document.getElementById('error-message');
const successMessage = document.getElementById('success-message');

const qrInputSection = document.getElementById('qr-input-section');
const qrCodeInput = document.getElementById('qr-code-input');
const joinGameBtn = document.getElementById('join-game-btn');

const gameControls = document.getElementById('game-controls');
const scoreBtns = document.querySelectorAll('.score-btn');
const throwDartBtn = document.getElementById('throw-dart-btn');
const startGameBtn = document.getElementById('start-game-btn');
const endGameBtn = document.getElementById('end-game-btn');

const playerName = document.getElementById('player-name');
const currentScore = document.getElementById('current-score');
const throwCount = document.getElementById('throw-count');
const gameTime = document.getElementById('game-time');

const playerList = document.getElementById('player-list');
const leaderboardList = document.getElementById('leaderboard-list');

// 遊戲狀態
let socket = null;
let isConnected = false;
let currentRoomId = null;
let currentQrCode = null;
let selectedScore = 0;
let myScore = 0;
let myThrowCount = 0;
let gameStartTime = null;
let gameTimer = null;

// 從 URL 參數獲取房間資訊
const urlParams = new URLSearchParams(window.location.search);
currentRoomId = urlParams.get('roomId') || 'dart-room-001';

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeGame();
    setupEventListeners();
});

// 初始化遊戲
function initializeGame() {
    roomId.textContent = currentRoomId;
    roomName.textContent = `飛鏢遊戲房間 ${currentRoomId}`;
    
    // 初始化 Socket.IO 連接
    initializeSocket();
    
    // 載入排行榜
    loadLeaderboard();
}

// 初始化 Socket 連接
function initializeSocket() {
    try {
        socket = io({
            path: '/socket.io',
            transports: ['websocket', 'polling']
        });
        
        setupSocketListeners();
        updateConnectionStatus('connecting', '🟡 連接中...');
        
    } catch (error) {
        console.error('Socket 初始化失敗:', error);
        updateConnectionStatus('disconnected', '🔴 連接失敗');
    }
}

// 設置 Socket 事件監聽器
function setupSocketListeners() {
    socket.on('connect', () => {
        console.log('Socket 連接成功');
        updateConnectionStatus('connected', '🟢 已連接');
        isConnected = true;
    });
    
    socket.on('disconnect', () => {
        console.log('Socket 連接斷開');
        updateConnectionStatus('disconnected', '🔴 連接斷開');
        isConnected = false;
    });
    
    socket.on('error', (data) => {
        console.error('Socket 錯誤:', data);
        showError(data.message || '發生錯誤');
    });
    
    socket.on('join-success', (data) => {
        console.log('成功加入房間:', data);
        handleJoinSuccess(data);
    });
    
    socket.on('player-joined', (data) => {
        console.log('玩家加入:', data);
        updateRoomState(data.roomState);
        showSuccess(`${data.player.userName} 加入了遊戲`);
    });
    
    socket.on('player-left', (data) => {
        console.log('玩家離開:', data);
        updateRoomState(data.roomState);
        showSuccess(`${data.player.userName} 離開了遊戲`);
    });
    
    socket.on('game-started', (data) => {
        console.log('遊戲開始:', data);
        handleGameStarted(data);
    });
    
    socket.on('dart-thrown', (data) => {
        console.log('飛鏢投擲:', data);
        handleDartThrown(data);
    });
    
    socket.on('game-ended', (data) => {
        console.log('遊戲結束:', data);
        handleGameEnded(data);
    });
    
    socket.on('room-state', (data) => {
        console.log('房間狀態更新:', data);
        updateRoomState(data);
    });
    
    socket.on('leaderboard', (data) => {
        console.log('排行榜更新:', data);
        updateLeaderboard(data.leaderboard);
    });
}

// 設置事件監聽器
function setupEventListeners() {
    joinGameBtn.addEventListener('click', handleJoinGame);
    
    qrCodeInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            handleJoinGame();
        }
    });
    
    scoreBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            selectScore(btn);
        });
    });
    
    throwDartBtn.addEventListener('click', handleThrowDart);
    startGameBtn.addEventListener('click', handleStartGame);
    endGameBtn.addEventListener('click', handleEndGame);
}

// 處理加入遊戲
async function handleJoinGame() {
    const qrCode = qrCodeInput.value.trim();
    
    if (!qrCode) {
        showError('請輸入 QR Code');
        return;
    }
    
    if (!isConnected) {
        showError('Socket 未連接，請稍後再試');
        return;
    }
    
    currentQrCode = qrCode;
    
    // 發送加入房間請求
    socket.emit('join-room', {
        roomId: currentRoomId,
        qrCode: qrCode
    });
    
    joinGameBtn.disabled = true;
    joinGameBtn.textContent = '加入中...';
}

// 處理加入成功
function handleJoinSuccess(data) {
    playerName.textContent = data.playerInfo.userName;
    
    // 隱藏 QR 輸入區域，顯示遊戲控制
    qrInputSection.style.display = 'none';
    gameControls.style.display = 'block';
    
    // 更新房間狀態
    updateRoomState(data.roomState);
    
    showSuccess(`歡迎 ${data.playerInfo.userName}！您已成功加入遊戲`);
    
    // 重置按鈕
    joinGameBtn.disabled = false;
    joinGameBtn.textContent = '加入遊戲';
}

// 選擇分數
function selectScore(btn) {
    // 移除其他按鈕的選中狀態
    scoreBtns.forEach(b => b.classList.remove('selected'));
    
    // 選中當前按鈕
    btn.classList.add('selected');
    selectedScore = parseInt(btn.dataset.score);
    
    // 啟用投擲按鈕
    throwDartBtn.disabled = false;
}

// 處理投擲飛鏢
function handleThrowDart() {
    if (selectedScore === 0) {
        showError('請先選擇分數');
        return;
    }
    
    if (!isConnected) {
        showError('Socket 未連接');
        return;
    }
    
    // 發送投擲事件
    socket.emit('dart-throw', {
        roomId: currentRoomId,
        playerId: socket.id,
        score: selectedScore,
        sector: Math.floor(Math.random() * 20) + 1, // 模擬扇區
        multiplier: Math.random() > 0.8 ? 2 : 1, // 模擬倍數
        timestamp: new Date()
    });
    
    // 更新本地統計
    myThrowCount++;
    throwCount.textContent = myThrowCount;
    
    // 重置選擇
    scoreBtns.forEach(b => b.classList.remove('selected'));
    selectedScore = 0;
    throwDartBtn.disabled = true;
}

// 處理開始遊戲
function handleStartGame() {
    if (!isConnected) {
        showError('Socket 未連接');
        return;
    }
    
    socket.emit('start-game', {
        roomId: currentRoomId
    });
}

// 處理結束遊戲
function handleEndGame() {
    if (!isConnected) {
        showError('Socket 未連接');
        return;
    }
    
    socket.emit('end-game', {
        roomId: currentRoomId,
        finalScore: myScore
    });
}

// 處理遊戲開始
function handleGameStarted(data) {
    gameStartTime = new Date(data.startTime);
    startGameTimer();
    
    startGameBtn.disabled = true;
    endGameBtn.disabled = false;
    
    showSuccess('遊戲開始！開始投擲飛鏢吧！');
    updateRoomState(data.roomState);
}

// 處理飛鏢投擲
function handleDartThrown(data) {
    // 如果是自己的投擲，更新分數
    if (data.player.socketId === socket.id) {
        myScore = data.throw.totalScore;
        currentScore.textContent = myScore;
    }
    
    // 更新房間狀態
    updateRoomState(data.roomState);
    
    // 顯示投擲結果
    showSuccess(`${data.player.userName} 投中 ${data.throw.score} 分！總分: ${data.throw.totalScore}`);
}

// 處理遊戲結束
function handleGameEnded(data) {
    stopGameTimer();
    
    startGameBtn.disabled = false;
    endGameBtn.disabled = true;
    
    showSuccess(`${data.player.userName} 完成遊戲！最終分數: ${data.finalScore}`);
    
    // 更新排行榜
    setTimeout(() => {
        loadLeaderboard();
    }, 1000);
    
    updateRoomState(data.roomState);
}

// 更新房間狀態
function updateRoomState(roomState) {
    if (!roomState) return;
    
    // 更新玩家列表
    updatePlayerList(roomState.players);
}

// 更新玩家列表
function updatePlayerList(players) {
    if (!players || players.length === 0) {
        playerList.innerHTML = '<div class="player-item"><span class="player-name">暫無玩家</span><span class="player-score">-</span></div>';
        return;
    }
    
    playerList.innerHTML = '';
    
    players.forEach(player => {
        const item = document.createElement('div');
        item.className = 'player-item';
        
        const statusClass = player.isPlaying ? 'playing' : 'waiting';
        const statusText = player.isPlaying ? '遊戲中' : '等待中';
        
        item.innerHTML = `
            <div>
                <span class="player-name">${player.userName}</span>
                <span class="player-status ${statusClass}">${statusText}</span>
            </div>
            <span class="player-score">${player.score}</span>
        `;
        
        playerList.appendChild(item);
    });
}

// 載入排行榜
async function loadLeaderboard() {
    if (socket && isConnected) {
        socket.emit('get-leaderboard', {
            roomId: currentRoomId,
            limit: 10
        });
    }
}

// 更新排行榜
function updateLeaderboard(leaderboard) {
    if (!leaderboard || leaderboard.length === 0) {
        leaderboardList.innerHTML = '<div class="leaderboard-item"><span class="rank">暫無記錄</span><span>-</span><span>-</span></div>';
        return;
    }
    
    leaderboardList.innerHTML = '';
    
    leaderboard.forEach(item => {
        const element = document.createElement('div');
        element.className = 'leaderboard-item';
        
        element.innerHTML = `
            <span class="rank">#${item.rank}</span>
            <span>${item.userName}</span>
            <span>${item.score}分</span>
        `;
        
        leaderboardList.appendChild(element);
    });
}

// 開始遊戲計時器
function startGameTimer() {
    if (gameTimer) {
        clearInterval(gameTimer);
    }
    
    gameTimer = setInterval(() => {
        if (gameStartTime) {
            const elapsed = Math.floor((Date.now() - gameStartTime.getTime()) / 1000);
            const minutes = Math.floor(elapsed / 60);
            const seconds = elapsed % 60;
            gameTime.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
    }, 1000);
}

// 停止遊戲計時器
function stopGameTimer() {
    if (gameTimer) {
        clearInterval(gameTimer);
        gameTimer = null;
    }
}

// 更新連接狀態
function updateConnectionStatus(status, text) {
    connectionStatus.className = `connection-status ${status}`;
    connectionStatus.textContent = text;
}

// 顯示錯誤訊息
function showError(message) {
    errorMessage.textContent = message;
    errorMessage.style.display = 'block';
    successMessage.style.display = 'none';
    
    setTimeout(() => {
        errorMessage.style.display = 'none';
    }, 5000);
}

// 顯示成功訊息
function showSuccess(message) {
    successMessage.textContent = message;
    successMessage.style.display = 'block';
    errorMessage.style.display = 'none';
    
    setTimeout(() => {
        successMessage.style.display = 'none';
    }, 3000);
}

// 頁面卸載時清理
window.addEventListener('beforeunload', () => {
    if (socket && currentRoomId) {
        socket.emit('leave-room', { roomId: currentRoomId });
    }
    stopGameTimer();
});

// 導出函數供其他模組使用
export {
    handleJoinGame,
    handleThrowDart,
    updateRoomState,
    loadLeaderboard
};
