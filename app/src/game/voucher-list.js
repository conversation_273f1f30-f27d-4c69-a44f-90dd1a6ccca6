import * as Table from '../table.js';
import { translationText } from '../translation.js';

var account = localStorage.getItem("account");
var table;
var summaryTable;
var tableElem = document.getElementById("table_wrapper");
var summaryTableElem = document.getElementById("summary_table_wrapper");
const titles = document.getElementById("titles");
const content = document.getElementById("content");
var exhibitionData = JSON.parse(localStorage.getItem('exhibition_data'));
var allBtn = document.getElementById("all_btn");
var productsBtn = document.getElementById("products_btn");

productsBtn.addEventListener("click", ()=>{
    document.getElementById("table_wrapper").style.display='none';
    document.getElementById("summary_table_wrapper").style.display='block';
    
});

allBtn.addEventListener("click", ()=>{
    document.getElementById("table_wrapper").style.display='block';
    document.getElementById("summary_table_wrapper").style.display='none';

})



$(document).ready(async function ()  {
    var titleTranslationTexts = [await translationText("id"), await translationText("product_id"), await translationText("user_id"), await translationText("count"), await translationText("timestamp"), await translationText("used"), await translationText("used_time"), await translationText("expired_time")];

    table = Table.initTable("#table",[]);
    summaryTable = Table.initTable("#summary_table",[]);
    document.getElementById("summary_table_wrapper").style.display='none';

    for(let i=0;i < titles.children.length;i++){
        titles.children.item(i).textContent = titleTranslationTexts[i];
    }
    var where = encodeURIComponent(JSON.stringify({ activity_id:exhibitionData[0] }));
    Table.updateTable(table, ["voucher/list"],['id','product_id','uid','count','timestamp','is_used','usedTime','expiredTime'], `where=${where}`, "pd0001");
    Table.updateTable(summaryTable, ["voucher/aggregate_by_product"],['product_id','count','averageOwnTime'], `where=${where}`, "pd0001");
} );