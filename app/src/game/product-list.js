import * as Table from '../table.js';
import { translationText } from '../translation.js';

var account = localStorage.getItem("account");
var table;
const title = document.getElementById("titles");
const content = document.getElementById("content");
var projectData= JSON.parse(localStorage.getItem('project_data'));



$(document).ready(async function ()  {
    var titleTranslationTexts = [await translationText("id"), await translationText("name"), await translationText("store")];

    table = Table.initTable("#table",[]);

    for(let i=0;i < titles.children.length;i++){
        titles.children.item(i).textContent = titleTranslationTexts[i];
    }

    var where = encodeURIComponent(JSON.stringify({ project_id:projectData[0] }));
    console.log(projectData[0])
    Table.updateTable(table, ["product/list"],['id','name','count'], `where=${where}`, "pd0001");
} );