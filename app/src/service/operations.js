import {handleError} from '../handle_error.js';
import * as tokenOps from '../user/tokenOps.js';
import {generateHash, generateSalt, verifyHash} from '../utils/crypto.js';
import { linkApiRoot } from '../utils/env.js';
var data = JSON.parse(localStorage.getItem("exhibition_data"));
var token = tokenOps.getToken();

export function back(){
    window.location.href = "/html/exhibition/view.html";
}

export function displayerPage(){
    fetch(linkApiRoot(`exhibition/${data[0]}`),
        {
            headers:{ Authorization: 'Bearer '+ token },
        })
        .then(handleError)
        .then(res => res.json())
        .then(exhibitionData =>{
            window.location.href = `../video/displayer.html?hashID=${exhibitionData.hashID}`;
        })
        .catch(e=>{
            console.error(e);
        })  
}

export function vendingDisplayerPage(){
    fetch(linkApiRoot(`exhibition/${data[0]}`),
        {
            headers:{ Authorization: 'Bearer '+ token },
        })
        .then(handleError)
        .then(res => res.json())
        .then(exhibitionData =>{
            window.location.href = `https://tianyen-service.com:4008/vending_displayer/?channel_id=${exhibitionData.hashID}`;
            //window.location.href = `http://localhost:3008/vending_displayer/?channel_id=${exhibitionData.hashID}`;
        })
        .catch(e=>{
            console.error(e);
        })  
    
  }
  
  export function vendingControllerPage(){
    fetch(linkApiRoot(`exhibition/${data[0]}`),
        {
            headers:{ Authorization: 'Bearer '+ token },
        })
        .then(handleError)
        .then(res => res.json())
        .then(exhibitionData =>{
            window.location.href=`https://tianyen-service.com:4007/vending_controller?openExternalBrowser=1&channel_id=${exhibitionData.hashID}`;
        })
        .catch(e=>{
            console.error(e);
        }) 
  }

  export function clawControllerPage(){
    //window.location.href="https://tianyen-service.com:4004/rd0005";
    window.location.href="/claw_machine/controller/index.html";
  }

export function getExhibition(event){
    return fetch(linkApiRoot('exhibition/') + data[0],
        {
            headers:{ Authorization: 'Bearer '+ token }
        }
        )
        .then(handleError)
        .then(response => response.json());
}

export async function executeExhibition(expiration){
    var salt = generateSalt();
    var hash = await generateHash(data[0], salt);
    return fetch(linkApiRoot(`exhibition/${data[0]}`),
    {
        method: 'PUT',
        headers:{
        'Content-Type': 'application/json' ,
         Authorization: 'Bearer '+ token },
        body:JSON.stringify({
            status: "OPEN",
            expiration: expiration,
            salt: salt,
            hashID: hash
        })
    }
    )
    .then(handleError)
    .then(res => res.json());
  }
  
export function stopExhibition(event){
    return fetch(linkApiRoot(`exhibition/${data[0]}`),
    {
        method: 'PUT',
        headers:{
        'Content-Type': 'application/json' ,
         Authorization: 'Bearer '+ token },
        body:JSON.stringify({
            status: "CLOSE",
            expiration: null,
            salt:null,
            hashID:null
        })
    }
    )
    .then(handleError)
    .then(res => res.json());
  }