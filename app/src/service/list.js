import {back, displayerPage, vendingControllerPage, vendingDisplayerPage, clawControllerPage} from './operations.js';
import { gameStatus } from '../health.js';
var data = JSON.parse(localStorage.getItem("exhibition_data"));
var vendingDisplayerBtn = document.getElementById('vendingDisplayerBtn');
var vendingControllerBtn = document.getElementById('vendingControllerBtn');
var playGameBtn = document.getElementById('playGameBtn');

//loadContent('../exhibition/details.html');
if(playGameBtn)
  playGameBtn.style.display = 'none';
// vendingBtn.style.display = 'none';

gameStatus().then((result)=>{
  if(playGameBtn)
    playGameBtn.style.display = 'inline';
})

// fetch("https://localhost:3001/game/health")
//   .then(res => res.json())
//   .then((result)=>{
//       playGameBtn.style.display = 'inline';
//   })
//   .catch(e=>{
//       console.error(e);
//   })

// fetch("https://localhost:3001/vending/health")
//   .then(res => res.json())
//   .then((result)=>{
//       vendingBtn.style.display = 'inline';
//   })
//   .catch(e=>{
//       console.error(e);
//   })


document.getElementById('backBtn').addEventListener('click', function() {
    back();
});

document.getElementById('playBtn').addEventListener('click', function() {
  displayerPage();
});

document.getElementById('playGameBtn').addEventListener('click', function(){
  playGamePage();
});

vendingDisplayerBtn.addEventListener('click', function(){
  vendingDisplayerPage();
});

vendingControllerBtn.addEventListener('click', function(){
  vendingControllerPage();
})

document.getElementById('clawControllerBtn').addEventListener('click', function(){
  clawControllerPage();
})

function playGamePage(){
  switch(data[3]){
    case "LuckyDart":
      window.location.href="/game/luckydartdisplayer";
  }
}
