import { linkApiRoot } from '../utils/env.js';
import { handleError } from '../handle_error.js';

// DOM 元素
const roomName = document.getElementById('room-name');
const roomDescription = document.getElementById('room-description');
const roomId = document.getElementById('room-id');

const loading = document.getElementById('loading');
const errorMessage = document.getElementById('error-message');
const welcomeMessage = document.getElementById('welcome-message');
const welcomeText = document.getElementById('welcome-text');
const welcomeDetails = document.getElementById('welcome-details');

const scannerContainer = document.getElementById('scanner-container');
const manualInputBtn = document.getElementById('manual-input-btn');
const manualInput = document.getElementById('manual-input');
const qrCodeInput = document.getElementById('qr-code-input');
const manualCheckinBtn = document.getElementById('manual-checkin-btn');

const statsContainer = document.getElementById('stats-container');
const todayCheckins = document.getElementById('today-checkins');
const totalCheckins = document.getElementById('total-checkins');
const gameParticipants = document.getElementById('game-participants');
const currentPlayers = document.getElementById('current-players');

const checkinList = document.getElementById('checkin-list');

// 從 URL 參數獲取房間資訊
const urlParams = new URLSearchParams(window.location.search);
const currentRoomId = urlParams.get('roomId') || 'room-001';
const currentRoomName = urlParams.get('roomName') || '飛鏢遊戲區';

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeRoom();
    setupEventListeners();
    startAutoRefresh();
});

// 初始化房間
function initializeRoom() {
    roomId.textContent = currentRoomId;
    roomName.textContent = currentRoomName;
    roomDescription.textContent = `歡迎來到 ${currentRoomName}，請掃描您的報名 QR Code 進行簽到`;
    
    loadRoomStats();
    loadRecentCheckins();
}

// 設置事件監聽器
function setupEventListeners() {
    manualInputBtn.addEventListener('click', toggleManualInput);
    manualCheckinBtn.addEventListener('click', handleManualCheckin);
    qrCodeInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            handleManualCheckin();
        }
    });
    
    // 模擬 QR Code 掃描 (實際應用中會整合真正的掃描器)
    document.addEventListener('keydown', function(e) {
        // 如果按下 Enter 且輸入框有值，自動處理簽到
        if (e.key === 'Enter' && qrCodeInput.value.trim()) {
            handleManualCheckin();
        }
    });
}

// 切換手動輸入
function toggleManualInput() {
    const isVisible = manualInput.style.display === 'flex';
    manualInput.style.display = isVisible ? 'none' : 'flex';
    manualInputBtn.textContent = isVisible ? '手動輸入 QR Code' : '隱藏輸入框';
    
    if (!isVisible) {
        qrCodeInput.focus();
    }
}

// 處理手動簽到
async function handleManualCheckin() {
    const qrCode = qrCodeInput.value.trim();
    
    if (!qrCode) {
        showError('請輸入 QR Code');
        return;
    }
    
    await processCheckin(qrCode);
}

// 處理簽到邏輯
async function processCheckin(qrCode) {
    try {
        showLoading(true);
        hideMessages();
        
        const response = await fetch(linkApiRoot('checkin'), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                qrCode: qrCode,
                roomId: currentRoomId,
                roomName: currentRoomName
            })
        });
        
        const result = await response.json();
        
        if (!response.ok) {
            throw new Error(result.error || '簽到失敗');
        }
        
        if (result.success) {
            showWelcomeMessage(result);
            qrCodeInput.value = ''; // 清空輸入框
            
            // 更新統計和記錄
            setTimeout(() => {
                loadRoomStats();
                loadRecentCheckins();
            }, 1000);
        } else {
            throw new Error(result.error || '簽到失敗');
        }
        
    } catch (error) {
        console.error('簽到失敗:', error);
        showError(error.message || '簽到失敗，請稍後再試');
    } finally {
        showLoading(false);
    }
}

// 顯示歡迎訊息
function showWelcomeMessage(result) {
    const { data, isRepeatCheckIn } = result;
    
    welcomeText.textContent = result.message;
    
    const checkInTime = new Date(data.checkInTime).toLocaleString('zh-TW');
    const details = [
        `簽到時間: ${checkInTime}`,
        `活動: ${data.exhibitionName}`
    ];
    
    if (isRepeatCheckIn) {
        details.push('(重複簽到)');
    }
    
    welcomeDetails.textContent = details.join(' | ');
    
    welcomeMessage.style.display = 'block';
    scannerContainer.style.display = 'none';
    
    // 3秒後自動隱藏歡迎訊息，顯示掃描器
    setTimeout(() => {
        welcomeMessage.style.display = 'none';
        scannerContainer.style.display = 'block';
    }, 3000);
}

// 載入房間統計
async function loadRoomStats() {
    try {
        const response = await fetch(linkApiRoot(`game-room/${currentRoomId}/stats`));
        
        if (response.ok) {
            const result = await response.json();
            if (result.success) {
                const stats = result.data;
                todayCheckins.textContent = stats.todayCheckIns || 0;
                totalCheckins.textContent = stats.checkInCount || 0;
                gameParticipants.textContent = stats.totalGames || 0;
                currentPlayers.textContent = stats.currentPlayers || 0;
            }
        }
    } catch (error) {
        console.error('載入房間統計失敗:', error);
    }
}

// 載入最近簽到記錄
async function loadRecentCheckins() {
    try {
        const response = await fetch(linkApiRoot(`room/${currentRoomId}/checkins?limit=10`));
        
        if (response.ok) {
            const result = await response.json();
            if (result.success && result.data.length > 0) {
                displayRecentCheckins(result.data);
            } else {
                checkinList.innerHTML = '<div class="checkin-item"><span class="checkin-name">暫無簽到記錄</span><span class="checkin-time">-</span></div>';
            }
        }
    } catch (error) {
        console.error('載入簽到記錄失敗:', error);
        checkinList.innerHTML = '<div class="checkin-item"><span class="checkin-name">載入失敗</span><span class="checkin-time">-</span></div>';
    }
}

// 顯示最近簽到記錄
function displayRecentCheckins(checkins) {
    checkinList.innerHTML = '';
    
    checkins.forEach(checkin => {
        const item = document.createElement('div');
        item.className = 'checkin-item';
        
        const checkInTime = new Date(checkin.checkInTime).toLocaleString('zh-TW', {
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
        
        item.innerHTML = `
            <span class="checkin-name">${checkin.registration.userName}</span>
            <span class="checkin-time">${checkInTime}</span>
        `;
        
        checkinList.appendChild(item);
    });
}

// 開始自動刷新
function startAutoRefresh() {
    // 每30秒刷新一次統計和記錄
    setInterval(() => {
        loadRoomStats();
        loadRecentCheckins();
    }, 30000);
}

// 工具函數
function showError(message) {
    errorMessage.textContent = message;
    errorMessage.style.display = 'block';
    welcomeMessage.style.display = 'none';
}

function hideMessages() {
    errorMessage.style.display = 'none';
    welcomeMessage.style.display = 'none';
}

function showLoading(show) {
    loading.style.display = show ? 'block' : 'none';
}

// 導出函數供其他模組使用
export {
    processCheckin,
    loadRoomStats,
    loadRecentCheckins,
    showWelcomeMessage
};
