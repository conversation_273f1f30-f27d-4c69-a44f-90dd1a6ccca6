import { linkApiRoot } from '../utils/env.js';
import { handleError } from '../handle_error.js';

// DOM 元素
const exhibitionInfo = document.getElementById('exhibition-info');
const exhibitionName = document.getElementById('exhibition-name');
const exhibitionDescription = document.getElementById('exhibition-description');
const exhibitionStatus = document.getElementById('exhibition-status');
const exhibitionTime = document.getElementById('exhibition-time');

const registrationForm = document.getElementById('registration-form');
const submitBtn = document.getElementById('submit-btn');
const errorMessage = document.getElementById('error-message');
const successMessage = document.getElementById('success-message');
const loading = document.getElementById('loading');

const registrationInfo = document.getElementById('registration-info');
const registrationId = document.getElementById('registration-id');
const registrationTime = document.getElementById('registration-time');

const qrCodeContainer = document.getElementById('qr-code-container');
const qrCodeImage = document.getElementById('qr-code-image');
const downloadQr = document.getElementById('download-qr');

// 從 URL 參數獲取展覽 ID
const urlParams = new URLSearchParams(window.location.search);
const exhibitionId = urlParams.get('exhibitionId');

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    if (!exhibitionId) {
        showError('缺少展覽 ID 參數');
        return;
    }
    
    loadExhibitionInfo();
    setupEventListeners();
});

// 載入展覽資訊
async function loadExhibitionInfo() {
    try {
        const response = await fetch(linkApiRoot(`exhibition/${exhibitionId}`));
        
        if (!response.ok) {
            throw new Error('無法載入展覽資訊');
        }
        
        const data = await response.json();
        
        exhibitionName.textContent = data.name;
        exhibitionDescription.textContent = data.description;
        exhibitionStatus.textContent = data.status === 'OPEN' ? '開放中' : '未開放';
        
        // 格式化時間
        if (data.start_time && data.end_time) {
            exhibitionTime.textContent = `${data.start_time} - ${data.end_time}`;
        } else {
            exhibitionTime.textContent = '時間未設定';
        }
        
        // 檢查展覽狀態
        if (data.status !== 'OPEN') {
            showError('此展覽目前未開放報名');
            submitBtn.disabled = true;
        }
        
    } catch (error) {
        console.error('載入展覽資訊失敗:', error);
        showError('載入展覽資訊失敗，請稍後再試');
    }
}

// 設置事件監聽器
function setupEventListeners() {
    registrationForm.addEventListener('submit', handleRegistration);
    downloadQr.addEventListener('click', downloadQRCode);
}

// 處理報名提交
async function handleRegistration(event) {
    event.preventDefault();
    
    const formData = new FormData(registrationForm);
    const registrationData = {
        exhibitionId: parseInt(exhibitionId),
        userName: formData.get('userName').trim(),
        userPhone: formData.get('userPhone').trim(),
        userEmail: formData.get('userEmail').trim()
    };
    
    // 驗證必填欄位
    if (!registrationData.userName) {
        showError('請填寫姓名');
        return;
    }
    
    // 驗證電子郵件格式
    if (registrationData.userEmail && !isValidEmail(registrationData.userEmail)) {
        showError('請填寫正確的電子郵件格式');
        return;
    }
    
    try {
        showLoading(true);
        hideMessages();
        
        const response = await fetch(linkApiRoot('registration'), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(registrationData)
        });
        
        const result = await response.json();
        
        if (!response.ok) {
            throw new Error(result.error || '報名失敗');
        }
        
        if (result.success) {
            showRegistrationSuccess(result.data);
        } else {
            throw new Error(result.error || '報名失敗');
        }
        
    } catch (error) {
        console.error('報名失敗:', error);
        showError(error.message || '報名失敗，請稍後再試');
    } finally {
        showLoading(false);
    }
}

// 顯示報名成功
function showRegistrationSuccess(data) {
    // 隱藏表單
    registrationForm.style.display = 'none';
    
    // 顯示成功資訊
    registrationId.textContent = data.registrationId;
    registrationTime.textContent = new Date(data.registeredAt).toLocaleString('zh-TW');
    registrationInfo.style.display = 'block';
    
    // 顯示 QR Code
    qrCodeImage.src = data.qrCodeImage;
    qrCodeImage.alt = `報名 QR Code - ${data.userName}`;
    downloadQr.href = data.qrCodeImage;
    downloadQr.download = `registration-${data.registrationId}-qr-code.png`;
    qrCodeContainer.style.display = 'block';
    
    showSuccess(`${data.userName}，您已成功報名 ${data.exhibitionName}！`);
    
    // 滾動到 QR Code
    setTimeout(() => {
        qrCodeContainer.scrollIntoView({ behavior: 'smooth' });
    }, 500);
}

// 下載 QR Code
function downloadQRCode(event) {
    event.preventDefault();
    
    const link = document.createElement('a');
    link.href = qrCodeImage.src;
    link.download = downloadQr.download;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 工具函數
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function showError(message) {
    errorMessage.textContent = message;
    errorMessage.style.display = 'block';
    successMessage.style.display = 'none';
}

function showSuccess(message) {
    successMessage.textContent = message;
    successMessage.style.display = 'block';
    errorMessage.style.display = 'none';
}

function hideMessages() {
    errorMessage.style.display = 'none';
    successMessage.style.display = 'none';
}

function showLoading(show) {
    loading.style.display = show ? 'block' : 'none';
    submitBtn.disabled = show;
    
    if (show) {
        submitBtn.textContent = '處理中...';
    } else {
        submitBtn.textContent = '確認報名';
    }
}

// 導出函數供其他模組使用
export {
    loadExhibitionInfo,
    handleRegistration,
    showRegistrationSuccess
};
