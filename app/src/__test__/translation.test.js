import { setLanguage, translationText}from "../translation";
import { screen, fireEvent } from "@testing-library/dom";

describe("test translation function",()=>
    test('First Translation Text', async ()=>{
        initDom();
        setLanguage('tw');
        expect(await translationText("welcome")).toBe("Welcome to IAMM.")
    })
)

function initDom(){
    document.body.innerHTML = `
    <form>
      <input id="name" />
      <button type="submit">Submit</button>
    </form>
  `;
}