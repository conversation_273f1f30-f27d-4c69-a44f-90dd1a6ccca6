import * as Table from '../table.js';
import { translationText } from '../translation.js';

var account = localStorage.getItem("account");
var table;
const titles = document.getElementById("titles");
const content = document.getElementById("content");
var exhibitionData = JSON.parse(localStorage.getItem('exhibition_data'));
var titleTranslationTexts = [await translationText("id"), await translationText("product_id"), await translationText("user_id"), await translationText("count"), await translationText("timestamp"), await translationText("used")];


$(document).ready( function ()  {
    table = Table.initTable("#table",[]);

    for(let i=0;i < titles.children.length;i++){
        titles.children.item(i).textContent = titleTranslationTexts[i];
    }
    
    var where = encodeURIComponent(JSON.stringify({ activity_id:exhibitionData[0] }));
    Table.updateTable(table, ["voucher/list"],['id','product_id','uid','count','timestamp','is_used'], `where=${where}`, "pd0001");
} );