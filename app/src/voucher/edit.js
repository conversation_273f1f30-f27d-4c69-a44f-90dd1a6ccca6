import {handleError} from '../handle_error.js';
import { gameStatus } from '../health.js';
var token = localStorage.getItem("token");
var account = localStorage.getItem('account');
var form = document.getElementById('voucher_form');

form.addEventListener('submit', async function(event) {
    const formData = new FormData(form);
    event.preventDefault();
    const errorMessage = document.getElementById('error_message');
    var object = {};
    formData.forEach(function(value, key){
        object[key] = value;
    });
    var json = JSON.stringify(object);
    
    fetch('https://localhost:4000/api/v1/voucher/update', {
        method: 'POST',
        headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        Authorization: 'Bearer '+ token 
        },
        body: json
    })
    .then(handleError)
    .then(response => response.json())
    .then(data => {
        window.location.href = "./list.html";
    })
    .catch(error =>{
        errorMessage.innerHTML = "Creating project failed.";
    })
});