import * as tokenOps from '../../src/user/tokenOps.js';
import {handleError} from '../../src/handle_error.js';
var token = tokenOps.getToken();
var name = document.getElementById("nameText");
var count = document.getElementById("countText");
var data = JSON.parse(localStorage.getItem("exhibition_data"));
load();
function load(){
    fetch('exhibition/' + data[0],
    {
        headers:{ Authorization: 'Bearer '+ token }
    }
    )
    .then(handleError)
    .then(response => response.json()) 
    .then(productData =>{
        name.innerText = productData.name;
        count.innerText = productData.count;
    })
    .catch(error => {
        console.error(error);
    });
}