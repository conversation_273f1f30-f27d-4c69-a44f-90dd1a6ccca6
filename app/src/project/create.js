import {handleError} from '../handle_error.js';
import { gameStatus } from '../health.js';
import { linkApiRoot } from '../utils/env.js';
import * as tokenOps from '../user/tokenOps.js';

// Check if user is authenticated
var token = tokenOps.getToken();
if (!token || !tokenOps.validateToken()) {
    console.log('No valid token found, redirecting to login');
    window.location.href = '../../index.html';
}

var account = localStorage.getItem('account');
var form = document.getElementById('project_form');
form.addEventListener('submit', async function(event) {
    const formData = new FormData(form);
    event.preventDefault(); // 阻止表單的默認提交行為
    const errorMessage = document.getElementById('error_message');
    // 建立表單數據
    var object = {};
    formData.forEach(function(value, key){
        object[key] = value;
    });
    
    formData.append("created_by", account); 
    var json = JSON.stringify(object);
    // 使用 Fetch 提交表單
    fetch(linkApiRoot('project'), {
        method: 'POST',
        headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        Authorization: 'Bearer '+ token 
        },
        body: json
    })
    .then(handleError)
    .then(response => response.json()) // 將回應轉換成 JSON 格式
    .then(async data => {
        let {status} = await gameStatus();
        if(status == "ok"){
            await fetch('http://127.0.0.1:4000/api/project/create', {
                method: 'POST',
                headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    id: data.id,
                    name:data.name
                })
            });
        }
        window.location.href = "./list.html";
    })
    .catch(error =>{
        errorMessage.innerHTML = "Creating project failed.";
    })
});