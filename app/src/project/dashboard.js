import {listPage, getProject, executeProject, stopProject, displayerPage} from './operations.js';
import {loadContent} from '../utils/dynamicLoading.js';
var data = JSON.parse(localStorage.getItem("project_data"));
var popup = document.getElementById("executePopup");
var expirationTime = document.getElementById('expirationTimeInput');
var wantToExecuteBtn = document.getElementById('wantToExecuteBtn');

//loadContent('./project/details.html');

// getProject().then((projectData)=>{
//     if(projectData.status == 'OPEN')
//         wantToExecuteBtn.textContent = "中止";
//     else
//         wantToExecuteBtn.textContent = "執行";
// });

document.getElementById('backBtn').addEventListener('click', function() {
    listPage();
});
document.getElementById('detailsBtn').addEventListener('click', function() {
  //loadContent('./project/details.html');
  window.location.href='./project/details.html';
});
document.getElementById('operationBtn').addEventListener('click', function() {
  //loadContent('./group/list.html');
window.location.href='./group/list.html';
});
document.getElementById('exhibitionBtn').addEventListener('click', function() {
  //loadContent('./exhibition/list.html');
  window.location.href='./exhibition/list.html';
});
document.getElementById('monitoringBtn').addEventListener('click', function() {
  //loadContent('./project/monitoring.html');
  window.location.href='./project/monitoring.html';
});
document.getElementById('settingBtn').addEventListener('click', function() {
  //loadContent('./project/settings.html');
  window.location.href='./project/settings.html';
});
document.getElementById('statisticsBtn').addEventListener('click', function(){
  //loadContent('./project/statistics.html');
  window.location.href='./project/statistics.html';
});
// wantToExecuteBtn.addEventListener('click', function() {
//   if( wantToExecuteBtn.textContent == "執行"){
//     showExecuteWindow();
//   }else{
//     if(showStopWindow()){
//       stopProject().then((data)=>{
//         window.location.reload();
//       });
//       wantToExecuteBtn.textContent = "執行";
//     }
//   }
// });
// document.getElementById('closeBtn').addEventListener('click', function() {
//   closeExecuteWindow();
// });
// document.getElementById('executeBtn').addEventListener('click',async function() {
//   const expiration = expirationTime.value? new Date(expirationTime.value).toISOString() : null;
//   executeProject(expiration).then((data)=>{
//     wantToExecuteBtn.textContent = "中止";
//     closeExecuteWindow();
//     window.location.reload();
//   });
// });
// document.getElementById('playBtn').addEventListener('click', function() {
//   displayerPage();
// });

// document.getElementById('playGameBtn').addEventListener('click', function(){
//   playGamePage();
// });

function playGamePage(){
  window.location.href="../game";
}

function showExecuteWindow() {
  popup.classList.add("show");
}

function closeExecuteWindow(){
  popup.classList.remove("show");
}

function showStopWindow(){
  return confirm("Are you sure you want to stop the project?");
}
