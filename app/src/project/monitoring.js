import * as tokenOps from '../user/tokenOps.js';
import {handleError} from '../handle_error.js';
import * as tableOps from '../table.js';
import { translationText } from '../translation.js';
import { linkApiRoot } from '../utils/env.js';
import { exportData } from '../export.js';

var token = tokenOps.getToken();
var data = JSON.parse(localStorage.getItem("project_data"));
var table = tableOps.initTable("#displayers-table");
var titles = document.getElementById("displayers-title");
var connectAmount = document.getElementById('connect-amount');
var pid;
var titleTranslationTexts;

document.onload=async()=>{
    titleTranslationTexts = [await translationText("exhibition_name"), await translationText("id"), await translationText("ip"), await translationText("status")];

    for(let i=0;i < titles.children.length;i++){
        titles.children.item(i).textContent = titleTranslationTexts[i];
    }
    document.getElementById("connects_label").textContent = await translationText("connects");
}

setInterval(loadData, 3000);
loadData();
function loadData() {
    fetch(linkApiRoot(`project/${data[0]}`),
    {
        headers:{ Authorization: 'Bearer '+ token },
    })
    .then(handleError)
    .then(res => res.json())
    .then(projectData =>{
        pid = projectData.id;
        connectAmount.textContent = tableOps.updateTable(table, ["connections?pid="+ pid], ['exhibitionName',"id","ip","status"]);
    })
    .catch(e=>{
        console.error(e);
    });
}

document.getElementById('export_data_btn').addEventListener('click',function(){
    if(pid)
        exportData('connections',`pid=${pid}`);
});