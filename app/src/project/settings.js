import * as tokenOps from '../user/tokenOps.js';
import {handleError} from '../handle_error.js';
import {translationText} from '../translation.js';
import { linkApiRoot } from '../utils/env.js';

var token = tokenOps.getToken();
var editForm = document.getElementById("edit-form");
var titleLabel = document.getElementById("title_text");
var descriptionLabel = document.getElementById("description_text");
var title = document.getElementById("title");
var description = document.getElementById("description");
var switchOpeningAndCloseing = document.getElementById("switch");
var OpeningClosingDiv = document.getElementById("opening-closing-times");
var startTime = document.getElementById("startTime");
var endTime =document.getElementById("endTime");
var data = JSON.parse(localStorage.getItem("project_data"));

document.onload = async()=>{
    titleLabel.textContent = await translationText("title");
    descriptionLabel.textContent = await translationText("description");
}

loadData();
  function loadData(){
    fetch(linkApiRoot('project/') + data[0],
    {
        headers:{ Authorization: 'Bearer '+ token }
    }
    )
    .then(handleError)
    .then(response => response.json()) 
    .then(projectData =>{
        title.value = projectData.name;
        description.value = projectData.description.toString();
        if(projectData.start_time){
            switchOpeningAndCloseing.checked = true;
            OpeningClosingDiv.style.display = "block";
        }
        startTime.value = projectData.start_time;
        endTime.value = projectData.end_time;
    })
    .catch(error => {
        console.error(error);
    });
}

  // 監聽時間輸入框的變化
startTime.addEventListener("change", validateTimes);
endTime.addEventListener("change", validateTimes);
switchOpeningAndCloseing.addEventListener("click", function(){
    OpeningClosingDiv.style.display = this.checked? "block" : "none";
});

function validateTimes() {
    const startTime = document.getElementById("startTime").value;
    const endTime = document.getElementById("endTime").value;

    return startTime && endTime;
}
editForm.addEventListener("submit", function(event) {
    event.preventDefault();
    if(switchOpeningAndCloseing.checked &&!validateTimes()){
        return;
    }
    if(!switchOpeningAndCloseing.checked){
        startTime.value="";
        endTime.value="";
    }
    const formData = new FormData(editForm);
    var object = {};
    formData.forEach(function(value, key){
        object[key] = value;
    });
    var json = JSON.stringify(object);
    console.log(json);
    fetch(linkApiRoot('project/') + data[0],
    {
        method: 'PUT',
        headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            Authorization: 'Bearer '+ token 
        },
        body: json
    }
    )
    .then(handleError)
    .then(response => response.json())
    .then(data => {
        window.location.reload();
    })
    .catch(err=>{
        error.textContent = err;
    })
});