import * as tokenOps from '../user/tokenOps.js';
import * as tableOps from '../table.js';
import { exportData } from '../export.js';
import {translationText} from "../translation.js";

// Check if user is authenticated
var token = tokenOps.getToken();
if (!token || !tokenOps.validateToken()) {
    console.log('No valid token found, redirecting to login');
    window.location.href = '../../index.html';
}

var data = JSON.parse(localStorage.getItem("project_data"));
if (!data || !data[0]) {
    console.log('No project data found, using test data');
    // Set test data for development
    data = [1, "測試專案"];
    localStorage.setItem("project_data", JSON.stringify(data));
}

var exportDataBtn = document.getElementById('export_data_btn');
var titles = document.getElementById("titles");
var where = encodeURIComponent(JSON.stringify({ pid:data[0] }));
var titleTranslationTexts;

document.onload=async()=>{
    exportDataBtn.value = await translationText("export");
    titleTranslationTexts = [await translationText("exhibition_name"), await translationText("name"), await translationText("impressions"), await translationText("time_spent"), await translationText("duration")];
    for(let i=0;i < titles.children.length;i++){
        titles.children.item(i).textContent = titleTranslationTexts[i];
    }
}

// Initialize table with proper column definitions
var columnDefs = [
    { targets: 0, title: "展覽名稱" },
    { targets: 1, title: "項目名稱" },
    { targets: 2, title: "參與人數" },
    { targets: 3, title: "總時長" },
    { targets: 4, title: "平均分數" }
];

var table = tableOps.initTable('#statistics-table', columnDefs);

// Use available exhibition stats API instead of non-existent video API
async function loadStatisticsData() {
    try {
        // Get project exhibitions first
        const exhibitionsResponse = await fetch(`http://localhost:3062/api/v1/project/${data[0]}/exhibitions`, {
            headers: {
                'Authorization': 'Bearer ' + token,
                'Accept': 'application/json'
            }
        });

        if (!exhibitionsResponse.ok) {
            throw new Error(`HTTP ${exhibitionsResponse.status}: ${exhibitionsResponse.statusText}`);
        }

        const exhibitionsData = await exhibitionsResponse.json();
        console.log('Exhibitions data:', exhibitionsData);

        // Clear existing table data
        table.clear();

        if (exhibitionsData.items && exhibitionsData.items.length > 0) {
            // Load stats for each exhibition
            for (const exhibition of exhibitionsData.items) {
                try {
                    const statsResponse = await fetch(`http://localhost:3062/api/v1/exhibition/${exhibition.id}/complete-stats`, {
                        headers: {
                            'Authorization': 'Bearer ' + token,
                            'Accept': 'application/json'
                        }
                    });

                    if (statsResponse.ok) {
                        const statsData = await statsResponse.json();
                        console.log(`Stats for exhibition ${exhibition.id}:`, statsData);

                        // Add row to table
                        table.row.add([
                            exhibition.name || '未命名展覽',
                            exhibition.description || '無描述',
                            statsData.data?.registration?.totalRegistrations || 0,
                            `${statsData.data?.checkIn?.totalCheckIns || 0} 次簽到`,
                            statsData.data?.game?.averageScore || 'N/A'
                        ]);
                    }
                } catch (error) {
                    console.error(`Error loading stats for exhibition ${exhibition.id}:`, error);
                    // Add row with basic info even if stats fail
                    table.row.add([
                        exhibition.name || '未命名展覽',
                        exhibition.description || '無描述',
                        'N/A',
                        'N/A',
                        'N/A'
                    ]);
                }
            }
        } else {
            // No exhibitions found, add a placeholder row
            table.row.add([
                '無展覽數據',
                '此專案尚未創建任何展覽',
                '0',
                '0',
                'N/A'
            ]);
        }

        // Redraw table
        table.draw();

    } catch (error) {
        console.error('Error loading statistics data:', error);
        // Add error row
        table.clear();
        table.row.add([
            '載入錯誤',
            error.message || '無法載入統計數據',
            'N/A',
            'N/A',
            'N/A'
        ]);
        table.draw();
    }
}

// Load data initially
loadStatisticsData();

// Refresh data every 30 seconds (reduced frequency)
setInterval(loadStatisticsData, 30000);

exportDataBtn.addEventListener('click', async function(){
    if (data && data[0]) {
        try {
            // Export project exhibitions data
            const response = await fetch(`http://localhost:3062/api/v1/project/${data[0]}/exhibitions`, {
                headers: {
                    'Authorization': 'Bearer ' + token,
                    'Accept': 'application/json'
                }
            });

            if (response.ok) {
                const exhibitionsData = await response.json();

                // Convert to CSV format
                const csvData = exhibitionsData.items.map(exhibition => ({
                    id: exhibition.id,
                    name: exhibition.name,
                    description: exhibition.description,
                    created_at: exhibition.created_at,
                    updated_at: exhibition.updated_at
                }));

                // Download CSV
                downloadCSV(arrayToCSV(csvData), `project_${data[0]}_exhibitions.csv`);
            } else {
                alert('無法導出數據：' + response.statusText);
            }
        } catch (error) {
            console.error('Export error:', error);
            alert('導出失敗：' + error.message);
        }
    } else {
        alert('No project data available for export');
    }
});

// Helper functions for CSV export
function arrayToCSV(arr) {
    if(arr.length === 0) return '';
    let headers = Object.keys(arr[0]);
    let csv = headers.join(',') + '\n';
    csv += arr.map(item => Object.values(item).join(',')).join('\n');
    return csv;
}

function downloadCSV(csvContent, filename = 'data.csv') {
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = filename;
    link.click();
}

