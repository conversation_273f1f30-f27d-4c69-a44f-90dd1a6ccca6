import * as tokenOps from '../user/tokenOps.js';
import {handleError} from '../handle_error.js';
import { linkApiRoot } from '../utils/env.js';

var token = tokenOps.getToken();
//var title = document.getElementById("titleText");
var description = document.getElementById("descriptionText");
var expiration = document.getElementById("expirationTimeText");
var startTime = document.getElementById("startTimeText");
var endTime =document.getElementById("endTimeText");
var data = JSON.parse(localStorage.getItem("project_data"));
load();
function load(){
    fetch(linkApiRoot('project/') + data[0],
    {
        headers:{ Authorization: 'Bearer '+ token }
    }
    )
    .then(handleError)
    .then(response => response.json()) 
    .then(projectData =>{
        console.log(projectData);
        var expirationDataTime = projectData.expiration? new Date(projectData.expiration).toLocaleString(): null;
        //title.innerText = projectData.name;
        description.innerText = projectData.description;
        // expiration.textContent = expirationDataTime;
        // startTime.innerText = projectData.start_time;
        // endTime.innerText = projectData.end_time;
    })
    .catch(error => {
        console.error(error);
    });
}