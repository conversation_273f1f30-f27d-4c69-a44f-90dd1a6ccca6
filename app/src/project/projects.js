import * as tokenOps from '../user/tokenOps.js';
import {handleError} from '../handle_error.js';
import { doubleClickTable, initTable, updateTable } from '../table.js';
import { gameStatus } from '../health.js';
import {translationText} from '../translation.js';
import { linkApiRoot } from '../utils/env.js';

// Check if user is authenticated
var token = tokenOps.getToken();
if (!token || !tokenOps.validateToken()) {
    console.log('No valid token found, redirecting to login');
    window.location.href = '../../index.html';
}

var account = localStorage.getItem("account");
var table;
const projects_table = document.getElementById("projects-table");
const title = document.getElementById("title");
const content = document.getElementById("content");
var create_project_btn = document.getElementById("create_project_btn");
var delete_project_btn = document.getElementById("delete_project_btn");
var join_group_btn = document.getElementById("join_group_btn");

// create_project_btn.value = await translationText("create");
// delete_project_btn.value = await translationText("delete");


$(document).ready(async function ()  {
    join_group_btn.value = await translationText("join");
    table = initTable('#projects-table',{});
    doubleClickTable(table, '#projects-table', (project_data)=>{
        localStorage.setItem("project_data", JSON.stringify(project_data));
        window.location.href = "../dashboard.html";
    });
    var created_by = account;
    var where = encodeURIComponent(JSON.stringify({ created_by }));
    updateTable(table, ["projects"],["id","name","status","created_by"], `where=${where}`);
} );

create_project_btn.addEventListener('click',async function(event){
    window.location.href = "create.html";
});

delete_project_btn.addEventListener('click', async function(event){ 
    $('#projects-table tbody tr.selected').each(function () {
        var rowData = table.row(this).data();  // 獲取該行的數據
        fetch(linkApiRoot('project/') + rowData[0],
        {
            method: 'DELETE',
            headers:{ Authorization: 'Bearer '+ token }
        }
        )
        .then(handleError)
        .then(response => response.json()) 
        .then(async projectData =>{
            const {status} = await gameStatus();
            if(status == "ok"){
                await fetch('http://localhost:4000/api/project/delete',
                    {
                        method: 'POST',
                        headers: {
                            'Accept': 'application/json',
                            'Content-Type': 'application/json',
                            },
                        body: JSON.stringify({
                            id: rowData[0]
                        })
                    })
            }
            var row = table.row($(this));
            row.remove().draw();  // 刪除該行並重新繪製表格
            console.log("deleted");
        })
        .catch(error => {
            console.error(error);    
        });
    });
});

join_group_btn.addEventListener('click', async function(event){
    var invite_code = prompt("Please enter invite code of the group:") || "";
    var where = encodeURIComponent(JSON.stringify({ invite_code }));
    fetch(linkApiRoot(`memberGroups?where=${where}`),
    {
        headers:{ Authorization: 'Bearer '+ token }
    }
    )
    .then(handleError)
    .then(response => response.json()) 
    .then(groupData =>{
        console.log(groupData); 
        if(groupData.length != 0){
            fetch(linkApiRoot('member'),
            {
                method:"POST",
                headers:{
                    Authorization: 'Bearer '+ token ,
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                },
                body:JSON.stringify({
                    account: account,
                    gid: groupData[0].id
                })
            }
            )
            .then(handleError)
            .then(response => response.json()) 
            .then(groupData =>{
                console.log("group joined");
            })
            .catch(error => {
                console.error(error);
            });
        }
    })
    .catch(error => {
        console.error(error);
    });
})
