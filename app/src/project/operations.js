import {handleError} from '../handle_error.js';
import * as tokenOps from '../user/tokenOps.js';
import {generateHash, generateSalt, verifyHash} from '../utils/crypto.js';
import { linkApiRoot } from '../utils/env.js';
var data = JSON.parse(localStorage.getItem("project_data"));
var token = tokenOps.getToken();

export function listPage(){
    window.location.href = "/html/project/list.html";
}

export function displayerPage(){
    fetch(linkApiRoot(`project/${data[0]}`),
        {
            headers:{ Authorization: 'Bearer '+ token },
        })
        .then(handleError)
        .then(res => res.json())
        .then(projectData =>{
            window.location.href = `../video/displayer.html?hashID=${projectData.hashID}`;
        })
        .catch(e=>{
            console.error(e);
        })  
}

export function getProject(event){
    return fetch(linkApiRoot('project/' + data[0]),
        {
            headers:{ Authorization: 'Bearer '+ token }
        }
        )
        .then(handleError)
        .then(response => response.json());
}

export async function executeProject(expiration){
    var salt = generateSalt();
    var hash = await generateHash(data[0], salt);
    return fetch(linkApiRoot(`project/${data[0]}`),
    {
        method: 'PUT',
        headers:{
        'Content-Type': 'application/json' ,
         Authorization: 'Bearer '+ token },
        body:JSON.stringify({
            status: "OPEN",
            expiration: expiration,
            salt: salt,
            hashID: hash
        })
    }
    )
    .then(handleError)
    .then(res => res.json());
  }
  
export function stopProject(event){
    return fetch(linkApiRoot(`project/${data[0]}`),
    {
        method: 'PUT',
        headers:{
        'Content-Type': 'application/json' ,
         Authorization: 'Bearer '+ token },
        body:JSON.stringify({
            status: "CLOSE",
            expiration: null,
            salt:null,
            hashID:null
        })
    }
    )
    .then(handleError)
    .then(res => res.json());
  }