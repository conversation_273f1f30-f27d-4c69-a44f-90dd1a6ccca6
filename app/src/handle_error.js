import { saveToken } from "./user/tokenOps.js";

export const handleError = async (response) => {
  if (!response.ok) {
    switch (response.status) {
      case 401:
        let result = confirm("登入已過期，請重新登入");
        // 跳轉到登入頁面
        if (result) {
          window.location.href = '../../index.html';
        }
        saveToken("");
        break;

      case 403:
        alert("權限不足，無法執行此操作");
        break;

      case 404:
        alert("請求的資源不存在");
        break;

      case 500:
        alert("伺服器內部錯誤，請稍後再試");
        break;

      default:
        // 處理其他錯誤
        try {
          const errorData = await response.json();
          const errorMessage = errorData.error || errorData.message || `請求失敗 (${response.status})`;
          alert(`錯誤：${errorMessage}`);
        } catch (e) {
          alert(`請求失敗 (${response.status})`);
        }
        break;
    }

    // 對於錯誤情況，拋出異常以中止後續處理
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  } else {
    // 處理成功的請求
    return response;
  }
};