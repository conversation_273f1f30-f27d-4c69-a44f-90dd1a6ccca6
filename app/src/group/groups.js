import * as tokenOps from '../user/tokenOps.js';
import {handleError} from '../handle_error.js';
import { initTable, updateTable, remove, doubleClickTable } from '../table.js';
import {translationText} from "../translation.js";
import { linkApiRoot } from '../utils/env.js';

var account = localStorage.getItem("account");
var token = tokenOps.getToken();
var create_btn = document.getElementById("create_btn");
var delete_btn = document.getElementById("delete_btn");
var leave_btn = document.getElementById("leave_btn");
var inviteCode = document.getElementById("invite-code");
var groupTitles = document.getElementById("groups-title");
var memberTitles = document.getElementById("members-title");
var groupContent = document.getElementById("groups-content");
var memberContent = document.getElementById("members-content");
var data = JSON.parse(localStorage.getItem("project_data"));
var groupId = null;
var tables = {};
var columns = {
    "#groups-table": ["id","name","invite_code"],
    "#members-table": ["id","name"]
};

var alphanumericSet = [
    'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',
    'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z',
    '0', '1', '2', '3', '4', '5', '6', '7', '8', '9'
];

var groupTitleTranslationTexts;
var memberTitleTranslationTexts;



$(document).ready(async function ()  {
    groupTitleTranslationTexts = [await translationText("id"), await translationText("name"), await translationText("invite_code")];
    memberTitleTranslationTexts = [await translationText("id"), await translationText("name")];
    create_btn.textContent = await translationText("create");
    delete_btn.textContent = await translationText("delete");
    leave_btn.textContent = await translationText("leave");
    for(let i=0;i < groupTitles.children.length;i++){
        groupTitles.children.item(i).textContent = groupTitleTranslationTexts[i];
    }
    for(let i=0;i < memberTitles.children.length;i++){
        memberTitles.children.item(i).textContent = memberTitleTranslationTexts[i];
    }
    tables['#groups-table'] = initTable("#groups-table", [
            {
                targets:0,
                visible: false,
                researchable:false
            }
        ]);
    tables['#members-table'] = initTable("#members-table",[
            {
                targets:0,
                visible: false,
                researchable:false
            }
        ]); 
    updateTable(tables['#groups-table'], ["project",data[0],"memberGroups"], columns['#groups-table']);
    doubleClickTable(tables['#groups-table'], '#groups-table',(group_data)=>{
        groupId = group_data[0];
        updateTable(tables["#members-table"], ["memberGroup",groupId,"members"], columns['#members-table']);
        let gid = groupId;
        var where = encodeURIComponent(JSON.stringify({ account, gid }));
        fetch(linkApiRoot(`members?where=${where}`),
        {
            method: 'GET',
            headers:{ Authorization: 'Bearer '+ token }
        }
        )
        .then(handleError)
        .then(response => response.json()) 
        .then(data =>{
           if(data.length > 0){
            let member = data[0];
            leave_btn.addEventListener('click', function handleLeaveEvent(){
                fetch(linkApiRoot(`member/${data[0].id}`),
                    {
                        method: 'DELETE',
                        headers:{ Authorization: 'Bearer '+ token }
                    }
                    )
                    .then(handleError)
                    .then(response => response.json()) 
                    .then(_ =>{
                        console.log("deleted");
                        leave_btn.removeEventListener('click', handleLeaveEvent);
                        tables['#members-table'].clear().draw();
                    })
                    .catch(error => {
                        console.log("remove failed");
                    });
            })
           }
        })
        .catch(error =>{
            console.error(error);
        });
    });
});

create_btn.addEventListener("click", function(){
    createPage();
})

delete_btn.addEventListener("click", function(){
    removeGroup();
})

/**
* Redirects the user to the create group page.
*/
function createPage() {
    window.location.href = "/html/group/create.html";
}

function GenerateInvCode(){
    inviteCode.innerHTML = GetInvCode(10);
    fetch(linkApiRoot(`memberGroup/${groupId}`),
    {
        method: 'PUT',
        headers:{ Authorization: 'Bearer '+ token },
        body:{
            invite_code: inviteCode.innerHTML
        }
    }
    )
    .then(handleError)
    .then(response => response.json()) 
    .then(data =>{
        console.log("invite code: " + data.invite_code);
    })
    .catch(error =>{
        console.error(error);
    })
}

function GetInvCode(l){
    var code = [];
    var randomCode = Math.random().toString().substr(2);
    for (i = 0; i < l; i++ ){
        const idx = BigInt(randomCode) % BigInt(alphanumericSet.length)
        code.push(alphanumericSet[idx]);
        randomCode = BigInt(randomCode) / BigInt(alphanumericSet.length) // 相当于右移一位（62进制）
    }
    return code.join("");
}

function removeGroup(){
    remove(tables['#groups-table'],"#groups-table", 'memberGroup');
}