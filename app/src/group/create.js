import {handleError} from '../handle_error.js';
import { linkApiRoot } from '../utils/env.js';
const alphanumericSet = [
    'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',
    'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z',
    '0', '1', '2', '3', '4', '5', '6', '7', '8', '9'
];
var token = localStorage.getItem("token");
var projectData =  JSON.parse(localStorage.getItem("project_data").toString());
var form = document.getElementById('group_form');
form.addEventListener('submit', async function(event) {
    const formData = new FormData(form);

    event.preventDefault(); // 阻止表單的默認提交行為
    const errorMessage = document.getElementById('error_message');
    // 建立表單數據
    var object = {};
    formData.forEach(function(value, key){
        object[key] = value;
    });
    object['invite_code'] = GetInvCode(10);
    object['project_id'] = projectData[0];
    console.log(object);
    var json = JSON.stringify(object);
    // 使用 Fetch 提交表單
    await fetch(linkApiRoot('memberGroup'), {
        method: 'POST',
        headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        Authorization: 'Bearer '+ token 
        },
        body: json
    })
    .then(handleError)
    .then(response => response.json()) // 將回應轉換成 JSON 格式
    .then(data => {
        window.location.href = "../dashboard.html";
    })
    .catch(error =>{
        errorMessage.innerHTML = "Creating project failed.";
    })
});

// GetInvCodeBy 获取指定长度的邀请码
function GetInvCode(l){
    var code = [];
    var randomCode = Math.random().toString().substr(2);
    for (var i = 0; i < l; i++ ){
        const idx = BigInt(randomCode) % BigInt(alphanumericSet.length)
        console.log(idx)
        code.push(alphanumericSet[idx]);
        randomCode = BigInt(randomCode) / BigInt(alphanumericSet.length) // 相当于右移一位（62进制）
    }
    return code.join("");
}