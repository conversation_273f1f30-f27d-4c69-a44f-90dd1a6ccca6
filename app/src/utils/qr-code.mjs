import QRCode from 'qrcode';
import * as tokenOps from '../user/tokenOps.js';
import {handleError} from '../handle_error.js';
import { linkApiRoot, linkRoot } from './env.js';
var canvas = document.getElementById('qr-container');

var data = JSON.parse(localStorage.getItem("exhibition_data"));

var token = tokenOps.getToken();

fetch(linkApiRoot(`exhibition/${data[0]}`),
  {
      headers:{ Authorization: 'Bearer '+ token },
  })
  .then(handleError)
  .then(res => res.json())
  .then(exhibitionData =>{
    QRCode.toCanvas(canvas, linkRoot("html/video/displayer.html?hashID=") + exhibitionData.hashID), function (error) {
      if (error) console.error(error)
      console.log('QR code generated successfully!');
    }
  })
  .catch(e=>{
      console.error(e);
  })  
