export function setCookie(name, value, days) {
    deleteAllCookies();
    const date = new Date();
    date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000); // Calculate expiration in milliseconds
    const expires = `expires=${date.toUTCString()}`;
    document.cookie = `${name}=${value}; ${expires}; path=/`; // Path=/ ensures the cookie is accessible across the site
  }

export function getCookie(name) {
    const cookies = document.cookie.split('; ');
    for (let i = 0; i < cookies.length; i++) {
      const [key, value] = cookies[i].split('=');
      console.log(document.cookie)
      if (key === name) {
        return value; // Return the value of the matching cookie
      }
    }
    return null; // Return null if the cookie is not found
  }

function deleteAllCookies() {
    document.cookie.split(';').forEach(cookie => {
        const eqPos = cookie.indexOf('=');
        const name = eqPos > -1 ? cookie.substring(0, eqPos) : cookie;
        document.cookie = name + '=;expires=Thu, 01 Jan 1970 00:00:00 GMT';
    });
}