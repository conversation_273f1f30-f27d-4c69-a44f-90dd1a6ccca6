export function generateSalt(length = 16) {
  const array = new Uint8Array(length);
  window.crypto.getRandomValues(array);  // 在浏览器中生成随机盐
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
}
// 将输入和盐结合，进行 SHA-256 哈希
export async function generateHash(input, salt) {
  const encoder = new TextEncoder();
  const inputData = encoder.encode(input); // 编码用户输入
  const saltData = encoder.encode(salt); // salt 現在是 hex 字串，直接編碼

  // 將輸入和盐合併為一個新的字節數組
  const combinedData = new Uint8Array(inputData.length + saltData.length);
  combinedData.set(inputData);
  combinedData.set(saltData, inputData.length);

  // 计算 SHA-256 哈希
  const hashBuffer = await window.crypto.subtle.digest('SHA-256', combinedData);

  // 将结果转换为十六进制字符串
  return bufferToHex(hashBuffer);
}

// 将 ArrayBuffer 转为十六进制字符串
function bufferToHex(buffer) {
  const view = new DataView(buffer);
  let hexStr = '';
  for (let i = 0; i < view.byteLength; i++) {
    const byte = view.getUint8(i);
    hexStr += byte.toString(16).padStart(2, '0');
  }
  return hexStr;
}

// 解密
export async function verifyHash(input, salt, storedHash) {
  const hash = await generateHash(input, salt);
  return hash === storedHash;
}