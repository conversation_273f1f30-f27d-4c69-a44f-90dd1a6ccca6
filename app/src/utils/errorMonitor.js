// 前端錯誤監控工具
class ErrorMonitor {
  constructor() {
    this.errors = [];
    this.setupGlobalErrorHandlers();
  }

  setupGlobalErrorHandlers() {
    // 捕獲 JavaScript 錯誤
    window.addEventListener('error', (event) => {
      this.logError('JavaScript Error', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error
      });
    });

    // 捕獲 Promise 拒絕錯誤
    window.addEventListener('unhandledrejection', (event) => {
      this.logError('Unhandled Promise Rejection', {
        reason: event.reason,
        promise: event.promise
      });
    });

    // 攔截 fetch 錯誤
    this.interceptFetch();
  }

  interceptFetch() {
    const originalFetch = window.fetch;
    window.fetch = async (...args) => {
      try {
        const response = await originalFetch(...args);
        if (!response.ok) {
          this.logError('API Error', {
            url: args[0],
            status: response.status,
            statusText: response.statusText,
            method: args[1]?.method || 'GET'
          });
        }
        return response;
      } catch (error) {
        this.logError('Network Error', {
          url: args[0],
          error: error.message,
          method: args[1]?.method || 'GET'
        });
        throw error;
      }
    };
  }

  logError(type, details) {
    const errorInfo = {
      type,
      details,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    this.errors.push(errorInfo);

    // 在控制台顯示格式化的錯誤
    console.group(`🚨 ${type} - ${errorInfo.timestamp}`);
    console.error('詳細信息:', details);
    console.log('頁面:', errorInfo.url);
    console.groupEnd();

    // 如果需要，可以發送到後端
    this.sendToBackend(errorInfo);
  }

  sendToBackend(errorInfo) {
    // 發送錯誤到後端 (可選)
    if (window.location.hostname !== 'localhost') {
      fetch('/api/v1/errors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(errorInfo)
      }).catch(() => {
        // 靜默處理發送錯誤
      });
    }
  }

  getErrors() {
    return this.errors;
  }

  clearErrors() {
    this.errors = [];
  }

  exportErrors() {
    const errorReport = {
      reportTime: new Date().toISOString(),
      errors: this.errors,
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    const dataStr = JSON.stringify(errorReport, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });

    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `error-report-${Date.now()}.json`;
    link.click();
  }
}

// 初始化錯誤監控
const errorMonitor = new ErrorMonitor();

// 提供全局訪問
window.errorMonitor = errorMonitor;

console.log('🛡️ 錯誤監控已啟用');
console.log('使用 window.errorMonitor.getErrors() 查看所有錯誤');
console.log('使用 window.errorMonitor.exportErrors() 導出錯誤報告'); 