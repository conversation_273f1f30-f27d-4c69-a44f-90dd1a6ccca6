let env = 'dev';

export function linkApiRoot(path){
    if(env == 'dev'){
        return 'http://localhost:3062/api/v1/' + path;
    }else if (env == 'prod'){
        return 'https://iamm-cdn.tianyen-service.com/api/v1/' + path;
    }else{
        throw Error("env set error.");
    }
}

export function linkRoot(path){
    if(env == 'dev'){
        return 'http://localhost:3062/' + path;
    }else if (env == 'prod'){
        return 'https://iamm-cdn.tianyen-service.com/' + path;
    }else{
        throw Error("env set error.");
    }
}

export function linkGameApiRoot(path){
    if(env == 'dev'){
        return 'http://localhost:3334/http://localhost:4000/api/' + path;
    }else if(env == 'prod'){
        return 'http://localhost:3334/http://localhost:4000/api/' + path;
    }else{
        throw Error("env set error.");
    }
}