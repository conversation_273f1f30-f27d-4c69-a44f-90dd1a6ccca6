import { translationText } from "../translation.js";

/*
This function loads content from the specified page using fetch, then updates the content section on the webpage with the fetched data. 
It clears existing scripts, creates new script elements based on the content of the fetched scripts, and appends them to the body for execution.
*/
export function loadContent(page) {
    fetch(page)
      .then(response => response.text())
      .then(async data => {
        clearExistingScripts();
        var contentSection = document.getElementById('content-section');
        //var title = document.getElementById('projectName');
        //title.textContent = await translationText((page.split(".html")[0]).split("./")[1]);
        contentSection.innerHTML = data;
  
        // 找到並執行所有的 <script> 標籤
        const scripts = contentSection.querySelectorAll('script');
        scripts.forEach(script => {
          const newScript = document.createElement('script');
          newScript.setAttribute('data-loaded', 'true');
          if(script.type == 'module')
            newScript.setAttribute('type','module');
          if (script.src) {
            newScript.src = script.src+ '?t=' + new Date().getTime();  // 如果有 src 屬性，則加載外部腳本, 使用時間戳作為查詢參數來避免緩存
          } else {
            newScript.textContent = script.textContent;  // 如果是內聯腳本，則直接執行
          }
          document.body.appendChild(newScript);  // 加入到 body 中，這樣可以執行
        });
      })
      .catch(error => {
        console.error('Error loading content:', error);
        document.getElementById('content-section').innerHTML = '<p>Error loading content.</p>';
      });
  }
  
function clearExistingScripts() {
  const existingScripts = document.querySelectorAll('script[data-loaded="true"]');
  existingScripts.forEach(script => {
      script.remove();
  });
  }