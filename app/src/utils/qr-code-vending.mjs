import QRCode from 'qrcode';
import * as tokenOps from '../user/tokenOps.js';
import {handleError} from '../handle_error.js';
import { linkApiRoot } from './env.js';

var canvas = document.getElementById('qr-vending-controller-container');

var canvas2 = document.getElementById('qr-vending-displayer-container');

var data = JSON.parse(localStorage.getItem("exhibition_data"));

var token = tokenOps.getToken();

fetch(linkApiRoot(`exhibition/${data[0]}`),
  {
      headers:{ Authorization: 'Bearer '+ token },
  })
  .then(handleError)
  .then(res => res.json())
  .then(exhibitionData =>{
    QRCode.toCanvas(canvas, "https://tianyen-service.com:4007/vending_controller?openExternalBrowser=1&channel_id=" + exhibitionData.hashID), function (error) {
      if (error) console.error(error)
    }
    QRCode.toCanvas(canvas2, "https://tianyen-service.com:4008/vending_displayer?channel_id=" + exhibitionData.hashID), function (error) {
      if (error) console.error(error)
    }
  })
  .catch(e=>{
      console.error(e);
  })  
