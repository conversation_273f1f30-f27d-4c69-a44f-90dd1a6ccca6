# IAMM - 廣告定時投放系統

## 🚀 快速啟動

### Docker 部署（推薦）
```bash
# 啟動所有服務
npm run docker:start

# 停止所有服務
npm run docker:stop
```

### 訪問地址
- **前端應用**: http://localhost:5173
- **後端 API**: http://localhost:3062
- **健康檢查**: http://localhost:3062/health
- **MySQL 數據庫**: localhost:3306

## 🏗️ 系統架構

```
Frontend (Port 5173) → HTTP → Backend (Port 3062) → MySQL (Port 3306)
```

### 服務端口配置
| 服務 | 容器名稱 | 端口 | 說明 |
|------|----------|------|------|
| 前端應用 | iamm-app | 5173 | Vite 開發服務器 |
| 後端 API | iamm-backend | 3062 | Express + Prisma + Socket.IO |
| MySQL 數據庫 | iamm-mysql | 3306 | 數據存儲 |

## 目前製作進度

- 帳戶管理
- 專案管理
- 廣告排程
- 群組管理

### 帳戶管理製作進度

- 註冊 
- 登入 

### 專案管理製作進度

- 專案管理頁面 
- 專案每日定時啟動關閉
- 專案啟動並指定時間關閉

### 廣告排程製作進度

- 廣告排程頁面 (進行中)
- 廣告上傳
- 廣告刪除
- 廣告排程 API (進行中)

### 群組管理製作進度

- 群組管理頁面
- 創建 
- 刪除 
- 加入

## 🛠️ 技術棧

### 前端 (iamm-app)
- **框架**: Vite + JavaScript
- **端口**: 5173
- **容器**: `iamm-app`

### 後端 (iamm-backend)
- **框架**: Node.js + Express + TypeScript
- **數據庫**: Prisma ORM + MySQL
- **實時通信**: Socket.IO
- **認證**: JWT
- **端口**: 3062
- **容器**: `iamm-backend`

### 數據庫
- **類型**: MySQL 8.0
- **端口**: 3306
- **容器**: `iamm-mysql`

## 📝 開發說明

### API 端點
- 基礎 URL: `http://localhost:3062/api/v1/`
- 健康檢查: `http://localhost:3062/health`
- WebSocket: `http://localhost:3062/socket.io/`

### 環境變量
```env
NODE_ENV=development
DATABASE_URL=mysql://root:password@mysql:3306/iamm
JWT_SECRET=IAMMSecretPassword
```

### 已暫時停用的功能
- Video 上傳/管理相關 API
- Event 註冊/簽到/遊戲相關 API

```sql
## Database
CREATE TABLE User(
    account varchar(255) NOT NULL,
    password varchar(255) NOT NULL,
    name varchar(255),
    phone int,
    email varchar(255),
    PRIMARY KEY(account)
);
CREATE TABLE Projects(
    id int NOT NULL auto_increment,
    name varchar(255) NOT NULL,
    time_start DATETIME,
    time_end DATETIME,
    PRIMARY KEY(id)
);

CREATE TABLE MemberGroup(
    id int NOT NULL auto_increment,
    name varchar(255),
    manager varchar(255),
    project_id int NOT NULL,
    PRIMARY KEY(id)
);

CREATE TABLE Permission(
    id int NOT NULL auto_increment,
    account varchar(255) NOT NULL,
    pid int NOT NULL,
    create_proj tinyint(1) NOT NULL DEFAULT 1,
    update_proj tinyint(1) NOT NULL DEFAULT 1,
    read_proj tinyint(1) NOT NULL DEFAULT 1,
    delete_proj tinyint(1) NOT NULL DEFAULT 1,
    PRIMARY KEY(id)
);
CREATE TABLE Member(
    id int NOT NULL auto_increment,
    account varchar(255) NOT NULL,
    gid int NOT NULL,
    PRIMARY KEY(id)
);
CREATE TABLE Media(
    id int NOT NULL auto_increment,
    account varchar(255) NOT NULL,
    pid int NOT NULL,
    url varchar(255) NOT NULL,
    PRIMARY KEY(id)
);
```

## 🔧 常用命令

### Docker 管理
```bash
# 查看容器狀態
docker ps --filter "name=iamm-"

# 查看容器日誌
docker logs iamm-app --tail 20
docker logs iamm-backend --tail 20
docker logs iamm-mysql --tail 20

# 重啟特定服務
docker restart iamm-app
docker restart iamm-backend
docker restart iamm-mysql
```

### 數據庫管理
```bash
# 生成 Prisma 客戶端
npx prisma generate

# 數據庫遷移
npx prisma migrate dev

# 查看數據庫
npx prisma studio
```

### 開發調試
```bash
# 檢查健康狀態
curl http://localhost:3062/health

# 檢查端口使用情況
lsof -i :3062  # 後端
lsof -i :5173  # 前端
lsof -i :3306  # MySQL

# 停止所有端口
npm run kill:all
```

## 📚 相關文檔

- [Docker 部署指南](./DOCKER_SETUP.md)
- [部署指南](./DEPLOYMENT_GUIDE.md)

## 🐛 故障排除

### 常見問題

1. **Prisma 客戶端錯誤**
   ```bash
   docker exec iamm-backend npx prisma generate
   ```

2. **端口被佔用**
   ```bash
   npm run kill:all
   ```

3. **容器無法啟動**
   ```bash
   docker-compose -f docker-compose.app.yml down
   docker-compose -f docker-compose.app.yml up -d
   ```
