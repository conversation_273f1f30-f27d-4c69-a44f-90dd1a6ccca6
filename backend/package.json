{"name": "iamm-backend", "version": "1.0.0", "description": "IAMM Backend API Server", "main": "dist/index.js", "scripts": {"dev": "nodemon --exec ts-node src/index.ts", "build": "tsc", "start": "node dist/index.js", "docker:dev": "nodemon --exec ts-node src/index.ts", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "dependencies": {"@prisma/client": "^6.11.1", "aws-sdk": "^2.1692.0", "bcrypt": "^6.0.0", "body-parser": "^1.20.3", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.21.0", "express-jwt": "^8.5.1", "http-proxy-middleware": "^3.0.5", "jsonwebtoken": "^9.0.2", "qrcode": "^1.5.4", "socket.io": "^4.8.0", "uuid": "^10.0.0", "vite": "^7.0.2", "vite-express": "^0.21.1", "winston": "^3.17.0"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/express": "^5.0.0", "@types/jest": "^29.5.0", "@types/jsonwebtoken": "^9.0.0", "@types/node": "^20.0.0", "@types/qrcode": "^1.5.5", "@types/uuid": "^10.0.0", "jest": "^29.7.0", "nodemon": "^3.1.7", "prisma": "^6.11.1", "ts-node": "^10.9.2", "typescript": "^5.6.3"}, "prisma": {"schema": "../prisma/schema.prisma"}}