import { Server as SocketIOServer } from 'socket.io';
import { Server as HTTPServer } from 'http';
import { PrismaClient } from '@prisma/client';
import { logger } from '../winston/logger';

const prisma = new PrismaClient({
  log: ['info', 'warn', 'error'],
});

interface GameRoom {
  id: string;
  name: string;
  exhibitionId: number;
  players: Map<string, PlayerInfo>;
  gameState: GameState;
  createdAt: Date;
}

interface PlayerInfo {
  socketId: string;
  registrationId: number;
  userName: string;
  qrCode: string;
  joinedAt: Date;
  isPlaying: boolean;
}

interface GameState {
  status: 'waiting' | 'playing' | 'finished';
  currentPlayer?: string;
  scores: Map<string, number>;
  gameStartTime?: Date;
  gameEndTime?: Date;
}

interface DartThrow {
  playerId: string;
  score: number;
  sector: number;
  multiplier: number;
  timestamp: Date;
}

class GameSocketManager {
  private io: SocketIOServer;
  private gameRooms: Map<string, GameRoom> = new Map();
  
  constructor(server: HTTPServer) {
    this.io = new SocketIOServer(server, {
      cors: {
        origin: "*",
        methods: ["GET", "POST"]
      },
      path: '/socket.io'
    });
    
    this.setupSocketHandlers();
    this.startRoomCleanup();
  }

  private setupSocketHandlers() {
    this.io.on('connection', (socket) => {
      logger.info(`Socket 連接: ${socket.id}`);

      // 加入房間
      socket.on('join-room', async (data) => {
        await this.handleJoinRoom(socket, data);
      });

      // 離開房間
      socket.on('leave-room', (data) => {
        this.handleLeaveRoom(socket, data);
      });

      // 開始遊戲
      socket.on('start-game', (data) => {
        this.handleStartGame(socket, data);
      });

      // 飛鏢投擲
      socket.on('dart-throw', async (data) => {
        await this.handleDartThrow(socket, data);
      });

      // 結束遊戲
      socket.on('end-game', async (data) => {
        await this.handleEndGame(socket, data);
      });

      // 獲取房間狀態
      socket.on('get-room-state', (data) => {
        this.handleGetRoomState(socket, data);
      });

      // 獲取排行榜
      socket.on('get-leaderboard', async (data) => {
        await this.handleGetLeaderboard(socket, data);
      });

      // 斷線處理
      socket.on('disconnect', () => {
        this.handleDisconnect(socket);
      });
    });
  }

  private async handleJoinRoom(socket: any, data: { roomId: string; qrCode: string }) {
    try {
      const { roomId, qrCode } = data;

      // 驗證 QR Code 和獲取用戶資訊
      const registration = await prisma.eventRegistration.findUnique({
        where: { qrCode },
        include: { exhibition: true }
      });

      if (!registration) {
        socket.emit('error', { message: 'QR Code 無效' });
        return;
      }

      // 檢查展覽是否開放
      if (registration.exhibition.status !== 'OPEN') {
        socket.emit('error', { message: '活動尚未開始或已結束' });
        return;
      }

      // 獲取或創建房間
      let room = this.gameRooms.get(roomId);
      if (!room) {
        room = await this.createGameRoom(roomId, registration.exhibitionId);
      }

      // 檢查房間是否已滿
      if (room.players.size >= 4) { // 假設最多4人
        socket.emit('error', { message: '房間已滿' });
        return;
      }

      // 加入房間
      const playerInfo: PlayerInfo = {
        socketId: socket.id,
        registrationId: registration.id,
        userName: registration.userName,
        qrCode,
        joinedAt: new Date(),
        isPlaying: false
      };

      room.players.set(socket.id, playerInfo);
      socket.join(roomId);

      // 更新資料庫中的房間玩家數
      await this.updateRoomPlayerCount(roomId, room.players.size);

      // 通知房間內所有玩家
      this.io.to(roomId).emit('player-joined', {
        player: {
          socketId: socket.id,
          userName: registration.userName,
          joinedAt: playerInfo.joinedAt
        },
        roomState: this.getRoomStateData(room)
      });

      // 回應加入成功
      socket.emit('join-success', {
        roomId,
        playerInfo: {
          socketId: socket.id,
          userName: registration.userName
        },
        roomState: this.getRoomStateData(room)
      });

      logger.info(`玩家 ${registration.userName} 加入房間 ${roomId}`);

    } catch (error) {
      logger.error('加入房間失敗:', error);
      socket.emit('error', { message: '加入房間失敗' });
    }
  }

  private handleLeaveRoom(socket: any, data: { roomId: string }) {
    const { roomId } = data;
    const room = this.gameRooms.get(roomId);
    
    if (room && room.players.has(socket.id)) {
      const player = room.players.get(socket.id);
      room.players.delete(socket.id);
      socket.leave(roomId);

      // 更新資料庫中的房間玩家數
      this.updateRoomPlayerCount(roomId, room.players.size);

      // 通知其他玩家
      this.io.to(roomId).emit('player-left', {
        player: {
          socketId: socket.id,
          userName: player?.userName
        },
        roomState: this.getRoomStateData(room)
      });

      logger.info(`玩家 ${player?.userName} 離開房間 ${roomId}`);

      // 如果房間空了，清理房間
      if (room.players.size === 0) {
        this.gameRooms.delete(roomId);
      }
    }
  }

  private handleStartGame(socket: any, data: { roomId: string }) {
    const { roomId } = data;
    const room = this.gameRooms.get(roomId);
    
    if (!room || !room.players.has(socket.id)) {
      socket.emit('error', { message: '房間不存在或您不在房間中' });
      return;
    }

    if (room.gameState.status === 'playing') {
      socket.emit('error', { message: '遊戲已在進行中' });
      return;
    }

    // 開始遊戲
    room.gameState.status = 'playing';
    room.gameState.gameStartTime = new Date();
    room.gameState.scores.clear();
    
    // 設置所有玩家為遊戲中
    room.players.forEach(player => {
      player.isPlaying = true;
      room.gameState.scores.set(player.socketId, 0);
    });

    // 通知所有玩家遊戲開始
    this.io.to(roomId).emit('game-started', {
      roomState: this.getRoomStateData(room),
      startTime: room.gameState.gameStartTime
    });

    logger.info(`房間 ${roomId} 遊戲開始`);
  }

  private async handleDartThrow(socket: any, data: DartThrow & { roomId: string }) {
    try {
      const { roomId, score, sector, multiplier } = data;
      const room = this.gameRooms.get(roomId);
      
      if (!room || !room.players.has(socket.id)) {
        socket.emit('error', { message: '房間不存在或您不在房間中' });
        return;
      }

      if (room.gameState.status !== 'playing') {
        socket.emit('error', { message: '遊戲尚未開始' });
        return;
      }

      const player = room.players.get(socket.id);
      if (!player) return;

      // 更新分數
      const currentScore = room.gameState.scores.get(socket.id) || 0;
      const newScore = currentScore + score;
      room.gameState.scores.set(socket.id, newScore);

      // 記錄到資料庫
      await prisma.gameScore.create({
        data: {
          registrationId: player.registrationId,
          roomId,
          gameType: 'DART',
          score: newScore,
          playTime: room.gameState.gameStartTime ? 
            Math.floor((Date.now() - room.gameState.gameStartTime.getTime()) / 1000) : 0,
          isCompleted: false
        }
      });

      // 通知所有玩家
      this.io.to(roomId).emit('dart-thrown', {
        player: {
          socketId: socket.id,
          userName: player.userName
        },
        throw: {
          score,
          sector,
          multiplier,
          totalScore: newScore
        },
        roomState: this.getRoomStateData(room)
      });

      logger.info(`玩家 ${player.userName} 投擲飛鏢，得分: ${score}，總分: ${newScore}`);

    } catch (error) {
      logger.error('處理飛鏢投擲失敗:', error);
      socket.emit('error', { message: '記錄分數失敗' });
    }
  }

  private async handleEndGame(socket: any, data: { roomId: string; finalScore?: number }) {
    try {
      const { roomId, finalScore } = data;
      const room = this.gameRooms.get(roomId);

      if (!room || !room.players.has(socket.id)) {
        socket.emit('error', { message: '房間不存在或您不在房間中' });
        return;
      }

      const player = room.players.get(socket.id);
      if (!player) return;

      // 結束遊戲
      room.gameState.status = 'finished';
      room.gameState.gameEndTime = new Date();

      const gameTime = room.gameState.gameStartTime ?
        Math.floor((room.gameState.gameEndTime.getTime() - room.gameState.gameStartTime.getTime()) / 1000) : 0;

      const playerFinalScore = finalScore || room.gameState.scores.get(socket.id) || 0;

      // 更新資料庫記錄為完成
      await prisma.gameScore.updateMany({
        where: {
          registrationId: player.registrationId,
          roomId,
          isCompleted: false
        },
        data: {
          score: playerFinalScore,
          playTime: gameTime,
          isCompleted: true
        }
      });

      // 更新報名狀態為已完成
      await prisma.eventRegistration.update({
        where: { id: player.registrationId },
        data: { status: 'COMPLETED' }
      });

      // 通知所有玩家遊戲結束
      this.io.to(roomId).emit('game-ended', {
        player: {
          socketId: socket.id,
          userName: player.userName
        },
        finalScore: playerFinalScore,
        gameTime,
        roomState: this.getRoomStateData(room)
      });

      logger.info(`玩家 ${player.userName} 完成遊戲，最終分數: ${playerFinalScore}`);

    } catch (error) {
      logger.error('結束遊戲失敗:', error);
      socket.emit('error', { message: '結束遊戲失敗' });
    }
  }

  private handleGetRoomState(socket: any, data: { roomId: string }) {
    const { roomId } = data;
    const room = this.gameRooms.get(roomId);

    if (!room) {
      socket.emit('error', { message: '房間不存在' });
      return;
    }

    socket.emit('room-state', this.getRoomStateData(room));
  }

  private async handleGetLeaderboard(socket: any, data: { roomId: string; limit?: number }) {
    try {
      const { roomId, limit = 10 } = data;

      const topScores = await prisma.gameScore.findMany({
        where: {
          roomId,
          isCompleted: true
        },
        include: {
          registration: {
            select: {
              userName: true
            }
          }
        },
        orderBy: { score: 'desc' },
        take: limit
      });

      const leaderboard = topScores.map((score, index) => ({
        rank: index + 1,
        userName: score.registration.userName,
        score: score.score,
        playTime: score.playTime,
        playedAt: score.playedAt
      }));

      socket.emit('leaderboard', { roomId, leaderboard });

    } catch (error) {
      logger.error('獲取排行榜失敗:', error);
      socket.emit('error', { message: '獲取排行榜失敗' });
    }
  }

  private handleDisconnect(socket: any) {
    // 查找玩家所在的房間並移除
    for (const [roomId, room] of this.gameRooms.entries()) {
      if (room.players.has(socket.id)) {
        this.handleLeaveRoom(socket, { roomId });
        break;
      }
    }

    logger.info(`Socket 斷線: ${socket.id}`);
  }

  private async createGameRoom(roomId: string, exhibitionId: number): Promise<GameRoom> {
    // 嘗試從資料庫獲取房間資訊
    let dbRoom = await prisma.gameRoom.findUnique({
      where: { id: roomId }
    });

    // 如果資料庫中沒有，創建新房間
    if (!dbRoom) {
      dbRoom = await prisma.gameRoom.create({
        data: {
          id: roomId,
          name: `房間 ${roomId}`,
          exhibitionId,
          gameType: 'DART',
          status: 'ACTIVE'
        }
      });
    }

    const room: GameRoom = {
      id: roomId,
      name: dbRoom.name,
      exhibitionId: dbRoom.exhibitionId,
      players: new Map(),
      gameState: {
        status: 'waiting',
        scores: new Map()
      },
      createdAt: new Date()
    };

    this.gameRooms.set(roomId, room);
    return room;
  }

  private async updateRoomPlayerCount(roomId: string, playerCount: number) {
    try {
      await prisma.gameRoom.update({
        where: { id: roomId },
        data: { currentPlayers: playerCount }
      });
    } catch (error) {
      logger.error('更新房間玩家數失敗:', error);
    }
  }

  private getRoomStateData(room: GameRoom) {
    const players = Array.from(room.players.values()).map(player => ({
      socketId: player.socketId,
      userName: player.userName,
      isPlaying: player.isPlaying,
      score: room.gameState.scores.get(player.socketId) || 0
    }));

    return {
      roomId: room.id,
      roomName: room.name,
      playerCount: room.players.size,
      players,
      gameStatus: room.gameState.status,
      gameStartTime: room.gameState.gameStartTime,
      gameEndTime: room.gameState.gameEndTime
    };
  }

  private startRoomCleanup() {
    // 每5分鐘清理空房間
    setInterval(() => {
      const now = Date.now();
      for (const [roomId, room] of this.gameRooms.entries()) {
        // 如果房間空了超過5分鐘，清理它
        if (room.players.size === 0 &&
            now - room.createdAt.getTime() > 5 * 60 * 1000) {
          this.gameRooms.delete(roomId);
          logger.info(`清理空房間: ${roomId}`);
        }
      }
    }, 5 * 60 * 1000);
  }

  // 公開方法供外部使用
  public getRoomCount(): number {
    return this.gameRooms.size;
  }

  public getTotalPlayers(): number {
    let total = 0;
    for (const room of this.gameRooms.values()) {
      total += room.players.size;
    }
    return total;
  }

  public getRoomInfo(roomId: string) {
    const room = this.gameRooms.get(roomId);
    return room ? this.getRoomStateData(room) : null;
  }
}

export default GameSocketManager;
