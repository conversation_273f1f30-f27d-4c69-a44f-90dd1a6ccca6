import { expressjwt, TokenGetter,Request } from "express-jwt";
import {RequestHandler} from 'express';

const getTokenFromHeaders: TokenGetter = (req:Request) => {
  if (
    (req.headers.authorization && req.headers.authorization.split(' ')[0] === 'Token') ||
    (req.headers.authorization && req.headers.authorization.split(' ')[0] === 'Bearer')
  ) {
    return req.headers.authorization.split(' ')[1];
  }
  return undefined;
};
// The provided code is using the `express-jwt` middleware to authenticate incoming requests. Here's a breakdown of the code and some suggestions for improvement:
export const auth = {
  required: expressjwt({
    secret: process.env.JWT_SECRET || 'superSecret',
    getToken: getTokenFromHeaders,
    algorithms: ['HS256'],
    requestProperty: 'auth', 
  }) as RequestHandler,
  
  optional: expressjwt({
    secret: process.env.JWT_SECRET || 'superSecret',
    credentialsRequired: false,
    getToken: getTokenFromHeaders,
    algorithms: ['HS256'],
    requestProperty: 'auth', 
  }) as RequestHandler,
};