import {Request,Response} from "express";
import {Role} from '../constants/role';
import {Action} from '../constants/action';
import { prisma } from "../controllers/member.controller";


const roles: { [key in Role]: { [key in Action]: boolean } } = {
    [Role.SUPER_ADMIN]: {
      [Action.CREATE_PROJECT]: true,
      [Action.MODIFY_PROJECT]: true,
      [Action.DELETE_PROJECT]: true,
      [Action.VIEW_PROJECT]: true,
      [Action.VIEW_STATISTICS] : true,
      [Action.SYSTEM_SETTINGS] : true,
      [Action.VIEW_OPRATION_LOGS] : true,
      [Action.CREATE_USER] : true,
      [Action.MODIFY_USER] : true,
      [Action.VIEW_USER] : true,
      [Action.DELETE_USER] : true,
      [Action.LIST_USERS] : true
    },
    [Role.ADMIN]: {
      [Action.CREATE_PROJECT]: true,
      [Action.MODIFY_PROJECT]: true,
      [Action.DELETE_PROJECT]: true,
      [Action.VIEW_PROJECT]: true,
      [Action.VIEW_STATISTICS] : false,
      [Action.SYSTEM_SETTINGS] : false,
      [Action.VIEW_OPRATION_LOGS] : false,
      [Action.CREATE_USER] : false,
      [Action.MODIFY_USER] : true,
      [Action.VIEW_USER] : true,
      [Action.DELETE_USER] : false,
      [Action.LIST_USERS] : false
    },
    [Role.USER]: {
      [Action.CREATE_PROJECT]: false,
      [Action.MODIFY_PROJECT]: true,
      [Action.DELETE_PROJECT]: false,
      [Action.VIEW_PROJECT]: true,
      [Action.VIEW_STATISTICS] : false,
      [Action.SYSTEM_SETTINGS] : false,
      [Action.VIEW_OPRATION_LOGS] : false,
      [Action.CREATE_USER] : false,
      [Action.MODIFY_USER] : true,
      [Action.VIEW_USER] : true,
      [Action.DELETE_USER] : false,
      [Action.LIST_USERS] : false
    }
  }

export function checkPermission(action: Action){
  return async (req:Request, res:Response, next: () => any) =>{
    const user = await prisma.user.findUnique({
      where:{
        account: req.auth!.username
      },
      select:{
        role: true
      }
    })
    
    if (roles[user!.role as Role][action]) {
      return next();
    } else {
      return res.status(403).send('Forbidden');
    }
  }
}