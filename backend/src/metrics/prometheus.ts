import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { logger } from '../winston/logger';

const prisma = new PrismaClient({
  log: ['info', 'warn', 'error'],
});

interface MetricData {
  name: string;
  help: string;
  type: 'counter' | 'gauge' | 'histogram';
  value: number;
  labels?: Record<string, string>;
}

class PrometheusMetrics {
  private metrics: MetricData[] = [];

  // 清空 metrics
  private clearMetrics() {
    this.metrics = [];
  }

  // 添加 metric
  private addMetric(metric: MetricData) {
    this.metrics.push(metric);
  }

  // 格式化 labels
  private formatLabels(labels?: Record<string, string>): string {
    if (!labels || Object.keys(labels).length === 0) {
      return '';
    }
    
    const labelPairs = Object.entries(labels)
      .map(([key, value]) => `${key}="${value}"`)
      .join(',');
    
    return `{${labelPairs}}`;
  }

  // 生成 Prometheus 格式的輸出
  private generatePrometheusOutput(): string {
    const output: string[] = [];
    
    // 按 metric 名稱分組
    const groupedMetrics = this.metrics.reduce((acc, metric) => {
      if (!acc[metric.name]) {
        acc[metric.name] = [];
      }
      acc[metric.name].push(metric);
      return acc;
    }, {} as Record<string, MetricData[]>);

    // 為每個 metric 生成輸出
    Object.entries(groupedMetrics).forEach(([name, metrics]) => {
      const firstMetric = metrics[0];
      
      // 添加 HELP 和 TYPE
      output.push(`# HELP ${name} ${firstMetric.help}`);
      output.push(`# TYPE ${name} ${firstMetric.type}`);
      
      // 添加所有值
      metrics.forEach(metric => {
        const labels = this.formatLabels(metric.labels);
        output.push(`${name}${labels} ${metric.value}`);
      });
      
      output.push(''); // 空行分隔
    });

    return output.join('\n');
  }

  // 收集所有 metrics
  async collectMetrics(exhibitionId?: number): Promise<string> {
    this.clearMetrics();

    try {
      await Promise.all([
        this.collectRegistrationMetrics(exhibitionId),
        this.collectCheckInMetrics(exhibitionId),
        this.collectGameMetrics(exhibitionId),
        this.collectRoomMetrics(exhibitionId)
      ]);

      return this.generatePrometheusOutput();

    } catch (error) {
      logger.error('收集 metrics 失敗:', error);
      return '# Error collecting metrics\n';
    }
  }

  // 收集報名相關 metrics
  private async collectRegistrationMetrics(exhibitionId?: number) {
    const whereCondition = exhibitionId ? { exhibitionId } : {};

    // 總報名數
    const totalRegistrations = await prisma.eventRegistration.count({
      where: whereCondition
    });

    this.addMetric({
      name: 'event_registrations_total',
      help: 'Total number of event registrations',
      type: 'counter',
      value: totalRegistrations,
      labels: exhibitionId ? { exhibition_id: exhibitionId.toString() } : undefined
    });

    // 按狀態分組的報名數
    const registrationsByStatus = await prisma.eventRegistration.groupBy({
      by: ['status'],
      where: whereCondition,
      _count: { id: true }
    });

    registrationsByStatus.forEach(group => {
      this.addMetric({
        name: 'event_registrations_by_status',
        help: 'Number of registrations by status',
        type: 'gauge',
        value: group._count.id,
        labels: {
          status: group.status.toLowerCase(),
          ...(exhibitionId ? { exhibition_id: exhibitionId.toString() } : {})
        }
      });
    });

    // 今日報名數
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const todayRegistrations = await prisma.eventRegistration.count({
      where: {
        ...whereCondition,
        registeredAt: {
          gte: today,
          lt: tomorrow
        }
      }
    });

    this.addMetric({
      name: 'event_registrations_today',
      help: 'Number of registrations today',
      type: 'gauge',
      value: todayRegistrations,
      labels: exhibitionId ? { exhibition_id: exhibitionId.toString() } : undefined
    });
  }

  // 收集簽到相關 metrics
  private async collectCheckInMetrics(exhibitionId?: number) {
    const whereCondition = exhibitionId ? {
      registration: { exhibitionId }
    } : {};

    // 總簽到數
    const totalCheckIns = await prisma.checkIn.count({
      where: whereCondition
    });

    this.addMetric({
      name: 'event_checkins_total',
      help: 'Total number of check-ins',
      type: 'counter',
      value: totalCheckIns,
      labels: exhibitionId ? { exhibition_id: exhibitionId.toString() } : undefined
    });

    // 按房間分組的簽到數
    const checkInsByRoom = await prisma.checkIn.groupBy({
      by: ['roomId', 'roomName'],
      where: whereCondition,
      _count: { id: true }
    });

    checkInsByRoom.forEach(group => {
      this.addMetric({
        name: 'event_room_checkins_total',
        help: 'Number of check-ins by room',
        type: 'counter',
        value: group._count.id,
        labels: {
          room_id: group.roomId,
          room_name: group.roomName,
          ...(exhibitionId ? { exhibition_id: exhibitionId.toString() } : {})
        }
      });
    });

    // 今日簽到數
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const todayCheckIns = await prisma.checkIn.count({
      where: {
        ...whereCondition,
        checkInTime: {
          gte: today,
          lt: tomorrow
        }
      }
    });

    this.addMetric({
      name: 'event_checkins_today',
      help: 'Number of check-ins today',
      type: 'gauge',
      value: todayCheckIns,
      labels: exhibitionId ? { exhibition_id: exhibitionId.toString() } : undefined
    });
  }

  // 收集遊戲相關 metrics
  private async collectGameMetrics(exhibitionId?: number) {
    const whereCondition = exhibitionId ? {
      registration: { exhibitionId }
    } : {};

    // 總遊戲數
    const totalGames = await prisma.gameScore.count({
      where: whereCondition
    });

    this.addMetric({
      name: 'event_games_total',
      help: 'Total number of games played',
      type: 'counter',
      value: totalGames,
      labels: exhibitionId ? { exhibition_id: exhibitionId.toString() } : undefined
    });

    // 完成的遊戲數
    const completedGames = await prisma.gameScore.count({
      where: {
        ...whereCondition,
        isCompleted: true
      }
    });

    this.addMetric({
      name: 'event_games_completed_total',
      help: 'Total number of completed games',
      type: 'counter',
      value: completedGames,
      labels: exhibitionId ? { exhibition_id: exhibitionId.toString() } : undefined
    });

    // 按房間分組的遊戲數
    const gamesByRoom = await prisma.gameScore.groupBy({
      by: ['roomId'],
      where: whereCondition,
      _count: { id: true },
      _avg: { score: true },
      _max: { score: true }
    });

    gamesByRoom.forEach(group => {
      this.addMetric({
        name: 'event_room_games_total',
        help: 'Number of games by room',
        type: 'counter',
        value: group._count.id,
        labels: {
          room_id: group.roomId,
          ...(exhibitionId ? { exhibition_id: exhibitionId.toString() } : {})
        }
      });

      this.addMetric({
        name: 'event_room_score_average',
        help: 'Average score by room',
        type: 'gauge',
        value: group._avg.score || 0,
        labels: {
          room_id: group.roomId,
          ...(exhibitionId ? { exhibition_id: exhibitionId.toString() } : {})
        }
      });

      this.addMetric({
        name: 'event_room_score_max',
        help: 'Maximum score by room',
        type: 'gauge',
        value: group._max.score || 0,
        labels: {
          room_id: group.roomId,
          ...(exhibitionId ? { exhibition_id: exhibitionId.toString() } : {})
        }
      });
    });
  }

  // 收集房間相關 metrics
  private async collectRoomMetrics(exhibitionId?: number) {
    const whereCondition = exhibitionId ? { exhibitionId } : {};

    // 房間狀態統計
    const roomsByStatus = await prisma.gameRoom.groupBy({
      by: ['status'],
      where: whereCondition,
      _count: { id: true }
    });

    roomsByStatus.forEach(group => {
      this.addMetric({
        name: 'event_rooms_by_status',
        help: 'Number of rooms by status',
        type: 'gauge',
        value: group._count.id,
        labels: {
          status: group.status.toLowerCase(),
          ...(exhibitionId ? { exhibition_id: exhibitionId.toString() } : {})
        }
      });
    });

    // 活躍房間數
    const activeRooms = await prisma.gameRoom.count({
      where: {
        ...whereCondition,
        status: 'ACTIVE'
      }
    });

    this.addMetric({
      name: 'event_rooms_active',
      help: 'Number of active rooms',
      type: 'gauge',
      value: activeRooms,
      labels: exhibitionId ? { exhibition_id: exhibitionId.toString() } : undefined
    });

    // 每個房間的當前玩家數
    const rooms = await prisma.gameRoom.findMany({
      where: whereCondition,
      select: {
        id: true,
        name: true,
        currentPlayers: true,
        maxPlayers: true
      }
    });

    rooms.forEach(room => {
      this.addMetric({
        name: 'event_room_players_current',
        help: 'Current number of players in room',
        type: 'gauge',
        value: room.currentPlayers,
        labels: {
          room_id: room.id,
          room_name: room.name,
          ...(exhibitionId ? { exhibition_id: exhibitionId.toString() } : {})
        }
      });

      this.addMetric({
        name: 'event_room_players_max',
        help: 'Maximum number of players in room',
        type: 'gauge',
        value: room.maxPlayers,
        labels: {
          room_id: room.id,
          room_name: room.name,
          ...(exhibitionId ? { exhibition_id: exhibitionId.toString() } : {})
        }
      });
    });
  }
}

// 創建全局實例
const prometheusMetrics = new PrometheusMetrics();

// Express 路由處理器
export const metricsHandler = async (req: Request, res: Response) => {
  try {
    const exhibitionId = req.query.exhibition_id ? 
      parseInt(req.query.exhibition_id as string) : undefined;

    const metricsOutput = await prometheusMetrics.collectMetrics(exhibitionId);

    res.set('Content-Type', 'text/plain; version=0.0.4; charset=utf-8');
    res.send(metricsOutput);

  } catch (error) {
    logger.error('Metrics endpoint 錯誤:', error);
    res.status(500).send('# Error generating metrics\n');
  }
};

export default prometheusMetrics;
