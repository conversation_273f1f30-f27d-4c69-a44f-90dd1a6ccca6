import express from 'express';
import PrismaClient from '@prisma/client';

interface Connection{
  id: string;
  ip: string;
  pid: number;
  hashID: number;
  status: string;
  exhibitionName: string;
  lastSeen: number; // 新增：最後活動時間
}



const prisma = new PrismaClient.PrismaClient({
  log: [ 'info', 'warn', 'error'],
});
export const router = express.Router({ mergeParams: true })

let connectionList: {[hashID: string]: {[display_id: string]: Connection}} = {};

// 修復：加入連接清理機制
const CONNECTION_TIMEOUT = 5 * 60 * 1000; // 5 分鐘超時

// 定時清理過期連接
setInterval(() => {
  const now = Date.now();
  Object.keys(connectionList).forEach(hashID => {
    Object.keys(connectionList[hashID]).forEach(connectionId => {
      const connection = connectionList[hashID][connectionId];
      if (now - connection.lastSeen > CONNECTION_TIMEOUT) {
        delete connectionList[hashID][connectionId];
        console.log(`Cleaned up expired connection: ${connectionId}`);
      }
    });
    
    // 如果 hashID 下沒有連接了，清理空的 hashID
    if (Object.keys(connectionList[hashID]).length === 0) {
      delete connectionList[hashID];
    }
  });
}, 60000); // 每分鐘清理一次

router.post('/api/v1/connection', async (req, res) => {
  let {ip, hashID, status} = req.body;
  connectionList[hashID] = connectionList[hashID] || {};
  let id = crypto.randomUUID().toString();

  try{
    var exhibitionWithVideos = await prisma.exhibition.findMany({
      where: {
        hashID: { equals: hashID as unknown as string }
      },
      include: {
        videos: {
          where:{
            status: "ACTIVE"
          }
        }, // 包含此專案的影片資料
      },
    });
    let exhibitionName = exhibitionWithVideos[0].name;
    let pid = exhibitionWithVideos[0].pid;
    connectionList[hashID][id] = {id, ip, pid, hashID,status, exhibitionName, lastSeen: Date.now()};
    res.status(200).json({data: exhibitionWithVideos[0], display_id: id});
  }catch(e){
    // console.error(e)
    res.status(500);
  }
});
// 交換彼此狀態
router.put('/api/v1/connection', async(req, res) => {
  let { id, hashID, status } = req.body;
    try{
      connectionList[hashID][id].status = status;
      connectionList[hashID][id].lastSeen = Date.now();
      const eStatus = await prisma.exhibition.findMany({
        where: {
          hashID: hashID
        },
        select: {
          status: true
        }
      })
      res.status(200).json(eStatus[0]);
    }catch(e){
      // console.error(e);
      res.status(500);
    }
})

router.delete('/api/v1/connection', (req, res) => {
  let {id, hashID} = req.body;
  try{
    delete connectionList[hashID][id];
    res.status(200).json({ 
      delete:"ok"
    });
  }catch(e){
    res.status(500).send({error: e});
  }

});

router.get('/api/v1/connections', (req, res) => {
  let hashID = req.query.hashID as string??"";
  let pid = parseInt(req.query.pid as string??"-1");
  let items = {};
  if(pid > 0){
    items = Object.entries(connectionList).map(([hashID, connections]) => Object.values(connections).filter((connection)=>connection.pid == pid)).flat();
  }else if(hashID){
    items =  connectionList[hashID]??{};
  }else{
    items = Object.entries(connectionList).map(([hashID, connections]) => connections).flat();
  }
  
  res.json({ items: items });
})