import express from 'express';
import * as EventRegistrationController from '../controllers/eventRegistration.controller';
import * as CheckInController from '../controllers/checkIn.controller';
import * as GameScoreController from '../controllers/gameScore.controller';
import * as GameRoomController from '../controllers/gameRoom.controller';
import { auth } from '../auth';

export const router = express.Router({ mergeParams: true });

// ==================== 報名系統 API ====================

// 創建報名記錄 (公開API，不需要認證)
router.post('/api/v1/registration', EventRegistrationController.createRegistration);

// 通過 QR Code 獲取報名記錄 (公開API，用於簽到)
router.get('/api/v1/registration/qr/:qrCode', EventRegistrationController.getRegistrationByQRCode);

// 獲取報名記錄 (需要認證)
router.get('/api/v1/registration/:id', auth.required, EventRegistrationController.getRegistration);

// 獲取展覽的所有報名記錄 (需要認證)
router.get('/api/v1/exhibition/:exhibitionId/registrations', auth.required, EventRegistrationController.getRegistrationsByExhibition);

// 更新報名狀態 (需要認證)
router.put('/api/v1/registration/:id/status', auth.required, EventRegistrationController.updateRegistrationStatus);

// 刪除報名記錄 (需要認證)
router.delete('/api/v1/registration/:id', auth.required, EventRegistrationController.deleteRegistration);

// 獲取報名統計 (需要認證)
router.get('/api/v1/exhibition/:exhibitionId/registration-stats', auth.required, EventRegistrationController.getRegistrationStats);

// ==================== 簽到系統 API ====================

// 處理簽到 (公開API，用於房間掃描QR Code)
router.post('/api/v1/checkin', CheckInController.checkIn);

// 獲取房間簽到記錄 (需要認證)
router.get('/api/v1/room/:roomId/checkins', auth.required, CheckInController.getRoomCheckIns);

// 獲取展覽簽到統計 (需要認證)
router.get('/api/v1/exhibition/:exhibitionId/checkin-stats', auth.required, CheckInController.getCheckInStats);

// 獲取用戶簽到歷史 (需要認證)
router.get('/api/v1/registration/:registrationId/checkins', auth.required, CheckInController.getUserCheckInHistory);

// 刪除簽到記錄 (需要認證)
router.delete('/api/v1/checkin/:id', auth.required, CheckInController.deleteCheckIn);

// ==================== 遊戲分數系統 API ====================

// 記錄遊戲分數 (公開API，用於遊戲系統)
router.post('/api/v1/game-score', GameScoreController.recordGameScore);

// 獲取房間遊戲分數記錄 (需要認證)
router.get('/api/v1/room/:roomId/game-scores', auth.required, GameScoreController.getRoomGameScores);

// 獲取房間排行榜 (公開API，可用於顯示)
router.get('/api/v1/room/:roomId/leaderboard', GameScoreController.getRoomLeaderboard);

// 獲取用戶遊戲記錄 (需要認證)
router.get('/api/v1/registration/:registrationId/game-scores', auth.required, GameScoreController.getUserGameScores);

// 獲取展覽遊戲統計 (需要認證)
router.get('/api/v1/exhibition/:exhibitionId/game-stats', auth.required, GameScoreController.getExhibitionGameStats);

// 刪除遊戲分數記錄 (需要認證)
router.delete('/api/v1/game-score/:id', auth.required, GameScoreController.deleteGameScore);

// ==================== 房間管理系統 API ====================

// 創建遊戲房間 (需要認證)
router.post('/api/v1/game-room', auth.required, GameRoomController.createGameRoom);

// 獲取房間資訊 (公開API，用於房間顯示)
router.get('/api/v1/game-room/:id', GameRoomController.getGameRoom);

// 獲取展覽的所有房間 (需要認證)
router.get('/api/v1/exhibition/:exhibitionId/game-rooms', auth.required, GameRoomController.getRoomsByExhibition);

// 更新房間資訊 (需要認證)
router.put('/api/v1/game-room/:id', auth.required, GameRoomController.updateGameRoom);

// 更新房間玩家數量 (公開API，用於遊戲系統)
router.put('/api/v1/game-room/:id/players', GameRoomController.updateRoomPlayerCount);

// 獲取房間統計 (需要認證)
router.get('/api/v1/game-room/:id/stats', auth.required, GameRoomController.getRoomStats);

// 刪除房間 (需要認證)
router.delete('/api/v1/game-room/:id', auth.required, GameRoomController.deleteGameRoom);

// 獲取所有房間概覽 (需要認證)
router.get('/api/v1/game-rooms/overview', auth.required, GameRoomController.getAllRoomsOverview);

// ==================== 綜合統計 API ====================

// 獲取展覽完整統計數據 (需要認證)
router.get('/api/v1/exhibition/:exhibitionId/complete-stats', auth.required, async (req, res) => {
  try {
    const { exhibitionId } = req.params;
    
    // 並行獲取各種統計數據
    const [registrationStats, checkInStats, gameStats] = await Promise.all([
      EventRegistrationController.getRegistrationStats(
        { ...req, params: { exhibitionId } } as any, 
        { json: (data: any) => data } as any
      ),
      CheckInController.getCheckInStats(
        { ...req, params: { exhibitionId } } as any,
        { json: (data: any) => data } as any
      ),
      GameScoreController.getExhibitionGameStats(
        { ...req, params: { exhibitionId } } as any,
        { json: (data: any) => data } as any
      )
    ]);

    res.json({
      success: true,
      data: {
        registration: registrationStats,
        checkIn: checkInStats,
        game: gameStats,
        timestamp: new Date()
      }
    });

  } catch (error) {
    res.status(500).json({ error: '獲取綜合統計失敗' });
  }
});

// 健康檢查 API
router.get('/api/v1/event-system/health', (req, res) => {
  res.json({
    success: true,
    message: '活動系統運行正常',
    timestamp: new Date(),
    version: '1.0.0'
  });
});
