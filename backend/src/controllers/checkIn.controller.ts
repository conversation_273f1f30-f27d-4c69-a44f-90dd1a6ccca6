import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { logger } from '../winston/logger';

const prisma = new PrismaClient({
  log: ['info', 'warn', 'error'],
});

// 處理簽到
export const checkIn = async (req: Request, res: Response) => {
  try {
    const { qrCode, roomId, roomName } = req.body;
    const ipAddress = req.ip || req.connection.remoteAddress || 'unknown';

    // 通過 QR Code 查找報名記錄
    const registration = await prisma.eventRegistration.findUnique({
      where: { qrCode },
      include: {
        exhibition: true,
        checkIns: {
          where: { roomId },
          orderBy: { checkInTime: 'desc' },
          take: 1
        }
      }
    });

    if (!registration) {
      return res.status(404).json({ 
        success: false,
        error: 'QR Code 無效或報名記錄不存在' 
      });
    }

    // 檢查展覽是否開放
    if (registration.exhibition.status !== 'OPEN') {
      return res.status(400).json({ 
        success: false,
        error: '活動尚未開始或已結束' 
      });
    }

    // 檢查是否已在此房間簽到過（可選：防止重複簽到）
    const existingCheckIn = registration.checkIns[0];
    if (existingCheckIn) {
      const timeDiff = Date.now() - existingCheckIn.checkInTime.getTime();
      const hoursDiff = timeDiff / (1000 * 60 * 60);
      
      // 如果在1小時內已簽到過同一房間，返回歡迎訊息但不重複記錄
      if (hoursDiff < 1) {
        return res.json({
          success: true,
          isRepeatCheckIn: true,
          message: `歡迎回來，${registration.userName}！`,
          data: {
            userName: registration.userName,
            roomName,
            checkInTime: existingCheckIn.checkInTime,
            exhibitionName: registration.exhibition.name
          }
        });
      }
    }

    // 創建簽到記錄
    const checkInRecord = await prisma.checkIn.create({
      data: {
        registrationId: registration.id,
        roomId,
        roomName,
        ipAddress
      }
    });

    // 更新報名狀態為已簽到
    await prisma.eventRegistration.update({
      where: { id: registration.id },
      data: { status: 'CHECKED_IN' }
    });

    logger.info(`用戶 ${registration.userName} 在房間 ${roomName} 簽到成功`);

    res.json({
      success: true,
      isRepeatCheckIn: false,
      message: `歡迎 ${registration.userName} 報到！`,
      data: {
        checkInId: checkInRecord.id,
        userName: registration.userName,
        roomName,
        checkInTime: checkInRecord.checkInTime,
        exhibitionName: registration.exhibition.name
      }
    });

  } catch (error) {
    logger.error('簽到失敗:', error);
    res.status(500).json({ 
      success: false,
      error: '簽到處理失敗' 
    });
  }
};

// 獲取房間簽到記錄
export const getRoomCheckIns = async (req: Request, res: Response) => {
  try {
    const { roomId } = req.params;
    const { date } = req.query;

    let whereCondition: any = { roomId };

    // 如果指定日期，只查詢該日期的記錄
    if (date) {
      const startDate = new Date(date as string);
      const endDate = new Date(startDate);
      endDate.setDate(endDate.getDate() + 1);

      whereCondition.checkInTime = {
        gte: startDate,
        lt: endDate
      };
    }

    const checkIns = await prisma.checkIn.findMany({
      where: whereCondition,
      include: {
        registration: {
          include: {
            exhibition: true
          }
        }
      },
      orderBy: { checkInTime: 'desc' }
    });

    res.json({
      success: true,
      data: checkIns,
      total: checkIns.length
    });

  } catch (error) {
    logger.error('獲取房間簽到記錄失敗:', error);
    res.status(500).json({ error: '獲取簽到記錄失敗' });
  }
};

// 獲取展覽的簽到統計
export const getCheckInStats = async (req: Request, res: Response) => {
  try {
    const { exhibitionId } = req.params;

    // 總簽到次數
    const totalCheckIns = await prisma.checkIn.count({
      where: {
        registration: {
          exhibitionId: parseInt(exhibitionId)
        }
      }
    });

    // 唯一簽到用戶數
    const uniqueUsers = await prisma.checkIn.groupBy({
      by: ['registrationId'],
      where: {
        registration: {
          exhibitionId: parseInt(exhibitionId)
        }
      },
      _count: {
        registrationId: true
      }
    });

    // 各房間簽到統計
    const roomStats = await prisma.checkIn.groupBy({
      by: ['roomId', 'roomName'],
      where: {
        registration: {
          exhibitionId: parseInt(exhibitionId)
        }
      },
      _count: {
        id: true
      },
      orderBy: {
        _count: {
          id: 'desc'
        }
      }
    });

    // 今日簽到統計
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const todayCheckIns = await prisma.checkIn.count({
      where: {
        registration: {
          exhibitionId: parseInt(exhibitionId)
        },
        checkInTime: {
          gte: today,
          lt: tomorrow
        }
      }
    });

    res.json({
      success: true,
      data: {
        totalCheckIns,
        uniqueUsers: uniqueUsers.length,
        todayCheckIns,
        roomStats
      }
    });

  } catch (error) {
    logger.error('獲取簽到統計失敗:', error);
    res.status(500).json({ error: '獲取統計數據失敗' });
  }
};

// 獲取用戶的簽到歷史
export const getUserCheckInHistory = async (req: Request, res: Response) => {
  try {
    const { registrationId } = req.params;

    const checkIns = await prisma.checkIn.findMany({
      where: {
        registrationId: parseInt(registrationId)
      },
      orderBy: { checkInTime: 'desc' }
    });

    res.json({
      success: true,
      data: checkIns,
      total: checkIns.length
    });

  } catch (error) {
    logger.error('獲取用戶簽到歷史失敗:', error);
    res.status(500).json({ error: '獲取簽到歷史失敗' });
  }
};

// 刪除簽到記錄
export const deleteCheckIn = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    await prisma.checkIn.delete({
      where: { id: parseInt(id) }
    });

    logger.info(`簽到記錄 ${id} 已刪除`);

    res.json({
      success: true,
      message: '簽到記錄已刪除'
    });

  } catch (error) {
    logger.error('刪除簽到記錄失敗:', error);
    res.status(500).json({ error: '刪除簽到記錄失敗' });
  }
};
