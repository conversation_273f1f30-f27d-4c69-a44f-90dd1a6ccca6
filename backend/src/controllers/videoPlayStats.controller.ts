import {Request,Response} from "express";
import { PrismaClient } from '@prisma/client';


const prisma = new PrismaClient({
        log: [ 'info', 'warn', 'error'],
    
  });
  
export const createVideoPlayStat = async (req:Request, res:Response)=>{
    
    try{
        const videoPlayStat = await prisma.videoPlayStats.create({
            data: req.body
        });
        res.status(200).json(videoPlayStat);
    }catch(e){
        res.status(500).json({ error : e });
    }
}

export const getVideoPlayStat = async (req:Request, res:Response)=>{
   
    try{
        const { id } = req.params;
        const videoPlayStat = await prisma.videoPlayStats.findUnique({
            where:{
                id: parseInt(id)
            }
        });
        res.status(200).json(videoPlayStat);
    }catch(e){
        res.status(500).json({ error : e });
    }
}

export const getVideoPlayStats = async(req:Request, res:Response) =>{

    try{
        const {where, select, ... others} = req.query;
        const whereCondition = where ? JSON.parse(decodeURIComponent(where.toString())) : null;
        const selectCondition = select ? JSON.parse(decodeURIComponent(select.toString())) : null;
        const videos = await prisma.videoPlayStats.findMany({
            where: whereCondition,
            select: selectCondition
        });
        res.status(200).json({items: videos});
    }catch(e){
        res.status(500).json({ error : e });
    }
}

export const updateVideoPlayStat = async (req:Request, res:Response)=>{
    try{
        const videoPlayStat = await prisma.videoPlayStats.update({
            where:{
                id:parseInt(req.params.id)
            },
            data:req.body
        });
        res.status(200).json(videoPlayStat);
    }catch(e){
        res.status(500).json({ error : e });
    }
}

export const deleteVideoPlayStat = async (req:Request, res:Response)=>{
    try{
        const videoPlayStat = await prisma.videoPlayStats.delete({
            where:{
                 id: parseInt(req.params.id)
            }
        });
        res.status(200).json(videoPlayStat);
    }catch(e){
        res.status(500).json({ error : e });
    }
}