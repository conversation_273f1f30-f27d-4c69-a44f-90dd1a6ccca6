import {Request,Response} from "express";
import { PrismaClient } from '@prisma/client';


const prisma = new PrismaClient({
        log: [ 'info', 'warn', 'error'],
    
  });
  
export const createVideo = async (req:Request, res:Response)=>{
    
    try{
        const video = await prisma.video.create({
            data: {...req.body, uploader: req.auth!.username}
        });
        res.status(200).json(video);
    }catch(e){
        res.status(500).json({ error : e });
    }
}

export const getVideo = async (req:Request, res:Response)=>{
   
    try{
        const { id } = req.params;
        const video = await prisma.video.findUnique({
            where:{
                id: parseInt(id)
            }
        });
        res.status(200).json(video);
    }catch(e){
        res.status(500).json({ error : e });
    }
}

export const getVideos = async(req:Request, res:Response) =>{
    try{
        const videos = await prisma.video.findMany();
        res.status(200).json({items: videos});
    }catch(e){
        res.status(500).json({ error : e });
    }
}

export const updateVideo = async (req:Request, res:Response)=>{
    try{
        const video = await prisma.video.update({
            where:{
                id:parseInt(req.params.id)
            },
            data:req.body
        });
        res.status(200).json(video);
    }catch(e){
        res.status(500).json({ error : e });
    }
}

export const deleteVideo = async (req:Request, res:Response)=>{
    try{
        const video = await prisma.video.delete({
            where:{
                 id: parseInt(req.params.id)
            }
        });
        res.status(200).json(video);
    }catch(e){
        res.status(500).json({ error : e });
    }
}

export const getVideoPermissions = async(req:Request, res:Response)=>{
    try{
        const permissons = await prisma.permission.findMany({
            where:{
                id: parseInt(req.params.id)
            }
        });
        res.status(200).json({items: permissons});
    }catch(e){
        res.status(500).json(e);
    }
}

export const getVideosWithPlayStat= async(req:Request, res:Response)=>{
    try{
        let result = "";
        if(req.query.eid){
            result = await prisma.$queryRaw`SELECT b.id, count(a.videoId) AS impressions,sum(a.playTime) AS TimeSpent, b.duration,b.name,e.name as ExhibitionName  FROM iamm.video_play_stats a LEFT JOIN iamm.Video b on a.videoId  = b.id LEFT JOIN iamm.Exhibition e on e.id = b.eid where e.id = ${req.query.eid}  GROUP BY a.videoId`;
        }else if(req.query.pid){
            result = await prisma.$queryRaw`SELECT b.id, count(a.videoId) AS impressions,sum(a.playTime) AS TimeSpent, b.duration,b.name,e.name as ExhibitionName  FROM iamm.video_play_stats a LEFT JOIN iamm.Video b on a.videoId  = b.id LEFT JOIN iamm.Exhibition e on e.id = b.eid where e.pid = ${req.query.pid}  GROUP BY a.videoId`;
        }else{
            result = await prisma.$queryRaw`SELECT b.id, count(a.videoId) AS impressions,sum(a.playTime) AS TimeSpent, b.duration,b.name,e.name as ExhibitionName  FROM iamm.video_play_stats a LEFT JOIN iamm.Video b on a.videoId  = b.id LEFT JOIN iamm.Exhibition e on e.id = b.eid  GROUP BY a.videoId`;
        }
        const replacer = (key:string, value:any) => {
            // 檢查是否為 BigInt 類型
            if (typeof value === 'bigint') {
              return value.toString();  // 將 BigInt 轉換為字符串
            }
            return value;
          };
        res.status(200).json({items: JSON.stringify(result, replacer)});
    }catch(e){
        res.status(500).json(e);
    }
}