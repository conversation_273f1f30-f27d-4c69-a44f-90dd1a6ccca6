import {Request,Response} from "express";
import { PrismaClient } from '@prisma/client';
import * as jwt from 'jsonwebtoken';
import * as bcrypt from 'bcrypt';
import { FieldRef } from "@prisma/client/runtime/library";
import { logger } from "../winston/logger";
import { prisma } from "../utils/database";


// 修復：移除重複的 PrismaClient 實例，使用統一的資料庫服務
// const prisma = new PrismaClient({
//     log: [ 'info', 'warn', 'error'],
//   });

export const createUser = async (req:Request, res:Response)=>{
    try{
        const { account, password, name, phone, email, role } = req.body;
        const user = await prisma.user.findUnique({
            where:{
                account: account
            }
        });

        if(user != null){
            res.status(401).send("already exists");
            return;
        }

        const user2 = await prisma.user.create({
            data:{
                account : account,
                password: bcrypt.hashSync(password, 10),
                name    : name,
                phone   : phone,
                email   : email,
                role: role
            }
        });
        res.status(200).json(user2);
    }catch(e){
        res.status(500).json({ error : e });
    }
}

export const getUser = async (req:Request, res:Response)=>{
    try{
        const account = req.params.account;
        const user = await prisma.user.findUnique({
            where:{
                account: account
            }
        });
        res.status(200).json(user);
    }catch(e){
        res.status(500).json({ error : e });
    }
}

// Should add root 
export const getUsers = async(req:Request, res:Response)=>{
    const {where, select, ... others} = req.query;
    const whereCondition = where ? JSON.parse(decodeURIComponent(where.toString())) : null;
    const selectCondition = select ? JSON.parse(decodeURIComponent(select.toString())) : null;
    try{
        const users = await prisma.user.findMany({
            where: whereCondition,
            select: selectCondition
        });
        res.status(200).json({items: users});
    }catch(e){
        res.status(500).json({ error : e });
    }
}

export const updateUser = async (req:Request, res:Response)=>{
    try{
        const user = await prisma.user.update({
            where:{
                account: req.params.account
            },
            data: req.body
        });
        res.status(200).json(user);
    }catch(e){
        res.status(500).json({ error : e });
    }
}

export const deleteUser = async (req:Request, res:Response)=>{
    try{
        const { account } = req.body;
        const user = await prisma.user.delete({
            where:{
                account: account
            }
        });
        res.status(200).json(user);
    }catch(e){
        res.status(500).json({ error : e });
    }
}

export const login = async (req:Request, res:Response)=>{
    try{
        const { account, password } = req.body;   
        let user = await prisma.user.findUnique({
            where:{
                account: account
            }
        });
        if(!user){
            logger.error(`User ${account} not found`);
            res.status(401).json({ message: 'Invalid username or password.' });
            return;
        }
        if(!bcrypt.compareSync(password, user.password!)){
            logger.error(`Password ${ password } not match`);
            res.status(401).json({ message: 'Invalid username or password.' });
            return;
        }
        
        const token = jwt.sign(
            { id: user.account, username: account }, // Payload (data inside the token)
            process.env.JWT_SECRET || 'superSecret', // Secret key
            { expiresIn: '1h' } // Token expiration time (optional)
          );
        res.json({ token });
        res.end();
    }catch(e){
        res.status(500).json({ error : e });
    }
}

export const secret = async (req:Request, res:Response)=> {
    res.json({
        user: req.auth,
      });
}

export const getUserProjects = async (req:Request, res:Response)=>{
    try{
        const projects = await getUserProjectsByPerm(req.params.account, "VIEW");
        res.status(200).json(projects);
        
    }catch(e){
        res.status(500).json(e);
    }
    
}

async function getUserProjectsByPerm(userId:string,perm:string) {
    // 查詢個人專案 `VIEW` 權限
    const personalPermissions = await prisma.permission.findMany({
        where: {
            user_id: userId,
            permission: {
                "equals": perm as unknown as FieldRef<"Permission", "Permissions">
            }
        },
        select: {
            project: true  // 只選擇專案
        }
    });
 
    //查詢使用者所在群組的專案 `VIEW` 權限
    const groupPermissions = await prisma.permission.findMany({
        where: {
            group: {
                members: {
                    some:{
                        user:{
                            account:{
                                equals: userId
                            }
                        }
                    }
                }
            },
            permission:  {
                "equals": perm as unknown as FieldRef<"Permission", "Permissions">
            }
        },
        select: {
            project: true  // 只選擇專案
        }
    });
    //將個人和群組的專案結果合併
    const allProjects = [...personalPermissions, ...groupPermissions];

    // 去重 (根據專案ID)
    const uniqueProjects = Array.from(new Set(allProjects.map(p => p.project.id)))
        .map(id => {
            return allProjects.find(p => p.project.id === id);
        });

    return uniqueProjects.map(p => p!.project);
}