import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { logger } from '../winston/logger';

const prisma = new PrismaClient({
  log: ['info', 'warn', 'error'],
});

// 創建遊戲房間
export const createGameRoom = async (req: Request, res: Response) => {
  try {
    const { id, name, exhibitionId, gameType, maxPlayers } = req.body;

    // 檢查展覽是否存在
    const exhibition = await prisma.exhibition.findUnique({
      where: { id: exhibitionId }
    });

    if (!exhibition) {
      return res.status(404).json({ error: '展覽不存在' });
    }

    // 檢查房間ID是否已存在
    const existingRoom = await prisma.gameRoom.findUnique({
      where: { id }
    });

    if (existingRoom) {
      return res.status(400).json({ error: '房間ID已存在' });
    }

    // 創建房間
    const gameRoom = await prisma.gameRoom.create({
      data: {
        id,
        name,
        exhibitionId,
        gameType: gameType || 'DART',
        maxPlayers: maxPlayers || 1,
        status: 'ACTIVE'
      }
    });

    logger.info(`遊戲房間創建: ${id} - ${name}`);

    res.status(201).json({
      success: true,
      data: gameRoom
    });

  } catch (error) {
    logger.error('創建遊戲房間失敗:', error);
    res.status(500).json({ error: '創建遊戲房間失敗' });
  }
};

// 獲取房間資訊
export const getGameRoom = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const gameRoom = await prisma.gameRoom.findUnique({
      where: { id },
      include: {
        exhibition: true
      }
    });

    if (!gameRoom) {
      return res.status(404).json({ error: '房間不存在' });
    }

    res.json({
      success: true,
      data: gameRoom
    });

  } catch (error) {
    logger.error('獲取房間資訊失敗:', error);
    res.status(500).json({ error: '獲取房間資訊失敗' });
  }
};

// 獲取展覽的所有房間
export const getRoomsByExhibition = async (req: Request, res: Response) => {
  try {
    const { exhibitionId } = req.params;
    const { status, gameType } = req.query;

    let whereCondition: any = {
      exhibitionId: parseInt(exhibitionId)
    };

    if (status) {
      whereCondition.status = status;
    }

    if (gameType) {
      whereCondition.gameType = gameType;
    }

    const gameRooms = await prisma.gameRoom.findMany({
      where: whereCondition,
      include: {
        exhibition: true
      },
      orderBy: { createdAt: 'asc' }
    });

    res.json({
      success: true,
      data: gameRooms,
      total: gameRooms.length
    });

  } catch (error) {
    logger.error('獲取展覽房間失敗:', error);
    res.status(500).json({ error: '獲取房間列表失敗' });
  }
};

// 更新房間資訊
export const updateGameRoom = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { name, gameType, maxPlayers, status, currentPlayers } = req.body;

    const updateData: any = {};

    if (name !== undefined) updateData.name = name;
    if (gameType !== undefined) updateData.gameType = gameType;
    if (maxPlayers !== undefined) updateData.maxPlayers = maxPlayers;
    if (status !== undefined) updateData.status = status;
    if (currentPlayers !== undefined) updateData.currentPlayers = currentPlayers;

    const gameRoom = await prisma.gameRoom.update({
      where: { id },
      data: updateData
    });

    logger.info(`房間 ${id} 資訊已更新`);

    res.json({
      success: true,
      data: gameRoom
    });

  } catch (error) {
    logger.error('更新房間資訊失敗:', error);
    res.status(500).json({ error: '更新房間資訊失敗' });
  }
};

// 更新房間玩家數量
export const updateRoomPlayerCount = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { action } = req.body; // 'join' or 'leave'

    const gameRoom = await prisma.gameRoom.findUnique({
      where: { id }
    });

    if (!gameRoom) {
      return res.status(404).json({ error: '房間不存在' });
    }

    let newPlayerCount = gameRoom.currentPlayers;

    if (action === 'join') {
      if (newPlayerCount >= gameRoom.maxPlayers) {
        return res.status(400).json({ error: '房間已滿' });
      }
      newPlayerCount += 1;
    } else if (action === 'leave') {
      if (newPlayerCount > 0) {
        newPlayerCount -= 1;
      }
    } else {
      return res.status(400).json({ error: '無效的操作' });
    }

    const updatedRoom = await prisma.gameRoom.update({
      where: { id },
      data: { currentPlayers: newPlayerCount }
    });

    logger.info(`房間 ${id} 玩家數量更新: ${newPlayerCount}/${gameRoom.maxPlayers}`);

    res.json({
      success: true,
      data: updatedRoom
    });

  } catch (error) {
    logger.error('更新房間玩家數量失敗:', error);
    res.status(500).json({ error: '更新房間玩家數量失敗' });
  }
};

// 獲取房間統計數據
export const getRoomStats = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    // 簽到統計
    const checkInCount = await prisma.checkIn.count({
      where: { roomId: id }
    });

    // 遊戲統計
    const gameStats = await prisma.gameScore.aggregate({
      where: { roomId: id },
      _count: { id: true },
      _avg: { score: true },
      _max: { score: true },
      _min: { score: true }
    });

    // 完成遊戲統計
    const completedGames = await prisma.gameScore.count({
      where: { 
        roomId: id,
        isCompleted: true 
      }
    });

    // 今日統計
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const todayCheckIns = await prisma.checkIn.count({
      where: {
        roomId: id,
        checkInTime: {
          gte: today,
          lt: tomorrow
        }
      }
    });

    const todayGames = await prisma.gameScore.count({
      where: {
        roomId: id,
        playedAt: {
          gte: today,
          lt: tomorrow
        }
      }
    });

    res.json({
      success: true,
      data: {
        checkInCount,
        todayCheckIns,
        totalGames: gameStats._count.id || 0,
        completedGames,
        todayGames,
        averageScore: gameStats._avg.score || 0,
        highestScore: gameStats._max.score || 0,
        lowestScore: gameStats._min.score || 0,
        completionRate: gameStats._count.id > 0 ? 
          (completedGames / gameStats._count.id * 100).toFixed(2) : 0
      }
    });

  } catch (error) {
    logger.error('獲取房間統計失敗:', error);
    res.status(500).json({ error: '獲取房間統計失敗' });
  }
};

// 刪除房間
export const deleteGameRoom = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    await prisma.gameRoom.delete({
      where: { id }
    });

    logger.info(`遊戲房間 ${id} 已刪除`);

    res.json({
      success: true,
      message: '遊戲房間已刪除'
    });

  } catch (error) {
    logger.error('刪除遊戲房間失敗:', error);
    res.status(500).json({ error: '刪除遊戲房間失敗' });
  }
};

// 獲取所有房間概覽
export const getAllRoomsOverview = async (req: Request, res: Response) => {
  try {
    const { exhibitionId } = req.query;

    let whereCondition: any = {};
    if (exhibitionId) {
      whereCondition.exhibitionId = parseInt(exhibitionId as string);
    }

    const rooms = await prisma.gameRoom.findMany({
      where: whereCondition,
      include: {
        exhibition: {
          select: {
            id: true,
            name: true,
            status: true
          }
        }
      },
      orderBy: { createdAt: 'asc' }
    });

    // 為每個房間獲取基本統計
    const roomsWithStats = await Promise.all(
      rooms.map(async (room) => {
        const checkInCount = await prisma.checkIn.count({
          where: { roomId: room.id }
        });

        const gameCount = await prisma.gameScore.count({
          where: { roomId: room.id }
        });

        return {
          ...room,
          stats: {
            checkInCount,
            gameCount
          }
        };
      })
    );

    res.json({
      success: true,
      data: roomsWithStats,
      total: roomsWithStats.length
    });

  } catch (error) {
    logger.error('獲取房間概覽失敗:', error);
    res.status(500).json({ error: '獲取房間概覽失敗' });
  }
};
