import {Request,Response} from "express";
import { PrismaClient, Prisma } from '@prisma/client';

const prisma = new PrismaClient({
    log: [
      {
        emit: "event",
        level: "query",
      },
    ],
  });
  
export const createPermission = async (req:Request, res:Response) =>{
    
    try{
        // const {  pid, group, } = req.body;

        const permission = await prisma.permission.create({
            data: req.body
        });
        res.status(200).json(permission);
    }catch(e){
        res.status(500).json({ error : e });
    }
}

export const getPermission = async (req:Request, res:Response) =>{
   
    try{
        const { id } = req.params;
        const permission = await prisma.permission.findUnique({
            where:{
                id:parseInt(id)
            }
        });
        res.status(200).json(permission);
    }catch(e){
        res.status(500).json({ error : e });
    }
}

export const getPermissions = async(req:Request, res:Response) =>{
    try{
        if( req.auth!.username == "root"){
            const permissions = await prisma.permission.findMany();
            res.status(200).json({items: permissions});
        }
        else
            res.status(401).json("You don't have access rights.");
    }catch(e){
        res.status(500).json({ error : e });
    }
}

export const updatePermission = async (req:Request, res:Response) =>{
    try{
        const permission = await prisma.permission.update({
            where:{
                id:parseInt(req.params.id)
            },
            data:req.body
        });
        res.status(200).json(permission);
    }catch(e){
        res.status(500).json({ error : e });
    }
}
// only allow project manager to delete.
export const deletePermission = async (req:Request, res:Response) =>{
    try{
        const permission = await prisma.permission.delete({
            where:{
                 id: parseInt(req.params.id)
            }
        });
        res.status(200).json(permission);
    }catch(e){
        res.status(500).json({ error : e });
    }
}

async function hasPermission({ groupId = null, userId = null} ){
    // 檢查 groupId 和 userId 至少有一個
    if (!groupId && !userId) {
        throw new Error('Either groupId or userId must be provided.');
    }
    let groupIdCondition:Prisma.PermissionWhereInput = { group_id: groupId };
    let userIdCondition:Prisma.PermissionWhereInput = { user_id: userId };

     // 構建 Prisma 查詢條件
     const whereCondition: Prisma.PermissionWhereInput  = {
        OR: [
            groupIdCondition,
            userIdCondition
        ].filter(Boolean), // 過濾掉 null
    };

    // 執行 Prisma 查詢
    const permission= await prisma.permission.findFirst({
        where: whereCondition
    });

    // 檢查是否有權限
    return permission !== null;
}