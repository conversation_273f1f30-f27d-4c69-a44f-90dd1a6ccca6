import {Request,Response} from "express";
import { PrismaClient } from '@prisma/client';
import { prisma } from "../utils/database";

export const createProject = async (req:Request, res:Response)=>{
    try{
        const project = await createProjectAndManager(req.auth!.username, req.body);
        res.status(200).json(project);
    }catch(e){
        res.status(500).json({ error : e });
    }
}

export const getProject = async (req:Request, res:Response)=>{
    try{
        const { id } = req.params;
        const project = await prisma.project.findUnique({
            where:{
                id: parseInt(id)
            }
        });
        res.status(200).json(project);
    }catch(e){
        res.status(500).json({ error : e });
    }
}

export const getProjects = async(req:Request, res:Response)=>{
    const {where, select,include, ... others} = req.query;
    const whereCondition = where ? JSON.parse(decodeURIComponent(where.toString())) : undefined;
    const selectCondition = select ? JSON.parse(decodeURIComponent(select.toString())) : undefined;
    try{
        var projects = [];
        var groupProjects: any[] = [];
        let user = await prisma.user.findUnique({
            select:{
                role: true
            },
            where:{
                account: req.auth!.username
            }
        })
        // 求類型
        if( user!.role as string == "SUPERADMIN"){
            projects = await prisma.project.findMany();
        }else{
            let groups = await prisma.memberGroup.findMany({
                where:{
                    members:{
                        some:{
                            account: req.auth!.username
                        }
                    }
                }
            });
            // 有待添加群組權限允許讀取專案
            projects = await prisma.project.findMany(
                {
                    where: whereCondition,
                    select: selectCondition,
                }
            );
            // groups.forEach(async (g)=>{
            //     let groupProject = await prisma.project.findUnique({
            //         where:{
            //             id: g.project_id
            //         }
            //     });
            //     groupProjects.push(groupProject as Project);

                groupProjects = await Promise.all(groups.map(async (g: any) => {
                    const project = 
                    await prisma.project.findUnique({
                      where: {
                        id: g.project_id
                      }
                    });
                    return project;
                  }).filter((project: any) => project !== null)); 
        }
        var combined = [...projects, ... groupProjects];
        const uniqueProjects = combined.filter((project, index) => {
            return combined.findIndex((p) => p.id === project.id) === index;
          });
        res.status(200).json({items:uniqueProjects});
    }catch(e){
        res.status(500).json({ error : e });
    }
}

export const updateProject = async (req:Request, res:Response)=>{
    try{
        const project = await prisma.project.update({
            where:{
                id: parseInt(req.params.id)
            },
            data:req.body
        });
        res.status(200).json(project);
    }catch(e){
        res.status(500).json({ error : e });
    }
}

export const deleteProject = async (req:Request, res:Response)=>{
    try{
        const project = await prisma.project.delete({
            where:{
                 id :  parseInt(req.params.id)
            }
        });
        res.status(200).json(project);
    }catch(e){
        res.status(500).json({ error : e });
    }
}

export const getProjectPermissions = async(req:Request, res:Response)=>{
    try{
        const permissions = await prisma.permission.findMany({
            where:{
                pid: parseInt(req.params.id)
            }

        });
        res.status(200).json(permissions);
    }catch(e){
        res.status(500).json({ error : e});
    }
}

export const getProjectMemberGroups = async(req:Request, res:Response)=>{
    try{
        const memberGroups = await prisma.memberGroup.findMany({
            where:{
                project_id : parseInt(req.params.id)
            }
        });
        res.status(200).json({items: memberGroups});
    }catch(e){
        res.status(500).json({ error : e });
    }
}

export const getProjectExhibitions = async (req:Request, res:Response)=>{
    try{
        const exhibitions = await prisma.exhibition.findMany({
            where:{
                pid: parseInt(req.params.id)
            }
        });
        res.status(200).json({items: exhibitions});
    }catch(e){
        res.status(500).json({error:e})
    }
}

async function  createProjectAndManager(userAccount:string, projectData:Request) {
    const projectDataObj = projectData as unknown as { name: string; description: string };
    // Start a transaction to ensure atomicity (project and manager are created together)
    const project = await prisma.$transaction(async (prismaTransaction: any) => {
      // 1. 創建專案
      const newProject = await prismaTransaction.project.create({
        data: {
          name        : projectDataObj.name,
          description : projectDataObj.description,
          created_by  : userAccount,  // 使用者帳號作為創建者
        },
      });
  
      // 2. 將創建者添加為專案管理者
      await prismaTransaction.projectManager.create({
        data: {
          account : userAccount,      // 設置為管理者的使用者帳號
          id      : newProject.id,        // 新創建專案的 ID
        },
      });
  
      return newProject;
    });
  
    return project;
  }