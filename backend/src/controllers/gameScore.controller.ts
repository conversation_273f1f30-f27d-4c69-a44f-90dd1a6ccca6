import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { logger } from '../winston/logger';

const prisma = new PrismaClient({
  log: ['info', 'warn', 'error'],
});

// 記錄遊戲分數
export const recordGameScore = async (req: Request, res: Response) => {
  try {
    const { qrCode, roomId, gameType, score, maxScore, playTime, isCompleted } = req.body;

    // 通過 QR Code 查找報名記錄
    const registration = await prisma.eventRegistration.findUnique({
      where: { qrCode },
      include: {
        exhibition: true
      }
    });

    if (!registration) {
      return res.status(404).json({ 
        success: false,
        error: 'QR Code 無效或報名記錄不存在' 
      });
    }

    // 檢查展覽是否開放
    if (registration.exhibition.status !== 'OPEN') {
      return res.status(400).json({ 
        success: false,
        error: '活動尚未開始或已結束' 
      });
    }

    // 記錄遊戲分數
    const gameScore = await prisma.gameScore.create({
      data: {
        registrationId: registration.id,
        roomId,
        gameType: gameType || 'DART',
        score,
        maxScore,
        playTime,
        isCompleted: isCompleted || false
      }
    });

    // 如果遊戲完成，更新報名狀態
    if (isCompleted) {
      await prisma.eventRegistration.update({
        where: { id: registration.id },
        data: { status: 'COMPLETED' }
      });
    }

    logger.info(`用戶 ${registration.userName} 在房間 ${roomId} 遊戲分數: ${score}`);

    res.json({
      success: true,
      data: {
        gameScoreId: gameScore.id,
        userName: registration.userName,
        score,
        maxScore,
        isCompleted,
        playedAt: gameScore.playedAt
      }
    });

  } catch (error) {
    logger.error('記錄遊戲分數失敗:', error);
    res.status(500).json({ 
      success: false,
      error: '記錄遊戲分數失敗' 
    });
  }
};

// 獲取房間遊戲分數記錄
export const getRoomGameScores = async (req: Request, res: Response) => {
  try {
    const { roomId } = req.params;
    const { gameType, limit = 50 } = req.query;

    let whereCondition: any = { roomId };

    if (gameType) {
      whereCondition.gameType = gameType;
    }

    const gameScores = await prisma.gameScore.findMany({
      where: whereCondition,
      include: {
        registration: {
          select: {
            userName: true,
            userPhone: true
          }
        }
      },
      orderBy: { playedAt: 'desc' },
      take: parseInt(limit as string)
    });

    res.json({
      success: true,
      data: gameScores,
      total: gameScores.length
    });

  } catch (error) {
    logger.error('獲取房間遊戲分數失敗:', error);
    res.status(500).json({ error: '獲取遊戲分數失敗' });
  }
};

// 獲取房間排行榜
export const getRoomLeaderboard = async (req: Request, res: Response) => {
  try {
    const { roomId } = req.params;
    const { gameType, limit = 10 } = req.query;

    let whereCondition: any = { 
      roomId,
      isCompleted: true 
    };

    if (gameType) {
      whereCondition.gameType = gameType;
    }

    // 獲取最高分記錄
    const topScores = await prisma.gameScore.findMany({
      where: whereCondition,
      include: {
        registration: {
          select: {
            userName: true,
            userPhone: true
          }
        }
      },
      orderBy: { score: 'desc' },
      take: parseInt(limit as string)
    });

    // 計算排名
    const leaderboard = topScores.map((score, index) => ({
      rank: index + 1,
      userName: score.registration.userName,
      score: score.score,
      maxScore: score.maxScore,
      playTime: score.playTime,
      playedAt: score.playedAt
    }));

    res.json({
      success: true,
      data: leaderboard
    });

  } catch (error) {
    logger.error('獲取排行榜失敗:', error);
    res.status(500).json({ error: '獲取排行榜失敗' });
  }
};

// 獲取用戶遊戲記錄
export const getUserGameScores = async (req: Request, res: Response) => {
  try {
    const { registrationId } = req.params;
    const { gameType } = req.query;

    let whereCondition: any = {
      registrationId: parseInt(registrationId)
    };

    if (gameType) {
      whereCondition.gameType = gameType;
    }

    const gameScores = await prisma.gameScore.findMany({
      where: whereCondition,
      orderBy: { playedAt: 'desc' }
    });

    // 計算統計數據
    const stats = {
      totalGames: gameScores.length,
      completedGames: gameScores.filter(g => g.isCompleted).length,
      highestScore: gameScores.length > 0 ? Math.max(...gameScores.map(g => g.score)) : 0,
      averageScore: gameScores.length > 0 ? 
        gameScores.reduce((sum, g) => sum + g.score, 0) / gameScores.length : 0,
      totalPlayTime: gameScores.reduce((sum, g) => sum + (g.playTime || 0), 0)
    };

    res.json({
      success: true,
      data: {
        gameScores,
        stats
      }
    });

  } catch (error) {
    logger.error('獲取用戶遊戲記錄失敗:', error);
    res.status(500).json({ error: '獲取遊戲記錄失敗' });
  }
};

// 獲取展覽遊戲統計
export const getExhibitionGameStats = async (req: Request, res: Response) => {
  try {
    const { exhibitionId } = req.params;

    // 總遊戲次數
    const totalGames = await prisma.gameScore.count({
      where: {
        registration: {
          exhibitionId: parseInt(exhibitionId)
        }
      }
    });

    // 完成遊戲次數
    const completedGames = await prisma.gameScore.count({
      where: {
        registration: {
          exhibitionId: parseInt(exhibitionId)
        },
        isCompleted: true
      }
    });

    // 各房間遊戲統計
    const roomStats = await prisma.gameScore.groupBy({
      by: ['roomId'],
      where: {
        registration: {
          exhibitionId: parseInt(exhibitionId)
        }
      },
      _count: {
        id: true
      },
      _avg: {
        score: true
      },
      _max: {
        score: true
      }
    });

    // 遊戲類型統計
    const gameTypeStats = await prisma.gameScore.groupBy({
      by: ['gameType'],
      where: {
        registration: {
          exhibitionId: parseInt(exhibitionId)
        }
      },
      _count: {
        id: true
      },
      _avg: {
        score: true
      }
    });

    // 今日遊戲統計
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const todayGames = await prisma.gameScore.count({
      where: {
        registration: {
          exhibitionId: parseInt(exhibitionId)
        },
        playedAt: {
          gte: today,
          lt: tomorrow
        }
      }
    });

    res.json({
      success: true,
      data: {
        totalGames,
        completedGames,
        todayGames,
        completionRate: totalGames > 0 ? (completedGames / totalGames * 100).toFixed(2) : 0,
        roomStats,
        gameTypeStats
      }
    });

  } catch (error) {
    logger.error('獲取展覽遊戲統計失敗:', error);
    res.status(500).json({ error: '獲取統計數據失敗' });
  }
};

// 刪除遊戲分數記錄
export const deleteGameScore = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    await prisma.gameScore.delete({
      where: { id: parseInt(id) }
    });

    logger.info(`遊戲分數記錄 ${id} 已刪除`);

    res.json({
      success: true,
      message: '遊戲分數記錄已刪除'
    });

  } catch (error) {
    logger.error('刪除遊戲分數記錄失敗:', error);
    res.status(500).json({ error: '刪除遊戲分數記錄失敗' });
  }
};
