import {Request,Response} from "express";
import { $Enums, PrismaClient, Exhibition } from '@prisma/client';

const prisma = new PrismaClient({
    log: [ 'info', 'warn', 'error'],
  });

export const createExhibition = async (req:Request, res:Response)=>{
    try{
        const exhibition = await prisma.exhibition.create({
            data: {
              name        : req.body.name,
              description : req.body.description,
              created_by  : req.auth!.username,  // 使用者帳號作為創建者
              pid         : parseInt(req.body.pid)
            },
          });
        res.status(200).json(exhibition);
    }catch(e){
        res.status(500).json({ error : e });
    }
}

export const getExhibition = async (req:Request, res:Response)=>{
    try{
        const { id } = req.params;
        const exhibition = await prisma.exhibition.findUnique({
            where:{
                id: parseInt(id)
            }
        });
        res.status(200).json(exhibition);
    }catch(e){
        res.status(500).json({ error : e });
    }
}

export const getExhibitions = async(req:Request, res:Response)=>{
    const {where, select,include, ... others} = req.query;
    const whereCondition = where ? JSON.parse(decodeURIComponent(where.toString())) : undefined;
    const selectCondition = select ? JSON.parse(decodeURIComponent(select.toString())) : undefined;
    try{
        let exhibitions = await prisma.exhibition.findMany(
            {
                where: whereCondition,
                select: selectCondition,
            }
        );
        res.status(200).json({items:exhibitions});
    }catch(e){
        res.status(500).json({ error : e });
    }
}

export const updateExhibition = async (req:Request, res:Response)=>{
    try{
        const exhibition = await prisma.exhibition.update({
            where:{
                id: parseInt(req.params.id)
            },
            data:req.body
        });
        res.status(200).json(exhibition);
    }catch(e){
        res.status(500).json({ error : e });
    }
}

export const deleteExhibition = async (req:Request, res:Response)=>{
    try{
        const exhibition = await prisma.exhibition.delete({
            where:{
                 id :  parseInt(req.params.id)
            }
        });
        res.status(200).json(exhibition);
    }catch(e){
        res.status(500).json({ error : e });
    }
}

export const getExhibitionVideos = async (req:Request, res:Response)=> {
    try{   
        const videos = await prisma.video.findMany({
            where:{
                eid: parseInt(req.params.id)
            }
        });
        res.status(200).json(videos);
    }catch(e){
        res.status(500).json({ error : e });
    }
}

export const getExhibitionPermissions = async(req:Request, res:Response)=>{
    try{
        // const permissions = await prisma.permission.findMany({
        //     where:{
        //         pid: parseInt(req.params.id)
        //     }
        // });
        // res.status(200).json(permissions);
        res.status(500).json({error:"Not allow"})
    }catch(e){
        res.status(500).json({ error : e});
    }
}