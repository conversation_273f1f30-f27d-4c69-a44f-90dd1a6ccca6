import {Request,Response} from "express";
import { PrismaClient } from '@prisma/client';

interface AuthRequest extends Request {
    auth: {
      username: string;
    };
  }

const prisma = new PrismaClient({
    log: [ 'info', 'warn', 'error'],
  });
  
export const createMemberGroup = async (req:Request, res:Response)=>{
    
    try{
        const memberGroup = await prisma.memberGroup.create({
            data: req.body
        });
        const member = await prisma.member.create({
            data:{
                account: req.auth!.username,
                gid    : memberGroup.id
            }
        });
        res.status(200).json(memberGroup);
    }catch(e){
        res.status(500).json({ error : e });
    }
}

export const getMemberGroup = async (req:Request, res:Response)=>{
   
    try{
        const { id } = req.params;
        const memberGroup = await prisma.memberGroup.findUnique({
            where:{
                id: parseInt(id)
            }
        });
        res.status(200).json(memberGroup);
    }catch(e){
        res.status(500).json({ error : e });
    }
}

export const getMemberGroups = async (req:Request, res:Response)=>{
    const {where, select, ... others} = req.query;
    const whereCondition = where ? JSON.parse(decodeURIComponent(where.toString())) : null;
    const selectCondition = select ? JSON.parse(decodeURIComponent(select.toString())) : null;
    try{
        const memberGroups = await prisma.memberGroup.findMany({
            where: whereCondition,
            select: selectCondition
        });
        res.status(200).json({items: memberGroups});
    }catch(e){
        res.status(500).json({ error : e });
    }
}

export const updateMemberGroup = async (req:Request, res:Response)=>{
    try{
        const memberGroup = await prisma.memberGroup.update({
            where:{
                id: parseInt(req.params.id)
            },
            data:req.body
        });
        res.status(200).json(memberGroup);
    }catch(e){
        res.status(500).json({ error : e });
    }
}

export const deleteMemberGroup = async (req:Request, res:Response)=>{
    try{
        const memberGroup = await prisma.memberGroup.delete({
            where:{
                 id: parseInt(req.params.id)
            }
        });
        res.status(200).json(memberGroup);
    }catch(e){
        res.status(500).json({ error : e });
    }
}

export const getMemberGroupPermissions = async(req:Request, res:Response)=> {
    try{
        const permissions = await prisma.permission.findMany({
            where:{
                id: parseInt(req.params.id)
            }
        });
        res.status(200).json({items: permissions});
    }catch(e){
        res.status(500).json(e);
        }
    }

export const getMemberGroupMembers = async(req:Request, res:Response) =>{
    try{
        var members = await prisma.member.findMany({
            where:{
                gid: parseInt(req.params.id)
            },
            include:{
                user:true
            }
        });
        var result = members.map(member => ({
            ...member,
            ...member.user ? { ...member.user } : null, // 可根据需要修改user的数据结构
            user:undefined
        }));
        res.status(200).json({items: result});
    }catch(e)
    {
        res.status(500).json(e);
    }
}