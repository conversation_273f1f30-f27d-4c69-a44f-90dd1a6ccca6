import {Request,Response} from "express";
import { PrismaClient } from '@prisma/client';

export const prisma = new PrismaClient({
    log: [ 'info', 'warn', 'error'],
  });

export const createMember = async (req:Request, res:Response) =>{
    
    try{
        const { account, gid} = req.body;
        const member = await prisma.member.create({
            data:{
                account: account,
                gid: gid
            }
        });
        res.status(200).json(member);
    }catch(e){
        res.status(500).json({ error : e });
    }
}

export const getMember = async (req:Request, res:Response) =>{
   
    try{
        const { id } = req.params;
        const member = await prisma.member.findUnique({
            where:{
                id: parseInt(id)
            }
        });
        res.status(200).json(member);
    }catch(e){
        res.status(500).json({ error : e });
    }
}

export const getMembers = async(req:Request,res:Response) =>{
    const {where, select, ... others} = req.query;
    const whereCondition = where ? JSON.parse(decodeURIComponent(where.toString())) : null;
    const selectCondition = select ? JSON.parse(decodeURIComponent(select.toString())) : null;
    try{
        const members = await prisma.member.findMany({
            where: whereCondition,
            select: selectCondition
        });
        res.status(200).json({items: members});
    }catch(e){
        res.status(500).json({ error : e });
    }
}

export const updateMember = async (req:Request, res:Response) =>{
    try{
        const member = await prisma.member.update({
            where:{
                id: parseInt(req.params.id)
            },
            data:req.body
        });
        res.status(200).json(member);
    }catch(e){
        res.status(500).json({ error : e });
    }
}

export const deleteMember = async (req:Request, res:Response) =>{
    try{
        const { id } = req.params;
        const member = await prisma.member.delete({
            where:{
                 id: parseInt(id)
            }
        });
        res.status(200).json(member);
    }catch(e){
        res.status(500).json({ error : e });
    }
}