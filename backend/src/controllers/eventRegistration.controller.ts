import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import QRCode from 'qrcode';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '../winston/logger';

const prisma = new PrismaClient({
  log: ['info', 'warn', 'error'],
});

// 創建報名記錄
export const createRegistration = async (req: Request, res: Response) => {
  try {
    const { exhibitionId, userName, userPhone, userEmail } = req.body;

    // 檢查展覽是否存在
    const exhibition = await prisma.exhibition.findUnique({
      where: { id: exhibitionId }
    });

    if (!exhibition) {
      return res.status(404).json({ error: '展覽不存在' });
    }

    // 生成唯一的 QR Code 字串
    const qrCodeData = `${exhibitionId}-${uuidv4()}`;

    // 創建報名記錄
    const registration = await prisma.eventRegistration.create({
      data: {
        exhibitionId,
        userName,
        userPhone,
        userEmail,
        qrCode: qrCodeData,
        status: 'REGISTERED'
      }
    });

    // 生成 QR Code 圖片 (Base64)
    const qrCodeImage = await QRCode.toDataURL(qrCodeData);

    logger.info(`新報名記錄創建: ${registration.id}, 用戶: ${userName}`);

    res.status(201).json({
      success: true,
      data: {
        registrationId: registration.id,
        qrCode: qrCodeData,
        qrCodeImage,
        userName,
        exhibitionName: exhibition.name,
        registeredAt: registration.registeredAt
      }
    });

  } catch (error) {
    logger.error('創建報名記錄失敗:', error);
    res.status(500).json({ error: '創建報名記錄失敗' });
  }
};

// 獲取報名記錄
export const getRegistration = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const registration = await prisma.eventRegistration.findUnique({
      where: { id: parseInt(id) },
      include: {
        exhibition: true,
        checkIns: {
          orderBy: { checkInTime: 'desc' }
        },
        gameScores: {
          orderBy: { playedAt: 'desc' }
        }
      }
    });

    if (!registration) {
      return res.status(404).json({ error: '報名記錄不存在' });
    }

    res.json({
      success: true,
      data: registration
    });

  } catch (error) {
    logger.error('獲取報名記錄失敗:', error);
    res.status(500).json({ error: '獲取報名記錄失敗' });
  }
};

// 通過 QR Code 獲取報名記錄
export const getRegistrationByQRCode = async (req: Request, res: Response) => {
  try {
    const { qrCode } = req.params;

    const registration = await prisma.eventRegistration.findUnique({
      where: { qrCode },
      include: {
        exhibition: true,
        checkIns: {
          orderBy: { checkInTime: 'desc' }
        },
        gameScores: {
          orderBy: { playedAt: 'desc' }
        }
      }
    });

    if (!registration) {
      return res.status(404).json({ error: 'QR Code 無效或報名記錄不存在' });
    }

    res.json({
      success: true,
      data: registration
    });

  } catch (error) {
    logger.error('通過 QR Code 獲取報名記錄失敗:', error);
    res.status(500).json({ error: '獲取報名記錄失敗' });
  }
};

// 獲取展覽的所有報名記錄
export const getRegistrationsByExhibition = async (req: Request, res: Response) => {
  try {
    const { exhibitionId } = req.params;
    const { status } = req.query;

    const whereCondition: any = {
      exhibitionId: parseInt(exhibitionId)
    };

    if (status) {
      whereCondition.status = status;
    }

    const registrations = await prisma.eventRegistration.findMany({
      where: whereCondition,
      include: {
        checkIns: {
          orderBy: { checkInTime: 'desc' },
          take: 1 // 只取最新的簽到記錄
        },
        gameScores: {
          orderBy: { playedAt: 'desc' },
          take: 5 // 只取最近5次遊戲記錄
        }
      },
      orderBy: { registeredAt: 'desc' }
    });

    res.json({
      success: true,
      data: registrations,
      total: registrations.length
    });

  } catch (error) {
    logger.error('獲取展覽報名記錄失敗:', error);
    res.status(500).json({ error: '獲取報名記錄失敗' });
  }
};

// 更新報名狀態
export const updateRegistrationStatus = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    const registration = await prisma.eventRegistration.update({
      where: { id: parseInt(id) },
      data: { status }
    });

    logger.info(`報名記錄 ${id} 狀態更新為: ${status}`);

    res.json({
      success: true,
      data: registration
    });

  } catch (error) {
    logger.error('更新報名狀態失敗:', error);
    res.status(500).json({ error: '更新報名狀態失敗' });
  }
};

// 刪除報名記錄
export const deleteRegistration = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    await prisma.eventRegistration.delete({
      where: { id: parseInt(id) }
    });

    logger.info(`報名記錄 ${id} 已刪除`);

    res.json({
      success: true,
      message: '報名記錄已刪除'
    });

  } catch (error) {
    logger.error('刪除報名記錄失敗:', error);
    res.status(500).json({ error: '刪除報名記錄失敗' });
  }
};

// 獲取報名統計數據
export const getRegistrationStats = async (req: Request, res: Response) => {
  try {
    const { exhibitionId } = req.params;

    const stats = await prisma.eventRegistration.groupBy({
      by: ['status'],
      where: {
        exhibitionId: parseInt(exhibitionId)
      },
      _count: {
        id: true
      }
    });

    const totalRegistrations = await prisma.eventRegistration.count({
      where: {
        exhibitionId: parseInt(exhibitionId)
      }
    });

    const totalCheckIns = await prisma.checkIn.count({
      where: {
        registration: {
          exhibitionId: parseInt(exhibitionId)
        }
      }
    });

    const totalGamePlays = await prisma.gameScore.count({
      where: {
        registration: {
          exhibitionId: parseInt(exhibitionId)
        }
      }
    });

    res.json({
      success: true,
      data: {
        totalRegistrations,
        totalCheckIns,
        totalGamePlays,
        statusBreakdown: stats
      }
    });

  } catch (error) {
    logger.error('獲取報名統計失敗:', error);
    res.status(500).json({ error: '獲取統計數據失敗' });
  }
};
