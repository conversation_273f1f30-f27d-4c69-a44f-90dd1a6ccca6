import  { S3Client, PutObjectCommand,GetObjectCommand,DeleteObjectCommand, ListObjectsV2Command } from '@aws-sdk/client-s3';
import { Readable } from 'stream';

import dotenv from 'dotenv';
import { logger } from '../winston/logger';

dotenv.config();

const credential = {
  accessKeyId: process.env.AWS_ACCESS_KEY_ID ?? "",
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY?? "",
};

const region = process.env.AWS_REGION;
if (!region) {
  throw new Error("AWS_REGION environment variable is missing");
}

const s3Client = new S3Client({ region: region!,credentials: credential });

type StreamingBlobPayloadInputTypes = string | Uint8Array | Buffer | Readable;

export const addObject = async (key: string, body: StreamingBlobPayloadInputTypes) => {
  try {
    const s3Upload = new PutObjectCommand({
        Bucket: process.env.BUCKET_NAME,
        Key: key,
        Body: body 
    });

    s3Client.send(s3Upload).then(
      (data)=>logger.info(data),
      (error)=>console.error(error)
    );
  } catch (err) {
    logger.error(err);
  }
};

// 讀取物件
export const getObject = async (key:string) => {
  try {
    const streamToString = (stream:Readable) =>
      new Promise((resolve, reject) => {
        const chunks:Buffer[] = [];
        stream.on("data", (chunk) => chunks.push(chunk));
        stream.on("error", ()=>{
          console.error("讀取錯誤");
          reject();
        });
        stream.on("end", () => resolve(Buffer.concat(chunks)));
      });
  
    const command = new GetObjectCommand({
      Bucket: process.env.BUCKET_NAME,
      Key: key,
    });
  
    const { Body } = await s3Client.send(command);
    const bodyContents = await streamToString(Body as Readable);
    return bodyContents;
  } catch (err) {
    logger.error(err);
  }
};

// 刪除物件
export const deleteObject = async (key:string) => {
  try {
    const deleteObjectCommand = new DeleteObjectCommand({
      Bucket: process.env.BUCKET_NAME,
      Key: key,
    });

    const deleteObjectResponse = await s3Client.send(deleteObjectCommand);
    return deleteObjectResponse;
  } catch (err) {
    logger.error(err);
  }
};

export const listObjects = async()=>{
  const listObjectsCommand = new ListObjectsV2Command({
    Bucket: process.env.BUCKET_NAME
  });
  try{
    const listObjectsResponse = await s3Client.send(listObjectsCommand);
    return listObjectsResponse.Contents;
  }catch(err){
    logger.error(err);
  }
}