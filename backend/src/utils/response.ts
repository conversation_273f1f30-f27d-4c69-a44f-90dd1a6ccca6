import { Response } from 'express';

// 修復：建立統一的 API 回應格式
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export class ResponseHelper {
  static success<T>(res: Response, data: T, message?: string): Response {
    const response: ApiResponse<T> = {
      success: true,
      data,
      message
    };
    return res.status(200).json(response);
  }

  static successList<T>(res: Response, items: T[], message?: string): Response {
    const response: ApiResponse<{items: T[]}> = {
      success: true,
      data: { items },
      message
    };
    return res.status(200).json(response);
  }

  static error(res: Response, error: string, statusCode = 500): Response {
    const response: ApiResponse = {
      success: false,
      error
    };
    return res.status(statusCode).json(response);
  }

  static badRequest(res: Response, error: string): Response {
    return this.error(res, error, 400);
  }

  static unauthorized(res: Response, error = 'Unauthorized'): Response {
    return this.error(res, error, 401);
  }

  static forbidden(res: Response, error = 'Forbidden'): Response {
    return this.error(res, error, 403);
  }

  static notFound(res: Response, error = 'Not found'): Response {
    return this.error(res, error, 404);
  }
} 