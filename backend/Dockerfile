FROM node:24-alpine

# 安裝必要的系統依賴
RUN apk add --no-cache \
  python3 \
  make \
  g++ \
  curl \
  mysql-client

# 設置工作目錄
WORKDIR /app

# 複製 package.json 文件
COPY backend/package*.json ./backend/
COPY prisma ./prisma/

# 安裝依賴
RUN cd backend && npm install

# 複製源代碼
COPY backend ./backend/

# 生成 Prisma 客戶端
RUN npx prisma generate

# 暴露端口
EXPOSE 3062

# 健康檢查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3062/health || exit 1

# 啟動命令
CMD ["npm", "run", "docker:dev"]
