apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
    jsonData:
      timeInterval: "5s"
      queryTimeout: "60s"
      httpMethod: "POST"
    secureJsonData: {}

  - name: IAMM-API
    type: grafana-simple-json-datasource
    access: proxy
    url: http://host.docker.internal:3061/api/v1
    isDefault: false
    editable: true
    jsonData:
      timeInterval: "10s"
    basicAuth: false
    withCredentials: false
