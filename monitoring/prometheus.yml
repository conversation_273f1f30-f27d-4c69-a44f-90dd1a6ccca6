global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # IAMM 應用程式監控
  - job_name: 'iamm-app'
    static_configs:
      - targets: ['host.docker.internal:3061']
    metrics_path: '/metrics'
    scrape_interval: 10s
    scrape_timeout: 5s

  # Prometheus 自身監控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Node Exporter 系統監控
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

  # 可選：如果有其他服務需要監控
  # - job_name: 'mysql'
  #   static_configs:
  #     - targets: ['host.docker.internal:3306']

# 告警規則 (可選)
# alerting:
#   alertmanagers:
#     - static_configs:
#         - targets:
#           # - alertmanager:9093

# 規則文件 (可選)
# rule_files:
#   - "alert_rules.yml"
