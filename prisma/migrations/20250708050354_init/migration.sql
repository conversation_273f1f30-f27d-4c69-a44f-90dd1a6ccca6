-- CreateTable
CREATE TABLE `event_registration` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `exhibitionId` INTEGER NOT NULL,
    `userName` VARCHAR(191) NOT NULL,
    `userPhone` VARCHAR(191) NULL,
    `userEmail` VARCHAR(191) NULL,
    `qrCode` VARCHAR(191) NOT NULL,
    `status` ENUM('REGISTERED', 'CHECKED_IN', 'COMPLETED', 'CANCELLED') NOT NULL DEFAULT 'REGISTERED',
    `registeredAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    UNIQUE INDEX `event_registration_qrCode_key`(`qrCode`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `check_in` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `registrationId` INTEGER NOT NULL,
    `roomId` VARCHAR(191) NOT NULL,
    `roomName` VARCHAR(191) NOT NULL,
    `checkInTime` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `ipAddress` VARCHAR(191) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `game_score` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `registrationId` INTEGER NOT NULL,
    `roomId` VARCHAR(191) NOT NULL,
    `gameType` ENUM('DART', 'CLAW_MACHINE', 'VENDING') NOT NULL DEFAULT 'DART',
    `score` INTEGER NOT NULL,
    `maxScore` INTEGER NULL,
    `playTime` INTEGER NULL,
    `isCompleted` BOOLEAN NOT NULL DEFAULT false,
    `playedAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `game_room` (
    `id` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `exhibitionId` INTEGER NOT NULL,
    `gameType` ENUM('DART', 'CLAW_MACHINE', 'VENDING') NOT NULL DEFAULT 'DART',
    `status` ENUM('ACTIVE', 'INACTIVE', 'MAINTENANCE') NOT NULL DEFAULT 'ACTIVE',
    `maxPlayers` INTEGER NOT NULL DEFAULT 1,
    `currentPlayers` INTEGER NOT NULL DEFAULT 0,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `event_registration` ADD CONSTRAINT `event_registration_exhibitionId_fkey` FOREIGN KEY (`exhibitionId`) REFERENCES `Exhibition`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `check_in` ADD CONSTRAINT `check_in_registrationId_fkey` FOREIGN KEY (`registrationId`) REFERENCES `event_registration`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `game_score` ADD CONSTRAINT `game_score_registrationId_fkey` FOREIGN KEY (`registrationId`) REFERENCES `event_registration`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `game_room` ADD CONSTRAINT `game_room_exhibitionId_fkey` FOREIGN KEY (`exhibitionId`) REFERENCES `Exhibition`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
