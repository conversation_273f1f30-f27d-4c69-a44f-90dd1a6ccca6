-- AlterTable
ALTER TABLE `User` ADD COLUMN `role` ENUM('USER', 'ADMIN') NOT NULL DEFAULT 'USER';

-- CreateTable
CREATE TABLE `ProjectManager` (
    `account` VARCHAR(191) NOT NULL,
    `pid` INTEGER NOT NULL,

    PRIMARY KEY (`account`, `pid`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `ProjectManager` ADD CONSTRAINT `ProjectManager_pid_fkey` FOREIGN KEY (`pid`) REFERENCES `Project`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ProjectManager` ADD CONSTRAINT `ProjectManager_account_fkey` FOREIGN KEY (`account`) REFERENCES `User`(`account`) ON DELETE RESTRICT ON UPDATE CASCADE;
