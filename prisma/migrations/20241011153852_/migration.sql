/*
  Warnings:

  - The values [CREATE] on the enum `Permission_permission` will be removed. If these variants are still used in the database, this will fail.
  - The primary key for the `ProjectManager` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `pid` on the `ProjectManager` table. All the data in the column will be lost.
  - You are about to drop the column `device` on the `video_play_stats` table. All the data in the column will be lost.
  - You are about to drop the column `ipAddress` on the `video_play_stats` table. All the data in the column will be lost.
  - Added the required column `id` to the `ProjectManager` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE `ProjectManager` DROP FOREIGN KEY `ProjectManager_pid_fkey`;

-- AlterTable
ALTER TABLE `Permission` MODIFY `permission` ENUM('ALL', 'VIEW', 'EDIT', 'DELETE') NOT NULL DEFAULT 'ALL';

-- AlterTable
ALTER TABLE `ProjectManager` DROP PRIMARY KEY,
    DROP COLUMN `pid`,
    ADD COLUMN `id` INTEGER NOT NULL,
    ADD PRIMARY KEY (`account`, `id`);

-- AlterTable
ALTER TABLE `video_play_stats` DROP COLUMN `device`,
    DROP COLUMN `ipAddress`;

-- AddForeignKey
ALTER TABLE `ProjectManager` ADD CONSTRAINT `ProjectManager_id_fkey` FOREIGN KEY (`id`) REFERENCES `Project`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
