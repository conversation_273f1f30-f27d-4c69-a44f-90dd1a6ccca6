/*
  Warnings:

  - You are about to drop the column `manager` on the `MemberGroup` table. All the data in the column will be lost.
  - You are about to drop the column `account` on the `Permission` table. All the data in the column will be lost.
  - You are about to drop the column `create_proj` on the `Permission` table. All the data in the column will be lost.
  - You are about to drop the column `delete_proj` on the `Permission` table. All the data in the column will be lost.
  - You are about to drop the column `read_proj` on the `Permission` table. All the data in the column will be lost.
  - You are about to drop the column `update_proj` on the `Permission` table. All the data in the column will be lost.
  - You are about to alter the column `status` on the `Video` table. The data in that column could be lost. The data in that column will be cast from `VarChar(191)` to `Enum(EnumId(0))`.

*/
-- AlterTable
ALTER TABLE `MemberGroup` DROP COLUMN `manager`;

-- AlterTable
ALTER TABLE `Permission` DROP COLUMN `account`,
    DROP COLUMN `create_proj`,
    DROP COLUMN `delete_proj`,
    DROP COLUMN `read_proj`,
    DROP COLUMN `update_proj`,
    ADD COLUMN `group_id` INTEGER NULL,
    ADD COLUMN `permission` ENUM('ALL', 'VIEW', 'EDIT', 'DELETE', 'CREATE') NOT NULL DEFAULT 'ALL',
    ADD COLUMN `user_id` VARCHAR(191) NULL;

-- AlterTable
ALTER TABLE `Video` MODIFY `status` ENUM('PENDING', 'APPROVED', 'ACCEPTED') NOT NULL DEFAULT 'PENDING';

-- CreateTable
CREATE TABLE `video_play_stats` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `videoId` INTEGER NOT NULL,
    `playTime` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `device` VARCHAR(255) NULL,
    `ipAddress` VARCHAR(45) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `Video` ADD CONSTRAINT `Video_pid_fkey` FOREIGN KEY (`pid`) REFERENCES `Project`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Member` ADD CONSTRAINT `Member_account_fkey` FOREIGN KEY (`account`) REFERENCES `User`(`account`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Member` ADD CONSTRAINT `Member_gid_fkey` FOREIGN KEY (`gid`) REFERENCES `MemberGroup`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Permission` ADD CONSTRAINT `Permission_pid_fkey` FOREIGN KEY (`pid`) REFERENCES `Project`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Permission` ADD CONSTRAINT `Permission_group_id_fkey` FOREIGN KEY (`group_id`) REFERENCES `MemberGroup`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Permission` ADD CONSTRAINT `Permission_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `User`(`account`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `video_play_stats` ADD CONSTRAINT `video_play_stats_videoId_fkey` FOREIGN KEY (`videoId`) REFERENCES `Video`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
