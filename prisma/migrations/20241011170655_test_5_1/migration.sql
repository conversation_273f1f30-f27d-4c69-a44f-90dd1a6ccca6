-- DropForeignKey
ALTER TABLE `Member` DROP FOREIGN KEY `Member_account_fkey`;

-- DropForeignKey
ALTER TABLE `Member` DROP FOREIGN KEY `Member_gid_fkey`;

-- DropForeignKey
ALTER TABLE `Permission` DROP FOREIGN KEY `Permission_group_id_fkey`;

-- DropForeignKey
ALTER TABLE `Permission` DROP FOREIGN KEY `Permission_user_id_fkey`;

-- DropForeignKey
ALTER TABLE `ProjectManager` DROP FOREIGN KEY `ProjectManager_id_fkey`;

-- DropForeignKey
ALTER TABLE `Video` DROP FOREIGN KEY `Video_pid_fkey`;

-- DropForeignKey
ALTER TABLE `video_play_stats` DROP FOREIGN KEY `video_play_stats_videoId_fkey`;

-- DropIndex
DROP INDEX `Permission_pid_fkey` ON `Permission`;

-- AddForeignKey
ALTER TABLE `Video` ADD CONSTRAINT `Video_pid_fkey` F<PERSON><PERSON><PERSON><PERSON> KEY (`pid`) REFERENCES `Project`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Member` ADD CONSTRAINT `Member_account_fkey` FOREIGN KEY (`account`) REFERENCES `User`(`account`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Member` ADD CONSTRAINT `Member_gid_fkey` FOREIGN KEY (`gid`) REFERENCES `MemberGroup`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ProjectManager` ADD CONSTRAINT `ProjectManager_id_fkey` FOREIGN KEY (`id`) REFERENCES `Project`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Permission` ADD CONSTRAINT `Permission_pid_fkey` FOREIGN KEY (`pid`) REFERENCES `Project`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Permission` ADD CONSTRAINT `Permission_group_id_fkey` FOREIGN KEY (`group_id`) REFERENCES `MemberGroup`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Permission` ADD CONSTRAINT `Permission_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `User`(`account`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `video_play_stats` ADD CONSTRAINT `video_play_stats_videoId_fkey` FOREIGN KEY (`videoId`) REFERENCES `Video`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
