#!/bin/bash

# 修正 Docker 配置問題的腳本
set -e

echo "🔧 修正 Docker 配置問題..."

# 停止所有相關容器
echo "🛑 停止所有 IAMM 容器..."
docker stop iamm-mysql iamm-app iamm-backend 2>/dev/null || echo "容器已停止"
docker rm iamm-mysql iamm-app iamm-backend 2>/dev/null || echo "容器已移除"

# 移除舊網路
echo "🗑️ 移除舊網路..."
docker network rm iamm 2>/dev/null || echo "網路已移除或不存在"

# 清理環境變數緩存
echo "🧹 清理環境..."
unset Y4kJrBxNyxmReh9AhZpvDO5SafsztFL54jEehDSqWjCpYz3ayd61O

# 重新啟動服務
echo "🚀 重新啟動服務..."
npm run docker:start

echo ""
echo "✅ Docker 配置問題已修正！"
