#!/bin/bash

# 測試重建腳本 - 不實際執行，只檢查語法
set -e

echo "🧪 測試 rebuild-docker.sh 腳本..."

# 檢查腳本語法
echo "🔍 檢查腳本語法..."
bash -n scripts/rebuild-docker.sh && echo "✅ 語法檢查通過" || echo "❌ 語法錯誤"

# 檢查必要的指令是否存在
echo ""
echo "🔍 檢查必要指令:"
echo "  npm: $(which npm || echo '❌ 未找到')"
echo "  docker: $(which docker || echo '❌ 未找到')"

# 檢查必要的文件是否存在
echo ""
echo "🔍 檢查必要文件:"
echo "  docker-compose.mysql.yml: $([ -f docker-compose.mysql.yml ] && echo '✅ 存在' || echo '❌ 不存在')"
echo "  docker-compose.app.yml: $([ -f docker-compose.app.yml ] && echo '✅ 存在' || echo '❌ 不存在')"
echo "  package.json: $([ -f package.json ] && echo '✅ 存在' || echo '❌ 不存在')"

# 檢查 package.json 中的指令
echo ""
echo "🔍 檢查 package.json 指令:"
if [ -f package.json ]; then
    echo "  docker:stop: $(grep -q 'docker:stop' package.json && echo '✅ 存在' || echo '❌ 不存在')"
    echo "  docker:start: $(grep -q 'docker:start' package.json && echo '✅ 存在' || echo '❌ 不存在')"
    echo "  update:prisma: $(grep -q 'update:prisma' package.json && echo '✅ 存在' || echo '❌ 不存在')"
    echo "  check:versions: $(grep -q 'check:versions' package.json && echo '✅ 存在' || echo '❌ 不存在')"
fi

echo ""
echo "🎯 建議的安全測試指令:"
echo "  npm run check:versions  # 檢查版本配置"
echo "  npm run docker:restart-simple  # 簡單重啟"
echo "  npm run docker:rebuild  # 完整重建（小心使用）"

echo ""
echo "✅ 測試完成！"
