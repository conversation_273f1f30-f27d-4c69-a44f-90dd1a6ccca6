#!/bin/bash

# IAMM Docker 啟動腳本
set -e

echo "🚀 啟動 IAMM 系統..."

# 檢查 Docker 是否運行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker 未運行，請先啟動 Docker"
    exit 1
fi

# 啟動 MySQL（會自動創建網路）
echo "🗄️ 啟動 MySQL 資料庫..."
docker-compose -f docker-compose.mysql.yml up -d

# 等待 MySQL 啟動
echo "⏳ 等待 MySQL 啟動..."
timeout=60
counter=0
while ! docker exec iamm-mysql mysqladmin ping -h localhost -u root -ppassword --silent; do
    sleep 2
    counter=$((counter + 2))
    if [ $counter -ge $timeout ]; then
        echo "❌ MySQL 啟動超時"
        exit 1
    fi
    echo "等待中... ($counter/$timeout 秒)"
done

echo "✅ MySQL 已啟動"

# 初始化資料庫
echo "🔧 初始化資料庫..."
npm run prisma:generate
npm run prisma:migrate

# 啟動應用程式
echo "🚀 啟動應用程式..."
docker-compose -f docker-compose.app.yml up -d

# 等待應用程式啟動
echo "⏳ 等待應用程式啟動..."
timeout=60
counter=0
while ! curl -f http://localhost:3062/health > /dev/null 2>&1; do
    sleep 2
    counter=$((counter + 2))
    if [ $counter -ge $timeout ]; then
        echo "⚠️ 應用程式啟動超時，請檢查日誌"
        break
    fi
    echo "等待中... ($counter/$timeout 秒)"
done

echo "✅ 應用程式已啟動"

# 顯示狀態
echo ""
echo "📊 系統狀態:"
docker-compose -f docker-compose.mysql.yml ps
docker-compose -f docker-compose.app.yml ps

echo ""
echo "🌐 訪問地址:"
echo "  前端應用: http://localhost:5173"
echo "  後端 API: http://localhost:3062"
echo "  健康檢查: http://localhost:3062/health"
echo "  Prisma Studio: npm run prisma:studio"

echo ""
echo "📝 常用指令:"
echo "  查看日誌: npm run docker:logs"
echo "  停止服務: npm run docker:stop"
echo "  重啟服務: npm run docker:restart"
echo "  啟動監控: npm run docker:monitoring"

echo ""
echo "🎉 IAMM 系統啟動完成！"
