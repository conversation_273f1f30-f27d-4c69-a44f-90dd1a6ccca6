#!/bin/bash

# 檢查所有 Docker 和依賴版本的腳本
set -e

echo "🔍 檢查 IAMM 系統版本配置..."

echo ""
echo "📦 Node.js Docker Images:"
echo "========================"

# 檢查所有 Dockerfile 中的 Node.js 版本
echo "🐳 Dockerfile 檢查:"
find . -name "Dockerfile" -not -path "./node_modules/*" | while read dockerfile; do
    node_version=$(grep "FROM node:" "$dockerfile" | head -1)
    echo "  $dockerfile: $node_version"
done

echo ""
echo "🐳 Docker Compose 檢查:"
find . -name "docker-compose*.yml" -not -path "./node_modules/*" | while read compose_file; do
    echo "  檢查文件: $compose_file"
    grep -n "image: node:" "$compose_file" 2>/dev/null | while read line; do
        echo "    $line"
    done || echo "    無 Node.js image 配置"
done

echo ""
echo "📚 Prisma 版本:"
echo "==============="

# 檢查主 package.json
echo "🔍 主 package.json:"
if [ -f "package.json" ]; then
    prisma_client=$(grep "@prisma/client" package.json | head -1 | sed 's/^[[:space:]]*//')
    prisma_cli=$(grep '"prisma":' package.json | head -1 | sed 's/^[[:space:]]*//')
    echo "  $prisma_client"
    echo "  $prisma_cli"
else
    echo "  ❌ 找不到主 package.json"
fi

# 檢查後端 package.json
echo ""
echo "🔍 Backend package.json:"
if [ -f "backend/package.json" ]; then
    prisma_client=$(grep "@prisma/client" backend/package.json | head -1 | sed 's/^[[:space:]]*//')
    prisma_cli=$(grep '"prisma":' backend/package.json | head -1 | sed 's/^[[:space:]]*//')
    echo "  $prisma_client"
    echo "  $prisma_cli"
else
    echo "  ❌ 找不到 backend/package.json"
fi

echo ""
echo "🎯 預期版本:"
echo "============"
echo "  Node.js Docker Image: node:24-alpine"
echo "  Prisma Client: 6.11.1"
echo "  Prisma CLI: 6.11.1"

echo ""
echo "✅ 版本檢查完成！"

# 檢查是否有版本不一致
echo ""
echo "🔧 建議操作:"
echo "============"
echo "  1. 如果發現版本不一致，請手動更新"
echo "  2. 更新後執行: npm run docker:restart"
echo "  3. 重新生成 Prisma: npm run prisma:generate"
