#!/bin/bash

# Docker 重建腳本 - 確保使用最新的 Node.js 24 和 Prisma 6.11.1
set -e

echo "🔄 重建 IAMM Docker 環境..."

# 停止所有服務
echo "🛑 停止現有服務..."
npm run docker:stop 2>/dev/null || echo "服務已停止"

# 移除舊的 IAMM 容器
echo "🗑️ 移除舊容器..."
docker rm -f iamm-app iamm-backend iamm-mysql 2>/dev/null || echo "無舊容器需要移除"

# 重新拉取 Node.js 24 映像
echo "📥 拉取最新 Node.js 24 映像..."
docker pull node:24-alpine

# 清理未使用的映像（可選）
echo "🧹 清理未使用的映像..."
# docker image prune -f

# 更新 Prisma 依賴
echo "📦 更新 Prisma 依賴..."
npm run update:prisma

# 重新啟動服務
echo "🚀 啟動服務..."
npm run docker:start

echo ""
echo "✅ Docker 環境重建完成！"
echo ""
echo "🔍 檢查版本:"
npm run check:versions
