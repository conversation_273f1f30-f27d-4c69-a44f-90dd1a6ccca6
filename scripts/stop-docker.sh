#!/bin/bash

# IAMM Docker 停止腳本
set -e

echo "🛑 停止 IAMM 系統..."

# 停止應用程式
echo "📱 停止應用程式..."
docker-compose -f docker-compose.app.yml down

# 停止 MySQL
echo "🗄️ 停止 MySQL..."
docker-compose -f docker-compose.mysql.yml down

# 停止監控系統（如果運行中）
echo "📊 停止監控系統..."
docker-compose -f docker-compose.monitoring.yml down 2>/dev/null || echo "監控系統未運行"

echo "✅ IAMM 系統已停止"

# 顯示剩餘容器
echo ""
echo "📊 剩餘 Docker 容器:"
docker ps --filter "name=iamm-*" || echo "無相關容器運行"
