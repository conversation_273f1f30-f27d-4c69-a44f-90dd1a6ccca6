services:
  mysql:
    image: mysql:8.0
    container_name: iamm-mysql
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: iamm
      MYSQL_USER: iamm_user
      MY<PERSON><PERSON>_PASSWORD: iamm_password
      MYSQL_CHARSET: utf8mb4
      MYSQL_COLLATION: utf8mb4_unicode_ci
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
    networks:
      - iamm
    restart: unless-stopped
    command: --default-authentication-plugin=mysql_native_password --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-ppassword"]
      timeout: 20s
      retries: 10
      interval: 10s

volumes:
  mysql_data:
    driver: local

networks:
  iamm:
    driver: bridge
