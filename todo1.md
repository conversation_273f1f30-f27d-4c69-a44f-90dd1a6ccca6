# IAMM Dashboard 修復待辦清單

## 需要立即修復的問題

### 1. JWT Token 驗證邏輯錯誤 🚨 ✅ 已修復
**檔案**: `app/src/user/tokenOps.js`
**問題**: validateToken() 函數邏輯相反，會誤判有效 token 為無效
**影響**: 用戶可能被錯誤踢出登入狀態
**修復**: ✅ 已將 `return decodedToken.exp < now;` 改為 `return decodedToken.exp > now;`
**額外改善**: ✅ 加入了 token 存在性檢查和 try-catch 錯誤處理

### 2. 加密 Salt 產生不安全 🚨 ✅ 已修復
**檔案**: `app/src/utils/crypto.js`
**問題**: generateSalt() 使用 array.toString() 產生逗號分隔字串
**影響**: 安全性低，可預測性高
**修復**: ✅ 改用 hex 編碼：`Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('')`
**額外改善**: ✅ 同時更新了 generateHash 函數以配合新格式

### 3. 時間比較邏輯錯誤 🔧 ✅ 已修復
**檔案**: `backend/src/index.ts`
**問題**: updateProjectsStatus() 中時間格式不一致（12小時制 vs 24小時制）
**影響**: 自動開關展覽功能失效
**修復**: ✅ 統一使用 24 小時制：`date.toTimeString().substring(0, 5)`
**額外改善**: ✅ 移除了不再需要的 convert_hours 函數

### 4. 記憶體洩漏風險 🔧 ✅ 已修復
**檔案**: `backend/src/routes/connections_routes.ts`
**問題**: connectionList 無限累積，沒有清理機制
**影響**: 長期運行後記憶體耗盡
**修復**: ✅ 加入連接清理機制和定時清理
**改善內容**:
- ✅ 加入 lastSeen 時間戳
- ✅ 每分鐘自動清理超時連接（5分鐘超時）
- ✅ 清理空的 hashID 節點

### 5. 資料庫連接問題 🔧 ✅ 已修復
**檔案**: 多個 controller 檔案
**問題**: 重複建立 PrismaClient 實例
**影響**: 資源浪費，可能導致連接池耗盡
**修復**: ✅ 建立統一的 database service 單例
**改善內容**:
- ✅ 創建 `backend/src/utils/database.ts` 單例服務
- ✅ 更新 user.controller.ts 使用統一服務
- ✅ 更新 project.controller.ts 使用統一服務

### 6. API 回應格式不一致 🌐 ✅ 已修復
**檔案**: 多個 controller 檔案
**問題**: 有些回傳 {items: []}，有些直接回傳資料
**影響**: 前端處理困難，容易出錯
**修復**: ✅ 建立統一 API 回應格式工具
**改善內容**:
- ✅ 創建 `backend/src/utils/response.ts` 回應工具類
- ✅ 提供 success, successList, error 等標準方法
- ✅ 統一錯誤狀態碼處理

### 7. 錯誤處理不完整 🌐 ✅ 已修復
**檔案**: `app/src/handle_error.js`
**問題**: 只處理 401 錯誤，其他錯誤處理不當
**影響**: 用戶體驗差，除錯困難
**修復**: ✅ 完善錯誤處理機制
**改善內容**:
- ✅ 處理 401, 403, 404, 500 等常見錯誤
- ✅ 提供友善的中文錯誤訊息
- ✅ 自動解析後端回傳的錯誤訊息
- ✅ 加入異常拋出以中止後續處理

## 修復狀態總覽 ✅ 全部完成
1. **高危**: 問題 1, 2 (安全性) ✅ 完成
2. **中危**: 問題 3, 4, 5 (功能性) ✅ 完成  
3. **低危**: 問題 6, 7 (用戶體驗) ✅ 完成


## 後續建議
1. 對其他 controller 檔案套用統一的 database service 和 response helper
2. 將統一的錯誤處理套用到所有 API 端點
3. 進行整合測試以確保修復正常運作
4. 考慮加入 API 文件生成工具

## 其他發現的潛在問題 (未修復)
8. 架構設計：緊耦合的微服務依賴
9. 權限檢查：專案權限管理邏輯複雜且可能有安全漏洞
10. 效能問題：定時任務頻繁的資料庫查詢
11. 大型檔案：Bundle 檔案過大影響載入速度
12. 外鍵約束：某些資料庫關聯缺少適當的約束
13. 索引優化：缺少查詢優化的索引
14. 併發安全：某些操作可能有競爭條件
15. 日誌機制：需要改善日誌記錄和監控 